/**
 * Test script for photo collection service
 * Run this to test Google Places API integration
 */

import 'dotenv/config';
import { photoCollectionService } from '../src/services/photo-collection.js';

async function testPhotoCollection() {
  console.log('🧪 Testing Photo Collection Service...\n');

  // Test business data
  const testBusiness = {
    id: 999, // Test ID
    name: 'Starbucks',
    address: '123 Main Street, New York, NY',
    latitude: '40.7128',
    longitude: '-74.0060'
  };

  try {
    console.log('📍 Test Business:', testBusiness.name);
    console.log('📍 Address:', testBusiness.address);
    console.log('');

    // Test 1: Find Google Place ID
    console.log('🔍 Step 1: Finding Google Place ID...');
    const placeId = await photoCollectionService.findGooglePlaceId(testBusiness);
    
    if (placeId) {
      console.log('✅ Found Place ID:', placeId);
    } else {
      console.log('❌ Could not find Place ID');
      return;
    }

    // Test 2: Fetch Photos
    console.log('\n📸 Step 2: Fetching photos from Google Places...');
    const photos = await photoCollectionService.fetchGooglePlacesPhotos(placeId);
    
    if (photos.length > 0) {
      console.log(`✅ Found ${photos.length} photos`);
      photos.forEach((photo, index) => {
        console.log(`   Photo ${index + 1}: ${photo.photo_reference.substring(0, 20)}...`);
      });
    } else {
      console.log('❌ No photos found');
      return;
    }

    // Test 3: Get Pending Photos (without actually saving)
    console.log('\n📋 Step 3: Testing pending photos query...');
    const pendingPhotos = await photoCollectionService.getPendingPhotos();
    console.log(`✅ Current pending photos: ${pendingPhotos.length}`);

    console.log('\n🎉 Photo Collection Service Test Complete!');
    console.log('\n📋 Summary:');
    console.log(`   ✅ Google Places API: Working`);
    console.log(`   ✅ Place ID Search: Working`);
    console.log(`   ✅ Photo Fetching: Working`);
    console.log(`   ✅ Database Queries: Working`);
    console.log('\n💡 Ready to collect photos for real businesses!');

  } catch (error) {
    console.error('\n❌ Test Failed:', error.message);
    
    if (error.message.includes('API key')) {
      console.log('\n🔧 Fix: Add your Google Places API key to .env file:');
      console.log('   GOOGLE_PLACES_API_KEY=your_api_key_here');
    }
    
    if (error.message.includes('quota')) {
      console.log('\n🔧 Fix: Check your Google Places API quota and billing');
    }
  }
}

// Run the test
testPhotoCollection();
