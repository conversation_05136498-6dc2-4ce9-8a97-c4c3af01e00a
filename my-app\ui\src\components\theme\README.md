# Theme Components Documentation

## 🎯 Overview

This directory contains a comprehensive set of theme components for the Volo App Template, providing multiple ways to implement light/dark mode switching with full design system integration.

## 📦 Components

### Core Components

#### `ModeToggle`
Enhanced version of the original theme toggle with improved accessibility and animations.

```tsx
import { ModeToggle } from '@/components/theme';

<ModeToggle />
<ModeToggle variant="outline" size="sm" />
```

#### `ModeToggleButton`
Button-style toggle with text and icon options.

```tsx
import { ModeToggleButton, ModeToggleCompact, ModeToggleText } from '@/components/theme';

<ModeToggleButton showText={true} showIcon={true} />
<ModeToggleCompact />
<ModeToggleText />
```

#### `ModeToggleSwitch`
Switch-style toggle using the design system Switch component.

```tsx
import { ModeToggleSwitch, ModeToggleSwitchMinimal } from '@/components/theme';

<ModeToggleSwitch orientation="horizontal" />
<ModeToggleSwitch orientation="vertical" />
<ModeToggleSwitchMinimal />
```

#### `ModeToggleDropdown`
Dropdown menu with Light/Dark/System options.

```tsx
import { ModeToggleDropdown, ModeToggleDropdownDetailed } from '@/components/theme';

<ModeToggleDropdown showSystemOption={true} />
<ModeToggleDropdownDetailed />
```

#### `ThemeSelector`
Multi-option theme selector with various layouts.

```tsx
import { ThemeSelector, ThemeSelectorDetailed } from '@/components/theme';

<ThemeSelector variant="buttons" orientation="horizontal" />
<ThemeSelector variant="radio" />
<ThemeSelector variant="cards" />
<ThemeSelectorDetailed />
```

#### `ThemeSettings`
Advanced theme configuration panel.

```tsx
import { ThemeSettings, ThemeSettingsCompact } from '@/components/theme';

<ThemeSettings showAdvanced={true} />
<ThemeSettingsCompact />
```

### Utility Components

#### `ThemeShowcase`
Demonstration component showing all theme variants.

```tsx
import { ThemeShowcase } from '@/components/theme/ThemeShowcase';

<ThemeShowcase />
```

## 🎣 Hooks

### `useThemePreference`
Enhanced theme hook with additional features.

```tsx
import { useThemePreference } from '@/components/theme';

const {
  theme,
  setTheme,
  resolvedTheme,
  displayName,
  isSystem,
  toggleTheme,
  preferences,
  updatePreferences,
  mounted
} = useThemePreference();
```

## 🛠️ Utilities

### Theme Utilities
```tsx
import { 
  getTransitionClasses, 
  getThemeAriaLabel, 
  themeColors,
  themeTransitions 
} from '@/components/theme';

const transitionClass = getTransitionClasses('button');
const ariaLabel = getThemeAriaLabel('light');
```

## 🎨 Design System Integration

All components use design system tokens:

- **Colors**: `text-primary`, `bg-background`, `text-muted-foreground`
- **Typography**: `h1`, `h2`, `body-text`, `label`, `caption`
- **Spacing**: Design system spacing scale
- **Shadows**: Consistent elevation system
- **Transitions**: Smooth animations with reduced motion support

## 📱 Responsive Behavior

Components adapt to different screen sizes:

- **Desktop**: Full-featured toggles with text and icons
- **Mobile**: Compact toggles optimized for touch
- **Tablet**: Balanced approach with appropriate sizing

## ♿ Accessibility Features

- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard support
- **Focus Management**: Visible focus indicators
- **Reduced Motion**: Respects user motion preferences
- **High Contrast**: Support for high contrast modes

## 🔧 Usage Examples

### Header Navigation
```tsx
import { ModeToggleDropdown } from '@/components/theme';

<header className="flex items-center justify-between">
  <Logo />
  <nav className="flex items-center gap-4">
    <NavLinks />
    <ModeToggleDropdown />
  </nav>
</header>
```

### Mobile Navigation
```tsx
import { ThemeSelector } from '@/components/theme';

<MobileMenu>
  <div className="border-t pt-4">
    <h4 className="h5 mb-3">Appearance</h4>
    <ThemeSelector variant="buttons" orientation="vertical" size="sm" />
  </div>
</MobileMenu>
```

### Settings Page
```tsx
import { ThemeSettings } from '@/components/theme';

<SettingsPage>
  <ThemeSettings showAdvanced={true} />
</SettingsPage>
```

### Quick Toggle
```tsx
import { ModeToggle } from '@/components/theme';

<Toolbar>
  <ModeToggle />
</Toolbar>
```

## 🎯 Best Practices

1. **Choose the Right Component**
   - Use `ModeToggle` for simple icon-only toggles
   - Use `ModeToggleButton` for buttons with text
   - Use `ThemeSelector` for comprehensive theme selection
   - Use `ThemeSettings` for advanced configuration

2. **Responsive Design**
   - Use compact variants on mobile
   - Show text labels on desktop when space allows
   - Consider touch target sizes for mobile

3. **Accessibility**
   - Always include proper ARIA labels
   - Test with keyboard navigation
   - Verify screen reader compatibility

4. **Performance**
   - Check `mounted` state to prevent hydration issues
   - Use appropriate transition classes
   - Respect reduced motion preferences

## 🔄 Migration from Original

If migrating from the original `ModeToggle`:

```tsx
// Before
import { ModeToggle } from '@/components/mode-toggle';

// After (enhanced version)
import { ModeToggle } from '@/components/theme';
```

The enhanced version is backward compatible but adds:
- Better accessibility
- Improved animations
- Design system integration
- Additional props for customization

## 🧪 Testing

Use the `ThemeShowcase` component to test all variants:

```tsx
import { ThemeShowcase } from '@/components/theme/ThemeShowcase';

// In your development/testing environment
<ThemeShowcase />
```

## 📚 Related Documentation

- [Design System Guide](../../design/README.md)
- [Theme Utils](../../lib/theme-utils.ts)
- [Theme Hook](../../hooks/useThemePreference.ts)

---

**Last Updated**: 2025-01-08
**Version**: 1.0
**Maintainer**: Volo App Template Team
