import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock } from 'lucide-react';

interface BusinessHour {
  id: number;
  day_of_week: string;
  open_time: string;
  close_time: string;
  is_closed: boolean;
}

interface BusinessHoursProps {
  hours: BusinessHour[];
}

const dayOrder = [
  'monday',
  'tuesday', 
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday'
];

const dayNames = {
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
  sunday: 'Sunday'
};

export function BusinessHours({ hours }: BusinessHoursProps) {
  // Sort hours by day of week
  const sortedHours = hours.sort((a, b) => {
    return dayOrder.indexOf(a.day_of_week) - dayOrder.indexOf(b.day_of_week);
  });

  // Get current day to highlight
  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

  // Check if currently open
  const getCurrentStatus = () => {
    const now = new Date();
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
    
    const todayHours = hours.find(h => h.day_of_week === currentDay);
    
    if (!todayHours || todayHours.is_closed) {
      return { isOpen: false, status: 'Closed' };
    }
    
    if (currentTime >= todayHours.open_time && currentTime <= todayHours.close_time) {
      return { isOpen: true, status: 'Open Now' };
    }
    
    return { isOpen: false, status: 'Closed' };
  };

  const { isOpen, status } = getCurrentStatus();

  const formatTime = (time: string) => {
    if (!time) return '';
    
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    
    return `${displayHour}:${minutes} ${ampm}`;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Hours
          </CardTitle>
          <Badge variant={isOpen ? "default" : "secondary"}>
            {status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {sortedHours.map((hour) => {
            const isToday = hour.day_of_week === today;
            
            return (
              <div 
                key={hour.id}
                className={`flex justify-between items-center py-1 ${
                  isToday ? 'font-semibold text-primary' : 'text-muted-foreground'
                }`}
              >
                <span className="text-sm">
                  {dayNames[hour.day_of_week as keyof typeof dayNames]}
                </span>
                <span className="text-sm">
                  {hour.is_closed ? (
                    'Closed'
                  ) : (
                    `${formatTime(hour.open_time)} - ${formatTime(hour.close_time)}`
                  )}
                </span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
