/**
 * Mode Toggle Switch Component
 * 
 * A switch-style theme toggle using the design system
 */

import * as React from 'react';
import { Sun, Moon } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useThemePreference } from '@/hooks/useThemePreference';
import { getTransitionClasses } from '@/lib/theme-utils';

interface ModeToggleSwitchProps {
  showLabels?: boolean;
  showIcons?: boolean;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  id?: string;
}

export function ModeToggleSwitch({
  showLabels = true,
  showIcons = true,
  orientation = 'horizontal',
  size = 'md',
  className,
  id = 'theme-switch',
  ...props
}: ModeToggleSwitchProps) {
  const { 
    resolvedTheme, 
    setTheme, 
    mounted,
    displayName 
  } = useThemePreference();

  // Show loading state during hydration
  if (!mounted) {
    return (
      <div className={cn(
        'flex items-center gap-3',
        orientation === 'vertical' && 'flex-col',
        className
      )}>
        {showIcons && <Sun className="h-4 w-4 text-muted-foreground" />}
        <Switch disabled />
        {showIcons && <Moon className="h-4 w-4 text-muted-foreground" />}
      </div>
    );
  }

  const isDark = resolvedTheme === 'dark';

  const handleCheckedChange = (checked: boolean) => {
    setTheme(checked ? 'dark' : 'light');
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'gap-2',
          icon: 'h-3 w-3',
          label: 'text-xs'
        };
      case 'lg':
        return {
          container: 'gap-4',
          icon: 'h-5 w-5',
          label: 'text-base'
        };
      default:
        return {
          container: 'gap-3',
          icon: 'h-4 w-4',
          label: 'text-sm'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  if (orientation === 'vertical') {
    return (
      <div className={cn(
        'flex flex-col items-center',
        sizeClasses.container,
        className
      )}>
        {showIcons && (
          <Sun className={cn(
            sizeClasses.icon,
            'text-muted-foreground transition-colors',
            !isDark && 'text-primary'
          )} />
        )}
        
        <Switch
          id={id}
          checked={isDark}
          onCheckedChange={handleCheckedChange}
          aria-label="Toggle dark mode"
          className={getTransitionClasses('normal')}
          {...props}
        />
        
        {showIcons && (
          <Moon className={cn(
            sizeClasses.icon,
            'text-muted-foreground transition-colors',
            isDark && 'text-primary'
          )} />
        )}
        
        {showLabels && (
          <Label 
            htmlFor={id}
            className={cn(
              'label text-center cursor-pointer',
              sizeClasses.label,
              getTransitionClasses('text')
            )}
          >
            {displayName}
          </Label>
        )}
      </div>
    );
  }

  return (
    <div className={cn(
      'flex items-center',
      sizeClasses.container,
      className
    )}>
      {showIcons && (
        <Sun className={cn(
          sizeClasses.icon,
          'text-muted-foreground transition-colors',
          !isDark && 'text-primary'
        )} />
      )}
      
      <div className="flex items-center gap-2">
        <Switch
          id={id}
          checked={isDark}
          onCheckedChange={handleCheckedChange}
          aria-label="Toggle dark mode"
          className={getTransitionClasses('normal')}
          {...props}
        />
        
        {showLabels && (
          <Label 
            htmlFor={id}
            className={cn(
              'label cursor-pointer',
              sizeClasses.label,
              getTransitionClasses('text')
            )}
          >
            {displayName}
          </Label>
        )}
      </div>
      
      {showIcons && (
        <Moon className={cn(
          sizeClasses.icon,
          'text-muted-foreground transition-colors',
          isDark && 'text-primary'
        )} />
      )}
    </div>
  );
}

/**
 * Minimal Mode Toggle Switch
 * 
 * A clean switch without icons or labels
 */
export function ModeToggleSwitchMinimal({
  className,
  ...props
}: Omit<ModeToggleSwitchProps, 'showLabels' | 'showIcons'>) {
  return (
    <ModeToggleSwitch
      showLabels={false}
      showIcons={false}
      className={className}
      {...props}
    />
  );
}

/**
 * Mode Toggle Switch with Custom Labels
 * 
 * Switch with custom light/dark labels
 */
interface ModeToggleSwitchCustomProps extends Omit<ModeToggleSwitchProps, 'showLabels'> {
  lightLabel?: string;
  darkLabel?: string;
}

export function ModeToggleSwitchCustom({
  lightLabel = 'Light',
  darkLabel = 'Dark',
  className,
  ...props
}: ModeToggleSwitchCustomProps) {
  const { resolvedTheme, setTheme, mounted } = useThemePreference();

  if (!mounted) {
    return <ModeToggleSwitch className={className} {...props} />;
  }

  const isDark = resolvedTheme === 'dark';

  return (
    <div className={cn('flex items-center gap-3', className)}>
      <Label 
        className={cn(
          'label cursor-pointer transition-colors',
          !isDark ? 'text-foreground' : 'text-muted-foreground'
        )}
        onClick={() => setTheme('light')}
      >
        {lightLabel}
      </Label>
      
      <Switch
        checked={isDark}
        onCheckedChange={(checked) => setTheme(checked ? 'dark' : 'light')}
        aria-label="Toggle between light and dark mode"
        className={getTransitionClasses('normal')}
      />
      
      <Label 
        className={cn(
          'label cursor-pointer transition-colors',
          isDark ? 'text-foreground' : 'text-muted-foreground'
        )}
        onClick={() => setTheme('dark')}
      >
        {darkLabel}
      </Label>
    </div>
  );
}
