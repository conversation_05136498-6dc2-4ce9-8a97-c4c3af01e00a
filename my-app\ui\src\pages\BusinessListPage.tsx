import { useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { SearchBar } from '@/components/search/SearchBar';
import { BusinessCard } from '@/components/business/BusinessCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useBusinesses } from '@/hooks/useBusinesses';
import { useCategories } from '@/hooks/useCategories';
import { Skeleton } from '@/components/ui/skeleton';
import { Filter, Grid, List, SlidersHorizontal } from 'lucide-react';

type ViewMode = 'grid' | 'list';
type SortBy = 'name' | 'rating' | 'newest' | 'featured';

export function BusinessListPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortBy>('name');
  const [showFilters, setShowFilters] = useState(false);
  
  const category = searchParams.get('category') || '';
  const page = parseInt(searchParams.get('page') || '1');
  const featured = searchParams.get('featured') === 'true';
  
  const { businesses, loading, error, total, totalPages } = useBusinesses({
    page,
    limit: 12,
    category: category || undefined,
    featured: featured || undefined,
  });
  
  const { categories } = useCategories();
  
  const currentCategory = categories?.find(c => c.slug === category);

  const handleSearch = (query: string, location?: string) => {
    const params = new URLSearchParams();
    params.set('q', query);
    if (location) params.set('location', location);
    navigate(`/search?${params.toString()}`);
  };

  const handleCategoryFilter = (categorySlug: string) => {
    const params = new URLSearchParams();
    if (categorySlug) params.set('category', categorySlug);
    if (featured) params.set('featured', 'true');
    setSearchParams(params);
  };

  const handleFeaturedFilter = (showFeatured: boolean) => {
    const params = new URLSearchParams();
    if (category) params.set('category', category);
    if (showFeatured) params.set('featured', 'true');
    setSearchParams(params);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams();
    if (category) params.set('category', category);
    if (featured) params.set('featured', 'true');
    params.set('page', newPage.toString());
    setSearchParams(params);
  };

  const clearFilters = () => {
    setSearchParams(new URLSearchParams());
  };

  const hasActiveFilters = category || featured;

  return (
    <div className="min-h-screen bg-muted/30">
      {/* Header */}
      <div className="bg-background border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold mb-2">
                {currentCategory ? currentCategory.name : 'All Businesses'}
              </h1>
              <p className="text-muted-foreground">
                {currentCategory 
                  ? `Browse ${currentCategory.name.toLowerCase()} businesses in your area`
                  : 'Discover local businesses and services'
                }
              </p>
            </div>
            <SearchBar onSearch={handleSearch} />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <div className={`lg:w-64 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <Card className="sticky top-6">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Filters</h3>
                  {hasActiveFilters && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-xs"
                    >
                      Clear All
                    </Button>
                  )}
                </div>
                
                {/* Featured Filter */}
                <div className="space-y-3 mb-6">
                  <h4 className="text-sm font-medium">Type</h4>
                  <div className="space-y-2">
                    <button
                      onClick={() => handleFeaturedFilter(false)}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors text-sm ${
                        !featured
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      All Businesses
                    </button>
                    <button
                      onClick={() => handleFeaturedFilter(true)}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors text-sm ${
                        featured
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      Featured Only
                    </button>
                  </div>
                </div>

                {/* Category Filter */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Categories</h4>
                  <div className="space-y-2">
                    <button
                      onClick={() => handleCategoryFilter('')}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors text-sm ${
                        !category
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      All Categories
                    </button>
                    {categories?.map((cat) => (
                      <button
                        key={cat.id}
                        onClick={() => handleCategoryFilter(cat.slug)}
                        className={`w-full text-left px-3 py-2 rounded-md transition-colors text-sm ${
                          category === cat.slug
                            ? 'bg-primary text-primary-foreground'
                            : 'hover:bg-muted'
                        }`}
                      >
                        {cat.name}
                        {cat.business_count && (
                          <span className="ml-1 text-xs opacity-70">
                            ({cat.business_count})
                          </span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6">
              <div className="mb-4 sm:mb-0">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>
                    {loading ? 'Loading...' : `${total} businesses found`}
                  </span>
                  {hasActiveFilters && (
                    <div className="flex gap-1">
                      {featured && (
                        <Badge variant="secondary">Featured</Badge>
                      )}
                      {currentCategory && (
                        <Badge variant="outline">{currentCategory.name}</Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Mobile Filter Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </Button>

                {/* Sort */}
                <Select value={sortBy} onValueChange={(value: SortBy) => setSortBy(value)}>
                  <SelectTrigger className="w-40">
                    <SlidersHorizontal className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name A-Z</SelectItem>
                    <SelectItem value="rating">Highest Rated</SelectItem>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="featured">Featured First</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode Toggle */}
                <div className="flex border rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Error State */}
            {error && (
              <Card className="p-6 text-center">
                <p className="text-muted-foreground">{error}</p>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="mt-4"
                >
                  Try Again
                </Button>
              </Card>
            )}

            {/* Loading State */}
            {loading && (
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
                  : 'grid-cols-1'
              }`}>
                {Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <div className="h-48 bg-muted"></div>
                    <CardContent className="p-4">
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-1/2 mb-2" />
                      <Skeleton className="h-3 w-full" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Results Grid/List */}
            {!loading && !error && businesses.length > 0 && (
              <>
                <div className={`grid gap-6 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
                    : 'grid-cols-1'
                }`}>
                  {businesses.map((business) => (
                    <BusinessCard
                      key={business.id}
                      business={business}
                      layout={viewMode === 'list' ? 'horizontal' : 'vertical'}
                      showCategory={!category}
                      category={business.category}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(page - 1)}
                        disabled={page <= 1}
                      >
                        Previous
                      </Button>
                      
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const pageNum = i + 1;
                          return (
                            <Button
                              key={pageNum}
                              variant={page === pageNum ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>
                      
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(page + 1)}
                        disabled={page >= totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}

            {/* No Results */}
            {!loading && !error && businesses.length === 0 && (
              <Card className="p-8 text-center">
                <div className="max-w-md mx-auto">
                  <h3 className="text-lg font-semibold mb-2">No businesses found</h3>
                  <p className="text-muted-foreground mb-4">
                    {hasActiveFilters 
                      ? "No businesses match your current filters. Try adjusting your criteria."
                      : "No businesses are currently listed in our directory."
                    }
                  </p>
                  {hasActiveFilters && (
                    <Button onClick={clearFilters} variant="outline">
                      Clear Filters
                    </Button>
                  )}
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}