# Homepage LocalBiz Layout Adaptation Prompt

## 🎯 Objective
Transform the current Volo App Template homepage to adopt the LocalBiz layout structure while maintaining our unique design system, branding, and adding our own creative twists to create a more engaging and modern business directory experience.

## 📋 Current State Analysis

### ✅ What's Already Working
- **Existing Components**: HeroSection, CategoryHighlights, FeaturedBusinesses, NewBusinesses, MarqueeBanner
- **Design System**: Complete color tokens, typography scale, spacing system
- **Theme Support**: Light/dark mode with smooth transitions
- **Component Structure**: Well-organized React components with TypeScript
- **API Integration**: Dynamic data fetching for businesses and categories

### 🎨 LocalBiz Layout Elements to Adopt
1. **Sticky Header**: Backdrop blur with transparency
2. **Marquee Banner**: Positioned after header, before hero
3. **Hero Section**: Large background image with overlay and centered search
4. **Category Grid**: Clean circular icons with hover effects
5. **New Businesses Section**: Card-based layout with images
6. **Featured Products**: Horizontal scrolling product showcase
7. **Clean Footer**: Simple and minimal design

## 🔧 Implementation Tasks

### Phase 1: Header Enhancement

#### 1.1 Update PublicHeader Component
- Add sticky positioning with backdrop blur
- Implement transparency with background blur effect
- Maintain existing navigation structure but enhance styling
- Keep theme toggle functionality
- Add subtle border and shadow effects

**Design System Integration:**
```tsx
// Enhanced header styling
className="sticky top-0 z-50 w-full border-b border-border/20 bg-background/80 backdrop-blur-lg"
```

### Phase 2: Marquee Banner Redesign

#### 2.1 Enhance MarqueeBanner Component
- Move marquee to top of page (after header)
- Create dynamic announcements mixing new businesses and offers
- Add promotional content and special announcements
- Use primary color background with white text
- Implement smooth infinite scroll animation

**Content Strategy:**
- "New: [Business Name] now open!"
- "Offer: [Discount] at [Business Name]!"
- "Featured: [Business Name] - [Category]!"
- "Special: [Promotional content]!"

### Phase 3: Hero Section Transformation

#### 3.1 Complete Hero Redesign
- **Background**: Large hero image with dark overlay (60% opacity)
- **Layout**: Centered content with large typography
- **Search**: Single large search bar with integrated button
- **Typography**: Use design system h1 with serif font for impact
- **CTA**: Prominent search functionality

**Key Features:**
```tsx
// Hero background with overlay
style={{
  backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url('hero-image.jpg')`
}}

// Large search bar design
<div className="relative w-full max-w-2xl">
  <input className="h-16 w-full rounded-lg bg-white/90 pl-12 pr-32 text-lg" />
  <button className="absolute right-2 top-2 bottom-2 px-6 bg-primary text-primary-foreground rounded-lg">
    Search
  </button>
</div>
```

### Phase 4: Category Section Redesign

#### 4.1 Transform CategoryHighlights
- **Layout**: Grid layout with circular image backgrounds
- **Icons**: Replace Lucide icons with category images/photos
- **Hover Effects**: Subtle lift and shadow on hover
- **Design**: Clean white cards with rounded images
- **Responsive**: 2 cols mobile, 3 cols tablet, 6 cols desktop

**Visual Enhancement:**
```tsx
// Category card with image background
<div className="h-24 w-24 rounded-full bg-cover bg-center mx-auto mb-3" 
     style={{ backgroundImage: `url('${category.imageUrl}')` }} />
```

### Phase 5: New Businesses Section Enhancement

#### 5.1 Redesign NewBusinesses Component
- **Background**: Light secondary color background
- **Layout**: Card-based grid with aspect-ratio images
- **Images**: Prominent business photos with aspect-video ratio
- **Content**: Business name and description in card footer
- **Hover**: Subtle lift and shadow effects

### Phase 6: Featured Products Section (New)

#### 6.1 Create FeaturedProducts Component
- **Layout**: Horizontal scrolling product showcase
- **Content**: Products from local businesses with pricing
- **Design**: Product cards with images, names, business attribution, prices
- **Interaction**: Smooth horizontal scroll with snap points
- **Responsive**: Touch-friendly on mobile, mouse-friendly on desktop

**Component Structure:**
```tsx
interface Product {
  id: number;
  name: string;
  price: number;
  imageUrl: string;
  business: {
    name: string;
    slug: string;
  };
}
```

### Phase 7: Footer Simplification

#### 7.1 Streamline Footer Design
- **Layout**: Single row with links and copyright
- **Styling**: Minimal design with muted colors
- **Content**: Essential links only (About, Contact, Privacy, Terms)
- **Responsive**: Stack on mobile, inline on desktop

## 🎨 Design System Adaptations

### Color Palette Integration
```css
/* Use existing design system colors */
--primary: #6e56cf (Light) / #a48fff (Dark)
--secondary: #e4dfff (Light) / #2d2b55 (Dark)
--background: #f5f5ff (Light) / #0f0f1a (Dark)
--card: #ffffff (Light) / #1a1a2e (Dark)
```

### Typography Enhancements
```tsx
// Hero title with serif font
<h1 className="h1 font-serif text-white text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tighter">
  Discover the Best Businesses in Your Community
</h1>

// Section headings
<h2 className="h2 text-3xl sm:text-4xl font-bold tracking-tight">
  Explore Categories
</h2>
```

### Spacing and Layout
- Use design system spacing scale (space-1 through space-10)
- Maintain 8pt grid system
- Consistent container max-widths and padding
- Responsive breakpoints aligned with design system

## 🚀 Creative Twists & Enhancements

### 1. Dynamic Hero Backgrounds
- Rotate hero background images based on time of day
- Use local business photos as hero backgrounds
- Implement smooth transitions between backgrounds

### 2. Interactive Category Hover
- Animated icon transformations on hover
- Category-specific color accents
- Micro-interactions with business count updates

### 3. Smart Marquee Content
- Mix business announcements with local events
- Seasonal promotions and community highlights
- Real-time updates from business API

### 4. Enhanced Search Experience
- Search suggestions dropdown
- Recent searches memory
- Location-based search hints

### 5. Product Showcase Innovation
- Business spotlight rotation
- Seasonal product highlights
- Local artisan features

### 6. Performance Optimizations
- Lazy loading for images
- Intersection Observer for animations
- Optimized marquee performance

## 📁 Files to Create/Modify

### New Components
- `/my-app/ui/src/components/homepage/FeaturedProducts.tsx`
- `/my-app/ui/src/components/homepage/ProductCard.tsx`
- `/my-app/ui/src/components/homepage/HeroBackground.tsx`

### Enhanced Components
- `/my-app/ui/src/components/homepage/HeroSection.tsx` - Complete redesign
- `/my-app/ui/src/components/homepage/CategoryHighlights.tsx` - Visual overhaul
- `/my-app/ui/src/components/homepage/NewBusinesses.tsx` - Layout improvements
- `/my-app/ui/src/components/homepage/MarqueeBanner.tsx` - Content enhancement
- `/my-app/ui/src/components/navigation/PublicHeader.tsx` - Sticky blur header
- `/my-app/ui/src/pages/HomePage.tsx` - Component reordering

### Style Updates
- `/my-app/ui/src/index.css` - Add marquee animations and hero styles
- Add hero background images to public assets

## 🎯 Key Differences from LocalBiz

### Our Unique Twists
1. **Design System Integration**: Use our purple/blue color scheme instead of blue
2. **Theme Support**: Maintain light/dark mode functionality
3. **Typography**: Use our serif/sans font combination
4. **Interactive Elements**: Enhanced hover states and micro-interactions
5. **Dynamic Content**: Real business data instead of static content
6. **Modern Components**: React-based with TypeScript and proper state management

### Enhanced Features
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Performance**: Optimized images and lazy loading
- **Responsive**: Mobile-first design with touch interactions
- **SEO**: Semantic HTML structure and meta tags
- **Analytics**: Event tracking for user interactions

## 🔍 Quality Assurance Checklist

### Visual Consistency
- [ ] All components use design system colors and typography
- [ ] Consistent spacing and layout patterns
- [ ] Smooth transitions and hover effects
- [ ] Proper image aspect ratios and loading states

### Functionality
- [ ] Search functionality works correctly
- [ ] Category navigation functions properly
- [ ] Business links navigate to correct pages
- [ ] Marquee animation is smooth and performant

### Responsive Design
- [ ] Mobile layout is touch-friendly
- [ ] Tablet layout utilizes space effectively
- [ ] Desktop layout is visually appealing
- [ ] All breakpoints transition smoothly

### Accessibility
- [ ] Proper heading hierarchy (h1, h2, h3)
- [ ] Alt text for all images
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility

### Performance
- [ ] Images are optimized and lazy-loaded
- [ ] Animations are smooth (60fps)
- [ ] Initial page load is fast
- [ ] No layout shift during loading

## 📝 Implementation Notes

### Development Approach
1. **Start with header and marquee** - Foundation elements
2. **Transform hero section** - Most visual impact
3. **Enhance categories** - Core navigation
4. **Update business sections** - Content showcase
5. **Add products section** - New feature
6. **Finalize footer** - Completion

### Testing Strategy
- Test on multiple devices and screen sizes
- Verify theme switching works correctly
- Validate search functionality
- Check image loading and fallbacks
- Test marquee performance

### Content Strategy
- Use real business data where possible
- Implement graceful fallbacks for missing data
- Create engaging marquee content
- Optimize images for web delivery

## 💻 Detailed Implementation Examples

### Enhanced Header Component
```tsx
// PublicHeader.tsx updates
<header className="sticky top-0 z-50 w-full border-b border-border/20 bg-background/80 backdrop-blur-lg transition-all duration-200">
  <div className="container mx-auto flex h-20 items-center justify-between px-4 sm:px-6 lg:px-8">
    {/* Logo and Navigation */}
    <div className="flex items-center gap-8">
      <Link to="/" className="flex items-center gap-3 text-foreground">
        <div className="h-8 w-8 text-primary">
          {/* Your logo SVG */}
        </div>
        <h1 className="text-2xl font-bold leading-tight tracking-tighter">
          Volo Business Directory
        </h1>
      </Link>

      {/* Navigation remains the same but with enhanced styling */}
    </div>
  </div>
</header>
```

### Redesigned Hero Section
```tsx
// HeroSection.tsx complete redesign
export function HeroSection({ onSearch }: HeroSectionProps) {
  return (
    <section
      className="relative flex min-h-[60vh] items-center justify-center bg-cover bg-center py-20 text-center"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url('/hero-business-bg.jpg')`
      }}
    >
      <div className="container mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <h1 className="h1 font-serif text-white text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tighter">
          Discover the Best Businesses in Your Community
        </h1>
        <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-200">
          Explore a curated directory of local businesses, from cozy cafes to expert services, all in one place.
        </p>

        {/* Large Search Bar */}
        <div className="mx-auto mt-8 flex w-full max-w-2xl items-center">
          <div className="relative w-full">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
              <Search className="h-6 w-6 text-gray-400" />
            </div>
            <input
              className="h-16 w-full rounded-lg border border-transparent bg-white/90 pl-12 pr-32 text-lg text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary"
              placeholder="Search for businesses or services"
              type="search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
            />
            <Button
              onClick={handleSearch}
              className="absolute inset-y-0 right-0 m-2 px-6 text-sm font-semibold"
            >
              Search
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
```

### Enhanced Category Grid
```tsx
// CategoryHighlights.tsx with circular images
export function CategoryHighlights() {
  return (
    <section className="py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <h2 className="h2 text-3xl font-bold tracking-tight sm:text-4xl">
            Explore Categories
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Find what you're looking for by browsing our popular categories.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
          {categories.map((category) => (
            <Link
              key={category.id}
              to={`/categories/${category.slug}`}
              className="group flex flex-col items-center gap-3 rounded-lg bg-card p-4 text-center transition-all hover:shadow-lg hover:-translate-y-1"
            >
              <div
                className="h-24 w-24 rounded-full bg-cover bg-center"
                style={{
                  backgroundImage: `url('${category.imageUrl || '/category-placeholder.jpg'}')`
                }}
              />
              <p className="text-base font-semibold text-foreground">
                {category.name}
              </p>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
```

### New Featured Products Component
```tsx
// FeaturedProducts.tsx - New component
interface Product {
  id: number;
  name: string;
  price: number;
  imageUrl: string;
  business: {
    name: string;
    slug: string;
  };
}

export function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([]);

  return (
    <section className="py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <h2 className="h2 text-3xl font-bold tracking-tight sm:text-4xl">
            Featured Products
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Discover top products from our local businesses.
          </p>
        </div>

        <div className="relative">
          <div className="flex snap-x snap-mandatory gap-6 overflow-x-auto pb-8 scrollbar-hide">
            {products.map((product) => (
              <div key={product.id} className="flex-shrink-0 snap-center">
                <div className="w-80 overflow-hidden rounded-lg bg-card shadow-lg transition-all hover:shadow-xl">
                  <div
                    className="h-56 w-full bg-cover bg-center"
                    style={{ backgroundImage: `url('${product.imageUrl}')` }}
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      {product.name}
                    </h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      from {product.business.name}
                    </p>
                    <p className="mt-2 text-lg font-bold text-primary">
                      ${product.price.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
```

### Enhanced Marquee Banner
```tsx
// MarqueeBanner.tsx with dynamic content
export function MarqueeBanner() {
  const [announcements, setAnnouncements] = useState<string[]>([]);

  useEffect(() => {
    // Generate dynamic announcements
    const generateAnnouncements = () => {
      const newBusinesses = businesses.slice(0, 3);
      const offers = [
        "20% off at Urban Threads!",
        "Free consultation at Wellness Spa!",
        "Grand opening special at Tech Hub!"
      ];

      const announcements = [
        ...newBusinesses.map(b => `New: ${b.name} now open!`),
        ...offers.map(o => `Offer: ${o}`),
        "Featured: Local artisan showcase this weekend!",
        "Special: Support local businesses month!"
      ];

      return [...announcements, ...announcements]; // Duplicate for seamless loop
    };

    setAnnouncements(generateAnnouncements());
  }, [businesses]);

  return (
    <div className="overflow-hidden bg-primary py-3 text-primary-foreground">
      <div className="marquee whitespace-nowrap">
        {announcements.map((announcement, index) => (
          <span key={index} className="mx-8 text-sm font-medium">
            {announcement}
          </span>
        ))}
      </div>
    </div>
  );
}
```

### CSS Animations
```css
/* Add to index.css */
.marquee {
  animation: marquee 30s linear infinite;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Backdrop blur support */
.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}
```

## 🎨 Asset Requirements

### Hero Background Images
- **Primary Hero**: Modern business district or local community scene
- **Fallback**: Gradient background matching design system
- **Dimensions**: 1920x1080 minimum, optimized for web
- **Format**: WebP with JPEG fallback

### Category Images
- **Restaurants**: Food/dining scene
- **Retail**: Shopping/storefront
- **Services**: Professional/office environment
- **Health**: Wellness/medical facility
- **Automotive**: Car service/garage
- **Home Services**: Tools/home improvement

### Product Images
- **Placeholder**: Design system branded placeholder
- **Real Products**: Business-provided product photos
- **Optimization**: Multiple sizes for responsive images

## 🔄 Migration Strategy

### Phase 1: Foundation (Day 1)
1. Update PublicHeader with sticky blur effect
2. Enhance MarqueeBanner with dynamic content
3. Add hero background image and overlay

### Phase 2: Core Sections (Day 2)
1. Redesign HeroSection with large search
2. Transform CategoryHighlights with circular images
3. Update NewBusinesses layout

### Phase 3: New Features (Day 3)
1. Create FeaturedProducts component
2. Implement horizontal scrolling
3. Add product data integration

### Phase 4: Polish & Testing (Day 4)
1. Fine-tune animations and transitions
2. Optimize performance and loading
3. Test responsive behavior
4. Validate accessibility

---

**Priority**: High
**Estimated Effort**: 2-3 development sessions
**Dependencies**: Existing design system and component library
**Risk Level**: Medium (significant visual changes but maintaining functionality)

**Quick Start Checklist**:
- [ ] Update header with backdrop blur
- [ ] Add hero background image
- [ ] Enhance marquee with dynamic content
- [ ] Transform category grid layout
- [ ] Create featured products section
- [ ] Test responsive behavior
- [ ] Validate theme switching compatibility
