# 🎯 LocalBiz Business Profile & Automated Photo Collection Setup Guide

## 📋 Overview

This guide will help you set up the new LocalBiz-style business profiles with automated photo collection from Google Places API.

## 🚀 Quick Setup

### 1. Database Migration

Run the database migration to add photo collection fields:

```bash
cd server
node scripts/migrate-photo-collection.js
```

### 2. Environment Configuration

Add your Google Places API key to your `.env` file:

```bash
# Add to server/.env
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
```

**Get your Google Places API key:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the "Places API" and "Places API (New)"
4. Create credentials (API Key)
5. Restrict the key to Places API for security

### 3. Restart Your Server

```bash
cd server
npm run dev
```

## 🎨 What's New

### ✅ LocalBiz Design System
- **Clean Header**: Logo, Navigation, Search, Actions
- **Business Hero**: Logo, ratings, contact info, quick actions
- **Tabbed Content**: About, Contact, Location, Photos, Reviews
- **Space Grotesk/Noto Sans** fonts
- **Responsive design** for mobile and desktop

### ✅ Automated Photo Collection
- **Google Places Integration**: Automatically fetches business photos
- **Admin Approval Workflow**: Photos require approval before showing
- **Source Tracking**: Tracks where photos came from
- **Bulk Management**: Approve/reject multiple photos at once

### ✅ Admin Photo Review
- **Photo Review Dashboard**: `/admin/photos`
- **Pending Photos**: View all photos awaiting approval
- **Quick Actions**: Approve, reject, set as primary
- **Business Grouping**: Photos organized by business
- **Manual Photo Addition**: Add photos manually if needed

## 🔧 How It Works

### Automatic Photo Collection

When you create a new business through the admin panel:

1. **Business Created** → System automatically searches Google Places
2. **Photos Found** → Downloads up to 6 photos from Google Places
3. **Pending Approval** → Photos are marked as "pending" (not visible to public)
4. **Admin Review** → Admin reviews and approves/rejects photos
5. **Public Display** → Approved photos appear on business profile

### Manual Photo Collection

You can also trigger photo collection manually:

```bash
# Via API (admin only)
POST /api/v1/admin/photos/businesses/{business_id}/collect-photos
```

## 📱 Admin Interface

### Photo Review Dashboard

Navigate to **Admin Panel → Photo Review** to:

- ✅ **View pending photos** organized by business
- ✅ **Approve/reject individual photos**
- ✅ **Set photos as primary** (main business photo)
- ✅ **Bulk approve** all photos for a business
- ✅ **Add manual photos** with URL
- ✅ **Filter by source** (Google Places, Manual, etc.)

### Business Management

The business creation workflow now includes:

- ✅ **Automatic photo collection** when business is created
- ✅ **Background processing** (doesn't slow down business creation)
- ✅ **Error handling** (graceful failure if photo collection fails)

## 🎯 Business Profile Features

### LocalBiz Design

All business profiles now use the LocalBiz design with:

- **Professional Header** with search and navigation
- **Business Hero Card** with logo, ratings, and quick actions
- **Tabbed Content Layout**:
  - **About**: Business description
  - **Contact**: Phone, email, website, address, hours
  - **Location**: Address and map (Google Maps integration)
  - **Photos**: Auto-collected and manual photos
  - **Reviews**: Customer reviews with ratings

### Photo Integration

- ✅ **Only approved photos** are displayed to public
- ✅ **Primary photo** highlighted with "Main" badge
- ✅ **Responsive photo grid** (2-4 columns based on screen size)
- ✅ **Hover effects** and smooth transitions
- ✅ **Fallback handling** for broken images

## 🔍 Testing Your Setup

### 1. Test Photo Collection

1. **Create a new business** through admin panel
2. **Check server logs** for photo collection activity
3. **Visit Photo Review** (`/admin/photos`) to see pending photos
4. **Approve some photos** and set one as primary
5. **Visit business profile** to see approved photos

### 2. Test LocalBiz Design

1. **Visit any business profile** (e.g., `/business/tech-solutions-inc`)
2. **Check responsive design** on mobile and desktop
3. **Test tab navigation** (About, Contact, Location, Photos, Reviews)
4. **Verify fonts** (Space Grotesk/Noto Sans)

### 3. Test Admin Features

1. **Photo Review Dashboard** (`/admin/photos`)
2. **Bulk approve photos** for a business
3. **Add manual photo** with URL
4. **Set photo as primary**

## 🚨 Troubleshooting

### Photo Collection Not Working

1. **Check API Key**: Ensure `GOOGLE_PLACES_API_KEY` is set in `.env`
2. **Check API Limits**: Google Places API has usage limits
3. **Check Server Logs**: Look for error messages in console
4. **Check Business Address**: Ensure business has a valid address

### Photos Not Displaying

1. **Check Approval Status**: Only approved photos are shown
2. **Check Image URLs**: Ensure image URLs are accessible
3. **Check Browser Console**: Look for image loading errors

### LocalBiz Design Issues

1. **Check Font Loading**: Ensure Space Grotesk/Noto Sans are loading
2. **Check CSS**: Verify Tailwind classes are working
3. **Check Responsive**: Test on different screen sizes

## 📊 API Endpoints

### Photo Collection (Admin Only)

```bash
# Trigger photo collection for business
POST /api/v1/admin/photos/businesses/{id}/collect-photos

# Get pending photos
GET /api/v1/admin/photos/pending

# Approve photo
PUT /api/v1/admin/photos/{id}/approve

# Reject photo
DELETE /api/v1/admin/photos/{id}/reject

# Set as primary photo
PUT /api/v1/admin/photos/{id}/set-primary

# Bulk approve all photos for business
PUT /api/v1/admin/businesses/{id}/photos/approve-all

# Add manual photo
POST /api/v1/admin/businesses/{id}/photos/manual
```

## 🎉 Success Metrics

After setup, you should see:

- ✅ **80%+ of businesses** have at least 3 photos
- ✅ **LocalBiz design** on all business profiles
- ✅ **Fast loading times** with optimized images
- ✅ **Mobile-responsive** design
- ✅ **Admin efficiency** with bulk photo management

## 🔄 Next Steps

1. **Monitor photo collection** success rates
2. **Optimize image loading** for better performance
3. **Add more photo sources** (Yelp, Facebook, etc.)
4. **Implement image optimization** (WebP, compression)
5. **Add photo analytics** (view counts, engagement)

---

**Need Help?** Check the server logs for detailed error messages and ensure all environment variables are properly configured.
