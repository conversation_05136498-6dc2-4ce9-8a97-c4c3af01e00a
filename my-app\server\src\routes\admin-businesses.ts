// Admin business management routes
import { Hono } from 'hono';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import { photoCollectionService } from '../services/photo-collection';
import type { AdminResponse } from '../types/admin';

const businessRoutes = new Hono();

// ===== ENHANCED BUSINESS MANAGEMENT =====
// Enhanced Business Listing with Advanced Filtering
businessRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search');
    const category = c.req.query('category');
    const status = c.req.query('status'); // active, inactive, all
    const featured = c.req.query('featured');
    const sortBy = c.req.query('sortBy') || 'created_at';
    const sortOrder = c.req.query('sortOrder') || 'desc';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Build where conditions
    const whereConditions = [];
    
    if (status === 'active') {
      whereConditions.push(eq(businessSchema.businesses.is_active, true));
    } else if (status === 'inactive') {
      whereConditions.push(eq(businessSchema.businesses.is_active, false));
    }
    
    if (featured === 'true') {
      whereConditions.push(eq(businessSchema.businesses.is_featured, true));
    } else if (featured === 'false') {
      whereConditions.push(eq(businessSchema.businesses.is_featured, false));
    }
    
    if (category) {
      whereConditions.push(eq(businessSchema.businesses.category_id, parseInt(category)));
    }
    
    if (search) {
      whereConditions.push(
        sql`(${businessSchema.businesses.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.address} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.short_description} ILIKE ${`%${search}%`})`
      );
    }

    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        is_featured: businessSchema.businesses.is_featured,
        is_active: businessSchema.businesses.is_active,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id));

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Apply sorting
    const orderColumn = sortBy === 'name' ? businessSchema.businesses.name :
                       sortBy === 'rating' ? businessSchema.businesses.average_rating :
                       sortBy === 'reviews' ? businessSchema.businesses.total_reviews :
                       businessSchema.businesses.created_at;
    
    const orderDirection = sortOrder === 'asc' ? asc : desc;
    query = query.orderBy(orderDirection(orderColumn));

    const businesses = await query.limit(limit).offset(offset);

    // Get total count with same filters
    let countQuery = db.select({ total: count() }).from(businessSchema.businesses);
    if (category) {
      countQuery = countQuery.leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id));
    }
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    const response: AdminResponse<typeof businesses> = {
      data: businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        filters: { search, category, status, featured, sortBy, sortOrder },
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching admin businesses:', error);
    return c.json({ 
      error: 'Failed to fetch businesses',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Create New Business
businessRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Validate required fields
    if (!body.name || !body.category_id || !body.address) {
      return c.json({ 
        error: 'Missing required fields: name, category_id, address',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    // Generate slug from name
    const slug = body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

    const [newBusiness] = await db
      .insert(businessSchema.businesses)
      .values({
        name: body.name,
        slug,
        category_id: body.category_id,
        description: body.description || '',
        short_description: body.short_description || body.description?.substring(0, 500) || '',
        address: body.address,
        latitude: body.latitude || null,
        longitude: body.longitude || null,
        phone: body.phone || null,
        email: body.email || null,
        website: body.website || null,
        logo_url: body.logo_url || null,
        hero_image_url: body.hero_image_url || null,
        is_featured: body.is_featured || false,
        is_active: body.is_active !== false,
      })
      .returning();

    // Automatically collect photos from Google Places (async, don't wait)
    if (newBusiness.address) {
      photoCollectionService.collectBusinessPhotos({
        id: newBusiness.id,
        name: newBusiness.name,
        address: newBusiness.address,
        latitude: newBusiness.latitude,
        longitude: newBusiness.longitude,
      }).catch(error => {
        console.log(`Photo collection failed for business ${newBusiness.name}:`, error.message);
      });
    }

    const response: AdminResponse<typeof newBusiness> = {
      data: newBusiness,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response, 201);
  } catch (error) {
    console.error('Error creating business:', error);
    return c.json({ 
      error: 'Failed to create business',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Update Business
businessRoutes.put('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    const db = await getDatabase();

    // Check if business exists
    const [existingBusiness] = await db
      .select()
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.id, id))
      .limit(1);

    if (!existingBusiness) {
      return c.json({ 
        error: 'Business not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Update slug if name changed
    const slug = body.name ? 
      body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') :
      existingBusiness.slug;

    const [updatedBusiness] = await db
      .update(businessSchema.businesses)
      .set({
        name: body.name || existingBusiness.name,
        slug,
        category_id: body.category_id || existingBusiness.category_id,
        description: body.description !== undefined ? body.description : existingBusiness.description,
        short_description: body.short_description !== undefined ? body.short_description : existingBusiness.short_description,
        address: body.address || existingBusiness.address,
        latitude: body.latitude !== undefined ? body.latitude : existingBusiness.latitude,
        longitude: body.longitude !== undefined ? body.longitude : existingBusiness.longitude,
        phone: body.phone !== undefined ? body.phone : existingBusiness.phone,
        email: body.email !== undefined ? body.email : existingBusiness.email,
        website: body.website !== undefined ? body.website : existingBusiness.website,
        logo_url: body.logo_url !== undefined ? body.logo_url : existingBusiness.logo_url,
        hero_image_url: body.hero_image_url !== undefined ? body.hero_image_url : existingBusiness.hero_image_url,
        is_featured: body.is_featured !== undefined ? body.is_featured : existingBusiness.is_featured,
        is_active: body.is_active !== undefined ? body.is_active : existingBusiness.is_active,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.businesses.id, id))
      .returning();

    const response: AdminResponse<typeof updatedBusiness> = {
      data: updatedBusiness,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error updating business:', error);
    return c.json({
      error: 'Failed to update business',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Delete Business (Soft Delete)
businessRoutes.delete('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    // Check if business exists
    const [existingBusiness] = await db
      .select()
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.id, id))
      .limit(1);

    if (!existingBusiness) {
      return c.json({
        error: 'Business not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Soft delete by setting is_active to false
    await db
      .update(businessSchema.businesses)
      .set({
        is_active: false,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.businesses.id, id));

    return c.json({
      message: 'Business deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting business:', error);
    return c.json({
      error: 'Failed to delete business',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Toggle Featured Status
businessRoutes.patch('/:id/toggle-featured', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    const [business] = await db
      .select({ is_featured: businessSchema.businesses.is_featured })
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.id, id))
      .limit(1);

    if (!business) {
      return c.json({
        error: 'Business not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    const [updatedBusiness] = await db
      .update(businessSchema.businesses)
      .set({
        is_featured: !business.is_featured,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.businesses.id, id))
      .returning();

    return c.json({
      message: `Business ${updatedBusiness.is_featured ? 'featured' : 'unfeatured'} successfully`,
      business: updatedBusiness,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error toggling featured status:', error);
    return c.json({
      error: 'Failed to toggle featured status',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Toggle Active Status
businessRoutes.patch('/:id/toggle-status', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    const [business] = await db
      .select({ is_active: businessSchema.businesses.is_active })
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.id, id))
      .limit(1);

    if (!business) {
      return c.json({
        error: 'Business not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    const [updatedBusiness] = await db
      .update(businessSchema.businesses)
      .set({
        is_active: !business.is_active,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.businesses.id, id))
      .returning();

    return c.json({
      message: `Business ${updatedBusiness.is_active ? 'activated' : 'deactivated'} successfully`,
      business: updatedBusiness,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error toggling active status:', error);
    return c.json({
      error: 'Failed to toggle active status',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

export default businessRoutes;
