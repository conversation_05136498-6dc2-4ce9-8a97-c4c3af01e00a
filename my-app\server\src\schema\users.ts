import { pgSchema, pgTable, text, timestamp, boolean } from 'drizzle-orm/pg-core';

// Create private schema for application tables
export const appSchema = pgSchema('app');

export const users = appSchema.table('users', {
  id: text('id').primaryKey(),
  email: text('email').unique().notNull(),
  display_name: text('display_name'),
  photo_url: text('photo_url'),
  is_admin: boolean('is_admin').default(false).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

// Extended user type with admin capabilities
export type UserWithPermissions = User & {
  permissions: string[];
};