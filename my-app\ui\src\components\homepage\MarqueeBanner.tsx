import { useEffect, useState } from 'react';
import { api } from '@/lib/serverComm';
import { Badge } from '@/components/ui/badge';
import { Star, MapPin, Sparkles } from 'lucide-react';

interface Business {
  id: number;
  name: string;
  category: {
    name: string;
  };
}

interface Announcement {
  id: number;
  text: string;
  type: 'new' | 'featured' | 'offer' | 'special';
  icon?: React.ReactNode;
}

export function MarqueeBanner() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);

  useEffect(() => {
    async function fetchBusinesses() {
      try {
        const response = await api.getBusinesses({ limit: 20 });
        // Ensure we have a valid array of businesses
        if (response && response.businesses && Array.isArray(response.businesses)) {
          setBusinesses(response.businesses);
        } else {
          // Use fallback data if response structure is unexpected
          setBusinesses([
            { id: 1, name: "Amigos Del Mar Tour Center", category: { name: "Tours & Activities" } },
            { id: 2, name: "Victoria House Resort & Spa", category: { name: "Hotels & Accommodations" } },
            { id: 3, name: "Estel's Dine by the Sea", category: { name: "Restaurants & Dining" } },
            { id: 4, name: "Maya Mountain Coffee", category: { name: "Food & Beverages" } },
            { id: 5, name: "Jade Maya Jewelry", category: { name: "Jewelry & Accessories" } },
          ]);
        }
      } catch (err) {
        console.error('Error fetching businesses for marquee:', err);
        // Fallback to static data if API fails
        setBusinesses([
          { id: 1, name: "Amigos Del Mar Tour Center", category: { name: "Tours & Activities" } },
          { id: 2, name: "Victoria House Resort & Spa", category: { name: "Hotels & Accommodations" } },
          { id: 3, name: "Estel's Dine by the Sea", category: { name: "Restaurants & Dining" } },
          { id: 4, name: "Maya Mountain Coffee", category: { name: "Food & Beverages" } },
          { id: 5, name: "Jade Maya Jewelry", category: { name: "Jewelry & Accessories" } },
        ]);
      }
    }

    fetchBusinesses();
  }, []);

  // Generate dynamic announcements from businesses
  useEffect(() => {
    if (businesses.length > 0) {
      const newAnnouncements: Announcement[] = [];

      businesses.forEach((business, index) => {
        const announcementTypes = [
          {
            type: 'new' as const,
            text: `🎉 New: ${business.name} now open in ${business.category.name}!`,
            icon: <Sparkles className="w-3 h-3" />
          },
          {
            type: 'featured' as const,
            text: `⭐ Featured: ${business.name} - Top-rated ${business.category.name}`,
            icon: <Star className="w-3 h-3" />
          },
          {
            type: 'offer' as const,
            text: `🔥 Special Offer: Visit ${business.name} for exclusive deals!`,
            icon: <MapPin className="w-3 h-3" />
          }
        ];

        const randomType = announcementTypes[index % announcementTypes.length];
        newAnnouncements.push({
          id: business.id,
          ...randomType
        });
      });

      // Add some general promotional announcements
      newAnnouncements.push(
        {
          id: 999,
          type: 'special',
          text: '🌟 Discover amazing local businesses in Belize!',
          icon: <Sparkles className="w-3 h-3" />
        },
        {
          id: 998,
          type: 'special',
          text: '📍 Find the best services near you with LocalBiz',
          icon: <MapPin className="w-3 h-3" />
        }
      );

      setAnnouncements(newAnnouncements);
    }
  }, [businesses]);

  // Create a continuous list by duplicating the announcements
  const marqueeItems = Array.isArray(announcements) && announcements.length > 0
    ? [...announcements, ...announcements]
    : [];

  // Don't render anything if there are no items
  if (marqueeItems.length === 0) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-primary via-primary/90 to-primary text-primary-foreground py-3 overflow-hidden border-b border-primary/20 shadow-sm">
      <div className="relative">
        <div className="flex animate-marquee whitespace-nowrap">
          {marqueeItems.map((announcement, index) => (
            <div
              key={`${announcement.id}-${index}`}
              className="mx-6 flex items-center gap-2 text-sm font-medium"
            >
              <span className="flex items-center gap-1.5">
                {announcement.icon}
                {announcement.text}
              </span>
              <span className="text-primary-foreground/60">•</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Add the marquee animation to your global CSS (index.css)
// @keyframes marquee {
//   0% {
//     transform: translateX(0%);
//   }
//   100% {
//     transform: translateX(-50%);
//   }
// }
// 
// .animate-marquee {
//   animation: marquee 30s linear infinite;
// }
