// Enhanced admin application management routes
import { Hono } from 'hono';
import { eq, and, like, desc, asc, count, sql, gte, lte } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import { calculateApplicationAnalytics } from '../lib/analytics';
import type { AdminResponse, AdminApplicationFilters } from '../types/admin';

const applicationRoutes = new Hono();

// ===== ENHANCED APPLICATION MANAGEMENT =====
// Get All Applications with Advanced Filtering
applicationRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status') || 'pending';
    const category = c.req.query('category');
    const search = c.req.query('search');
    const dateFrom = c.req.query('dateFrom');
    const dateTo = c.req.query('dateTo');
    const sortBy = c.req.query('sortBy') || 'submitted_at';
    const sortOrder = c.req.query('sortOrder') || 'desc';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Build where conditions
    const whereConditions = [];
    
    if (status !== 'all') {
      whereConditions.push(eq(businessSchema.businessApplications.status, status));
    }
    
    if (category) {
      whereConditions.push(eq(businessSchema.businessApplications.category_id, parseInt(category)));
    }
    
    if (search) {
      whereConditions.push(
        sql`(${businessSchema.businessApplications.business_name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businessApplications.description} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businessApplications.contact_person} ILIKE ${`%${search}%`} OR
            ${businessSchema.businessApplications.email} ILIKE ${`%${search}%`})`
      );
    }
    
    if (dateFrom) {
      whereConditions.push(gte(businessSchema.businessApplications.submitted_at, new Date(dateFrom)));
    }
    
    if (dateTo) {
      whereConditions.push(lte(businessSchema.businessApplications.submitted_at, new Date(dateTo)));
    }

    let query = db
      .select({
        id: businessSchema.businessApplications.id,
        business_name: businessSchema.businessApplications.business_name,
        description: businessSchema.businessApplications.description,
        address: businessSchema.businessApplications.address,
        phone: businessSchema.businessApplications.phone,
        email: businessSchema.businessApplications.email,
        website: businessSchema.businessApplications.website,
        contact_person: businessSchema.businessApplications.contact_person,
        status: businessSchema.businessApplications.status,
        admin_notes: businessSchema.businessApplications.admin_notes,
        submitted_at: businessSchema.businessApplications.submitted_at,
        reviewed_at: businessSchema.businessApplications.reviewed_at,
        approved_business_id: businessSchema.businessApplications.approved_business_id,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        },
        // Calculate processing time for completed applications
        processing_time_hours: sql<number>`
          CASE 
            WHEN ${businessSchema.businessApplications.reviewed_at} IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (${businessSchema.businessApplications.reviewed_at} - ${businessSchema.businessApplications.submitted_at})) / 3600
            ELSE NULL 
          END
        `,
      })
      .from(businessSchema.businessApplications)
      .leftJoin(businessSchema.categories, eq(businessSchema.businessApplications.category_id, businessSchema.categories.id));

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Apply sorting
    const orderColumn = sortBy === 'business_name' ? businessSchema.businessApplications.business_name :
                       sortBy === 'category' ? businessSchema.categories.name :
                       sortBy === 'status' ? businessSchema.businessApplications.status :
                       businessSchema.businessApplications.submitted_at;
    
    const orderDirection = sortOrder === 'asc' ? asc : desc;
    query = query.orderBy(orderDirection(orderColumn));

    const applications = await query.limit(limit).offset(offset);

    // Get total count with same filters
    let countQuery = db.select({ total: count() }).from(businessSchema.businessApplications);
    if (category) {
      countQuery = countQuery.leftJoin(businessSchema.categories, eq(businessSchema.businessApplications.category_id, businessSchema.categories.id));
    }
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    const response: AdminResponse<typeof applications> = {
      data: applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        filters: { status, category, search, dateFrom, dateTo, sortBy, sortOrder },
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching applications:', error);
    return c.json({ 
      error: 'Failed to fetch applications',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get Application by ID
applicationRoutes.get('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    const [application] = await db
      .select({
        id: businessSchema.businessApplications.id,
        business_name: businessSchema.businessApplications.business_name,
        description: businessSchema.businessApplications.description,
        address: businessSchema.businessApplications.address,
        latitude: businessSchema.businessApplications.latitude,
        longitude: businessSchema.businessApplications.longitude,
        phone: businessSchema.businessApplications.phone,
        email: businessSchema.businessApplications.email,
        website: businessSchema.businessApplications.website,
        contact_person: businessSchema.businessApplications.contact_person,
        additional_info: businessSchema.businessApplications.additional_info,
        status: businessSchema.businessApplications.status,
        admin_notes: businessSchema.businessApplications.admin_notes,
        submitted_at: businessSchema.businessApplications.submitted_at,
        reviewed_at: businessSchema.businessApplications.reviewed_at,
        approved_business_id: businessSchema.businessApplications.approved_business_id,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        },
        processing_time_hours: sql<number>`
          CASE 
            WHEN ${businessSchema.businessApplications.reviewed_at} IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (${businessSchema.businessApplications.reviewed_at} - ${businessSchema.businessApplications.submitted_at})) / 3600
            ELSE NULL 
          END
        `,
      })
      .from(businessSchema.businessApplications)
      .leftJoin(businessSchema.categories, eq(businessSchema.businessApplications.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businessApplications.id, id))
      .limit(1);

    if (!application) {
      return c.json({ 
        error: 'Application not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // If approved, get the created business details
    let approvedBusiness = null;
    if (application.approved_business_id) {
      const [business] = await db
        .select({
          id: businessSchema.businesses.id,
          name: businessSchema.businesses.name,
          slug: businessSchema.businesses.slug,
          is_active: businessSchema.businesses.is_active,
          is_featured: businessSchema.businesses.is_featured,
          created_at: businessSchema.businesses.created_at,
        })
        .from(businessSchema.businesses)
        .where(eq(businessSchema.businesses.id, application.approved_business_id))
        .limit(1);
      
      approvedBusiness = business;
    }

    const response: AdminResponse<typeof application & { approved_business?: typeof approvedBusiness }> = {
      data: {
        ...application,
        approved_business: approvedBusiness,
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching application:', error);
    return c.json({ 
      error: 'Failed to fetch application',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Update Application (for admin notes, etc.)
applicationRoutes.put('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    const db = await getDatabase();

    // Check if application exists
    const [existingApplication] = await db
      .select()
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.id, id))
      .limit(1);

    if (!existingApplication) {
      return c.json({ 
        error: 'Application not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Update application (mainly for admin notes)
    const [updatedApplication] = await db
      .update(businessSchema.businessApplications)
      .set({
        admin_notes: body.admin_notes !== undefined ? body.admin_notes : existingApplication.admin_notes,
        // Don't allow status changes through this endpoint - use approve/reject endpoints
      })
      .where(eq(businessSchema.businessApplications.id, id))
      .returning();

    const response: AdminResponse<typeof updatedApplication> = {
      data: updatedApplication,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error updating application:', error);
    return c.json({ 
      error: 'Failed to update application',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get Application Analytics
applicationRoutes.get('/analytics/overview', async (c) => {
  try {
    const analytics = await calculateApplicationAnalytics();
    
    const response: AdminResponse<typeof analytics> = {
      data: analytics,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching application analytics:', error);
    return c.json({ 
      error: 'Failed to fetch application analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get Application Statistics by Status
applicationRoutes.get('/stats/by-status', async (c) => {
  try {
    const db = await getDatabase();

    const stats = await db
      .select({
        status: businessSchema.businessApplications.status,
        count: count(),
        avg_processing_time: sql<number>`
          AVG(
            CASE 
              WHEN ${businessSchema.businessApplications.reviewed_at} IS NOT NULL 
              THEN EXTRACT(EPOCH FROM (${businessSchema.businessApplications.reviewed_at} - ${businessSchema.businessApplications.submitted_at})) / 3600
              ELSE NULL 
            END
          )
        `,
      })
      .from(businessSchema.businessApplications)
      .groupBy(businessSchema.businessApplications.status);

    const response: AdminResponse<typeof stats> = {
      data: stats,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching application statistics:', error);
    return c.json({ 
      error: 'Failed to fetch application statistics',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

export default applicationRoutes;
