import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle, Clock, Mail, Home } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ApplicationSuccessProps {
  applicationId: string | null;
}

export function ApplicationSuccess({ applicationId }: ApplicationSuccessProps) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-6">
      <div className="max-w-2xl mx-auto text-center space-y-8">
        {/* Success Icon */}
        <div className="w-20 h-20 mx-auto bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
          <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
        </div>

        {/* Success Message */}
        <div className="space-y-4">
          <h1 className="text-3xl font-bold text-green-600 dark:text-green-400">
            Application Submitted Successfully!
          </h1>
          <p className="text-lg text-muted-foreground">
            Thank you for your interest in joining our business directory. 
            Your application has been received and is now under review.
          </p>
        </div>

        {/* Application Details */}
        {applicationId && (
          <Card className="text-left">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-4">Application Details</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-primary font-medium">#</span>
                  </div>
                  <div>
                    <p className="font-medium">Application ID</p>
                    <p className="text-muted-foreground">{applicationId}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Status</p>
                    <p className="text-muted-foreground">Pending Review</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <Mail className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Next Steps</p>
                    <p className="text-muted-foreground">
                      We'll email you within 2-3 business days with an update
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* What Happens Next */}
        <Card>
          <CardContent className="p-6 text-left">
            <h3 className="font-semibold mb-4">What Happens Next?</h3>
            <div className="space-y-4">
              <div className="flex gap-4">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
                </div>
                <div>
                  <h4 className="font-medium">Review Process</h4>
                  <p className="text-sm text-muted-foreground">
                    Our team will review your application to ensure it meets our quality standards.
                  </p>
                </div>
              </div>
              
              <div className="flex gap-4">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">2</span>
                </div>
                <div>
                  <h4 className="font-medium">Approval & Setup</h4>
                  <p className="text-sm text-muted-foreground">
                    Once approved, we'll create your business profile and send you the details.
                  </p>
                </div>
              </div>
              
              <div className="flex gap-4">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">3</span>
                </div>
                <div>
                  <h4 className="font-medium">Go Live</h4>
                  <p className="text-sm text-muted-foreground">
                    Your business will be visible to customers searching in your area.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild>
            <Link to="/">
              <Home className="w-4 h-4 mr-2" />
              Return to Homepage
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/categories">
              Browse Directory
            </Link>
          </Button>
        </div>

        {/* Contact Information */}
        <div className="text-sm text-muted-foreground">
          <p>
            Have questions about your application? 
            <br />
            Contact us at{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
