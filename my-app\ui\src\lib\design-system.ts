/**
 * Design System Utilities
 * 
 * This file provides TypeScript utilities and constants for using the design system
 * consistently across the application.
 */

// Typography Scale
export const typography = {
  sizes: {
    xs: 'text-xs',
    sm: 'text-sm', 
    base: 'text-base',
    md: 'text-md',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
    '4xl': 'text-4xl',
  },
  families: {
    sans: 'font-sans',
    serif: 'font-serif',
    mono: 'font-mono',
  },
  semantic: {
    h1: 'h1',
    h2: 'h2', 
    h3: 'h3',
    h4: 'h4',
    h5: 'h5',
    body: 'body-text',
    caption: 'caption',
    label: 'label',
  }
} as const;

// Spacing Scale (8pt grid)
export const spacing = {
  0: 'space-0',   // 0px
  1: 'space-1',   // 8px
  2: 'space-2',   // 16px
  3: 'space-3',   // 24px
  4: 'space-4',   // 32px
  5: 'space-5',   // 40px
  6: 'space-6',   // 48px
  7: 'space-7',   // 56px
  8: 'space-8',   // 64px
  9: 'space-9',   // 72px
  10: 'space-10', // 80px
} as const;

// Color Tokens
export const colors = {
  primary: 'text-primary',
  secondary: 'text-secondary',
  accent: 'text-accent',
  muted: 'text-muted-foreground',
  destructive: 'text-destructive',
  background: 'bg-background',
  foreground: 'text-foreground',
  card: 'bg-card',
  border: 'border-border',
} as const;

// Shadow Scale
export const shadows = {
  '2xs': 'shadow-2xs',
  xs: 'shadow-xs',
  sm: 'shadow-sm',
  default: 'shadow',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
  '2xl': 'shadow-2xl',
} as const;

// Border Radius
export const radius = {
  sm: 'rounded-sm',
  md: 'rounded-md', 
  lg: 'rounded-lg',
  xl: 'rounded-xl',
} as const;

// Design System Component Variants
export const variants = {
  button: {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  },
  card: {
    default: 'bg-card text-card-foreground border shadow-sm',
    elevated: 'bg-card text-card-foreground border shadow-lg',
  },
  badge: {
    default: 'bg-primary text-primary-foreground',
    secondary: 'bg-secondary text-secondary-foreground',
    outline: 'border text-foreground',
    destructive: 'bg-destructive text-destructive-foreground',
  }
} as const;

// Utility Functions
export const designSystem = {
  /**
   * Get typography class for semantic text elements
   */
  text: (variant: keyof typeof typography.semantic) => typography.semantic[variant],
  
  /**
   * Get spacing class for consistent layout
   */
  space: (size: keyof typeof spacing) => spacing[size],
  
  /**
   * Get color class for consistent theming
   */
  color: (variant: keyof typeof colors) => colors[variant],
  
  /**
   * Get shadow class for elevation
   */
  shadow: (size: keyof typeof shadows) => shadows[size],
  
  /**
   * Get border radius class
   */
  radius: (size: keyof typeof radius) => radius[size],
  
  /**
   * Combine multiple design system classes
   */
  combine: (...classes: string[]) => classes.filter(Boolean).join(' '),
} as const;

// CSS Custom Properties (for use in CSS-in-JS or direct CSS)
export const cssVariables = {
  colors: {
    primary: 'var(--primary)',
    secondary: 'var(--secondary)',
    accent: 'var(--accent)',
    background: 'var(--background)',
    foreground: 'var(--foreground)',
    muted: 'var(--muted)',
    mutedForeground: 'var(--muted-foreground)',
    border: 'var(--border)',
    destructive: 'var(--destructive)',
  },
  spacing: {
    0: 'var(--space-0)',
    1: 'var(--space-1)',
    2: 'var(--space-2)',
    3: 'var(--space-3)',
    4: 'var(--space-4)',
    5: 'var(--space-5)',
    6: 'var(--space-6)',
    7: 'var(--space-7)',
    8: 'var(--space-8)',
    9: 'var(--space-9)',
    10: 'var(--space-10)',
  },
  typography: {
    fontSizes: {
      xs: 'var(--font-size-xs)',
      sm: 'var(--font-size-sm)',
      base: 'var(--font-size-base)',
      md: 'var(--font-size-md)',
      lg: 'var(--font-size-lg)',
      xl: 'var(--font-size-xl)',
      '2xl': 'var(--font-size-2xl)',
      '3xl': 'var(--font-size-3xl)',
      '4xl': 'var(--font-size-4xl)',
    },
    fontFamilies: {
      sans: 'var(--font-family-sans)',
      serif: 'var(--font-family-serif)',
      mono: 'var(--font-family-mono)',
    }
  },
  shadows: {
    '2xs': 'var(--shadow-2xs)',
    xs: 'var(--shadow-xs)',
    sm: 'var(--shadow-sm)',
    default: 'var(--shadow)',
    md: 'var(--shadow-md)',
    lg: 'var(--shadow-lg)',
    xl: 'var(--shadow-xl)',
    '2xl': 'var(--shadow-2xl)',
  }
} as const;

// Type exports for better TypeScript support
export type TypographySize = keyof typeof typography.sizes;
export type TypographySemantic = keyof typeof typography.semantic;
export type SpacingSize = keyof typeof spacing;
export type ColorVariant = keyof typeof colors;
export type ShadowSize = keyof typeof shadows;
export type RadiusSize = keyof typeof radius;

// Example usage:
// import { designSystem, typography, spacing } from '@/lib/design-system';
// 
// const className = designSystem.combine(
//   typography.semantic.h1,
//   colors.primary,
//   spacing[4]
// );
