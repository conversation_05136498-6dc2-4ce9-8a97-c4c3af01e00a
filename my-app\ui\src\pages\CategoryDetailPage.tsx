import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/serverComm';
import { BusinessCard } from '@/components/business/BusinessCard';
import { ArrowLeft, Store } from 'lucide-react';

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
}

interface Business {
  id: number;
  name: string;
  slug: string;
  short_description: string;
  address: string;
  latitude: string;
  longitude: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: string;
}

interface CategoryDetailResponse {
  category: Category;
  businesses: Business[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function CategoryDetailPage() {
  const { slug } = useParams<{ slug: string }>();
  const [data, setData] = useState<CategoryDetailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    async function fetchCategoryData() {
      if (!slug) return;
      
      try {
        setLoading(true);
        const response = await api.getCategoryBusinesses(slug, {
          page: currentPage,
          limit: 12
        });
        setData(response);
        setError(null);
      } catch (err) {
        setError('Failed to load category data');
        console.error('Error fetching category data:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchCategoryData();
  }, [slug, currentPage]);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <div className="w-8 h-8 bg-muted rounded animate-pulse"></div>
            <div className="h-8 bg-muted rounded w-48 animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-muted"></div>
                <CardContent className="p-6">
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-3 bg-muted rounded mb-4"></div>
                  <div className="h-3 bg-muted rounded w-24"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">Category Not Found</h1>
          <p className="text-muted-foreground">{error || 'The requested category could not be found.'}</p>
          <Button asChild>
            <Link to="/categories">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Categories
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  const { category, businesses, pagination } = data;

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-8">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link to="/categories">
                <ArrowLeft className="w-4 h-4 mr-2" />
                All Categories
              </Link>
            </Button>
          </div>
          
          <div className="flex items-start gap-6">
            <div className="w-16 h-16 flex items-center justify-center rounded-full bg-primary/10 text-primary">
              <Store className="w-8 h-8" />
            </div>
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">{category.name}</h1>
              {category.description && (
                <p className="text-muted-foreground text-lg mb-4">
                  {category.description}
                </p>
              )}
              <Badge variant="secondary">
                {pagination.total} {pagination.total === 1 ? 'business' : 'businesses'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Businesses Grid */}
        {businesses.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {businesses.map((business) => (
                <BusinessCard key={business.id} business={business} />
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                
                <span className="text-sm text-muted-foreground">
                  Page {currentPage} of {pagination.pages}
                </span>
                
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(pagination.pages, prev + 1))}
                  disabled={currentPage === pagination.pages}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <Store className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">No businesses found</h3>
            <p className="text-muted-foreground mb-6">
              There are currently no businesses in the {category.name} category.
            </p>
            <Button asChild>
              <Link to="/apply">List Your Business</Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
