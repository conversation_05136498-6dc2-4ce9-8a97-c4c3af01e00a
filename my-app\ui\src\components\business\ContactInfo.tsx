import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Phone, Mail, Globe, MapPin, Navigation } from 'lucide-react';

interface ContactInfoProps {
  business: {
    phone: string;
    email: string;
    website: string;
    address: string;
    latitude: string;
    longitude: string;
  };
}

export function ContactInfo({ business }: ContactInfoProps) {
  const handleCall = () => {
    if (business.phone) {
      window.location.href = `tel:${business.phone}`;
    }
  };

  const handleEmail = () => {
    if (business.email) {
      window.location.href = `mailto:${business.email}`;
    }
  };

  const handleWebsite = () => {
    if (business.website) {
      const url = business.website.startsWith('http') 
        ? business.website 
        : `https://${business.website}`;
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  const handleDirections = () => {
    if (business.latitude && business.longitude) {
      const url = `https://www.google.com/maps/dir/?api=1&destination=${business.latitude},${business.longitude}`;
      window.open(url, '_blank', 'noopener,noreferrer');
    } else if (business.address) {
      const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(business.address)}`;
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Contact Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Phone */}
        {business.phone && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">{business.phone}</span>
            </div>
            <Button size="sm" variant="outline" onClick={handleCall}>
              Call
            </Button>
          </div>
        )}

        {/* Email */}
        {business.email && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Mail className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm">{business.email}</span>
            </div>
            <Button size="sm" variant="outline" onClick={handleEmail}>
              Email
            </Button>
          </div>
        )}

        {/* Website */}
        {business.website && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Globe className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm truncate">
                {business.website.replace(/^https?:\/\//, '')}
              </span>
            </div>
            <Button size="sm" variant="outline" onClick={handleWebsite}>
              Visit
            </Button>
          </div>
        )}

        {/* Address */}
        <div className="pt-2 border-t">
          <div className="flex items-start gap-3 mb-3">
            <MapPin className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <span className="text-sm">{business.address}</span>
          </div>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleDirections}
            className="w-full"
          >
            <Navigation className="w-4 h-4 mr-2" />
            Get Directions
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
