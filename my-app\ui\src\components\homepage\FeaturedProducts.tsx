import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ShoppingBag } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  price: number;
  imageUrl: string;
  business: {
    name: string;
    slug: string;
  };
  category?: string;
}

export function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Mock data for featured products since we don't have a products API yet
  const mockProducts: Product[] = [
    {
      id: 1,
      name: "Belizean Coffee Blend",
      price: 24.99,
      imageUrl: "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop",
      business: { name: "Maya Mountain Coffee", slug: "maya-mountain-coffee" },
      category: "Food & Beverages"
    },
    {
      id: 2,
      name: "Handwoven Mayan Textile",
      price: 89.99,
      imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop",
      business: { name: "Artisan Crafts Belize", slug: "artisan-crafts-belize" },
      category: "Arts & Crafts"
    },
    {
      id: 3,
      name: "Coconut Rum Cake",
      price: 18.50,
      imageUrl: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop",
      business: { name: "Sweet Delights Bakery", slug: "sweet-delights-bakery" },
      category: "Food & Beverages"
    },
    {
      id: 4,
      name: "Snorkeling Gear Set",
      price: 125.00,
      imageUrl: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop",
      business: { name: "Barrier Reef Sports", slug: "barrier-reef-sports" },
      category: "Sports & Recreation"
    },
    {
      id: 5,
      name: "Belizean Hot Sauce",
      price: 12.99,
      imageUrl: "https://images.unsplash.com/photo-1583623025817-d180a2221d0a?w=400&h=300&fit=crop",
      business: { name: "Spice Island Co.", slug: "spice-island-co" },
      category: "Food & Beverages"
    },
    {
      id: 6,
      name: "Jade Jewelry Set",
      price: 156.00,
      imageUrl: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=300&fit=crop",
      business: { name: "Jade Maya Jewelry", slug: "jade-maya-jewelry" },
      category: "Jewelry & Accessories"
    }
  ];

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setProducts(mockProducts);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const scrollLeft = () => {
    const container = document.getElementById('products-carousel');
    if (container) {
      container.scrollBy({ left: -320, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    const container = document.getElementById('products-carousel');
    if (container) {
      container.scrollBy({ left: 320, behavior: 'smooth' });
    }
  };

  if (loading) {
    return (
      <section className="py-12 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Featured Products</h2>
            <p className="text-muted-foreground text-lg">Discover amazing products from local businesses</p>
          </div>
          
          <div className="relative">
            <div className="flex gap-6 overflow-hidden">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="flex-shrink-0 w-80 animate-pulse">
                  <div className="h-48 bg-muted"></div>
                  <CardContent className="p-6">
                    <div className="h-4 bg-muted rounded mb-2"></div>
                    <div className="h-3 bg-muted rounded mb-4 w-3/4"></div>
                    <div className="h-6 bg-muted rounded w-20"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-12 px-4 bg-muted/30">
      <div className="container mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">Featured Products</h2>
          <p className="text-muted-foreground text-lg">Discover amazing products from local businesses</p>
        </div>
        
        <div className="relative">
          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 backdrop-blur-sm"
            onClick={scrollLeft}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-background/80 backdrop-blur-sm"
            onClick={scrollRight}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Products Carousel */}
          <div
            id="products-carousel"
            className="flex gap-6 overflow-x-auto scrollbar-hide snap-x snap-mandatory pb-4 px-12"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {products.map((product) => (
              <Link
                key={product.id}
                to={`/business/${product.business.slug}`}
                className="flex-shrink-0 snap-center group"
              >
                <Card className="w-80 overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                  {/* Product Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                    {product.category && (
                      <Badge className="absolute top-3 left-3 bg-primary/90 text-xs">
                        {product.category}
                      </Badge>
                    )}
                  </div>

                  <CardContent className="p-6">
                    <div className="space-y-3">
                      {/* Product Name */}
                      <h3 className="font-semibold text-lg group-hover:text-primary transition-colors line-clamp-1">
                        {product.name}
                      </h3>
                      
                      {/* Business Name */}
                      <p className="text-sm text-muted-foreground">
                        from {product.business.name}
                      </p>
                      
                      {/* Price */}
                      <div className="flex items-center justify-between">
                        <p className="text-2xl font-bold text-primary">
                          ${product.price.toFixed(2)}
                        </p>
                        <Button size="sm" className="gap-2">
                          <ShoppingBag className="h-4 w-4" />
                          View
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
