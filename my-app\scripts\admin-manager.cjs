#!/usr/bin/env node

/**
 * Admin Management Script for Firebase Emulator
 * 
 * Commands:
 *   make-admin <email>     - Make a user an admin
 *   remove-admin <email>   - Remove admin privileges from a user
 *   list-admins           - List all admin users
 *   list-users            - List all users
 * 
 * Usage Examples:
 *   node scripts/admin-manager.cjs make-admin <EMAIL>
 *   node scripts/admin-manager.cjs remove-admin <EMAIL>
 *   node scripts/admin-manager.cjs list-admins
 *   node scripts/admin-manager.cjs list-users
 */

const { readFileSync, writeFileSync, existsSync } = require('fs');
const { join, dirname } = require('path');

const scriptDir = dirname(require.main.filename);
const projectRoot = join(scriptDir, '..');

/**
 * Find the latest Firebase emulator export directory
 */
function findLatestFirebaseExport() {
  const { readdirSync, statSync } = require('fs');
  
  try {
    const files = readdirSync(projectRoot);
    const exportDirs = files
      .filter(file => file.startsWith('firebase-export-'))
      .map(dir => ({
        name: dir,
        path: join(projectRoot, dir),
        mtime: statSync(join(projectRoot, dir)).mtime
      }))
      .sort((a, b) => b.mtime - a.mtime);
    
    if (exportDirs.length === 0) {
      throw new Error('No Firebase export directories found');
    }
    
    return exportDirs[0].path;
  } catch (error) {
    console.error('Error finding Firebase export directory:', error.message);
    process.exit(1);
  }
}

/**
 * Load Firebase accounts data
 */
function loadAccounts() {
  const exportDir = findLatestFirebaseExport();
  const accountsPath = join(exportDir, 'auth_export', 'accounts.json');
  
  if (!existsSync(accountsPath)) {
    throw new Error(`Firebase accounts file not found at: ${accountsPath}`);
  }
  
  return {
    data: JSON.parse(readFileSync(accountsPath, 'utf8')),
    path: accountsPath
  };
}

/**
 * Save Firebase accounts data
 */
function saveAccounts(accountsData, accountsPath) {
  writeFileSync(accountsPath, JSON.stringify(accountsData, null, 2));
}

/**
 * Make a user an admin
 */
function makeUserAdmin(email) {
  try {
    const { data: accountsData, path: accountsPath } = loadAccounts();
    
    const user = accountsData.users.find(u => u.email === email);
    
    if (!user) {
      throw new Error(`User with email ${email} not found in Firebase emulator`);
    }
    
    console.log(`Found user: ${user.email} (ID: ${user.localId})`);
    
    // Add custom claims for admin
    if (!user.customClaims) {
      user.customClaims = {};
    }
    
    user.customClaims.admin = true;
    user.customClaims.roles = ['admin'];
    user.customClaims.permissions = ['admin.access', 'admin.businesses', 'admin.applications', 'admin.reviews'];
    
    saveAccounts(accountsData, accountsPath);
    
    console.log(`✅ Successfully made ${email} an admin!`);
    console.log('Custom claims added:');
    console.log('  - admin: true');
    console.log('  - roles: ["admin"]');
    console.log('  - permissions: ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]');
    
  } catch (error) {
    console.error('❌ Error making user admin:', error.message);
    process.exit(1);
  }
}

/**
 * Remove admin privileges from a user
 */
function removeUserAdmin(email) {
  try {
    const { data: accountsData, path: accountsPath } = loadAccounts();
    
    const user = accountsData.users.find(u => u.email === email);
    
    if (!user) {
      throw new Error(`User with email ${email} not found in Firebase emulator`);
    }
    
    console.log(`Found user: ${user.email} (ID: ${user.localId})`);
    
    // Remove admin custom claims
    if (user.customClaims) {
      delete user.customClaims.admin;
      delete user.customClaims.roles;
      delete user.customClaims.permissions;
      
      // Remove customClaims object if empty
      if (Object.keys(user.customClaims).length === 0) {
        delete user.customClaims;
      }
    }
    
    saveAccounts(accountsData, accountsPath);
    
    console.log(`✅ Successfully removed admin privileges from ${email}!`);
    
  } catch (error) {
    console.error('❌ Error removing admin privileges:', error.message);
    process.exit(1);
  }
}

/**
 * List all admin users
 */
function listAdmins() {
  try {
    const { data: accountsData } = loadAccounts();
    
    const admins = accountsData.users.filter(user => 
      user.customClaims && user.customClaims.admin === true
    );
    
    console.log(`\n📋 Admin Users (${admins.length} found):`);
    console.log('=' .repeat(50));
    
    if (admins.length === 0) {
      console.log('No admin users found.');
    } else {
      admins.forEach((user, index) => {
        console.log(`${index + 1}. ${user.email}`);
        console.log(`   ID: ${user.localId}`);
        console.log(`   Created: ${new Date(parseInt(user.createdAt)).toLocaleString()}`);
        console.log(`   Last Login: ${new Date(parseInt(user.lastLoginAt)).toLocaleString()}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Error listing admins:', error.message);
    process.exit(1);
  }
}

/**
 * List all users
 */
function listUsers() {
  try {
    const { data: accountsData } = loadAccounts();
    
    console.log(`\n📋 All Users (${accountsData.users.length} found):`);
    console.log('=' .repeat(50));
    
    accountsData.users.forEach((user, index) => {
      const isAdmin = user.customClaims && user.customClaims.admin === true;
      console.log(`${index + 1}. ${user.email} ${isAdmin ? '👑 (Admin)' : ''}`);
      console.log(`   ID: ${user.localId}`);
      console.log(`   Created: ${new Date(parseInt(user.createdAt)).toLocaleString()}`);
      console.log(`   Last Login: ${new Date(parseInt(user.lastLoginAt)).toLocaleString()}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Error listing users:', error.message);
    process.exit(1);
  }
}

/**
 * Show help
 */
function showHelp() {
  console.log(`
🔧 Admin Management Script for Firebase Emulator

Commands:
  make-admin <email>     - Make a user an admin
  remove-admin <email>   - Remove admin privileges from a user  
  list-admins           - List all admin users
  list-users            - List all users
  help                  - Show this help message

Usage Examples:
  node scripts/admin-manager.cjs make-admin <EMAIL>
  node scripts/admin-manager.cjs remove-admin <EMAIL>
  node scripts/admin-manager.cjs list-admins
  node scripts/admin-manager.cjs list-users

⚠️  Note: After making changes, you may need to restart the Firebase emulator
   and users will need to sign out and sign back in to get new tokens.
`);
}

/**
 * Main function
 */
function main() {
  const command = process.argv[2];
  const email = process.argv[3];
  
  switch (command) {
    case 'make-admin':
      if (!email) {
        console.error('❌ Email required for make-admin command');
        console.error('Usage: node scripts/admin-manager.cjs make-admin <email>');
        process.exit(1);
      }
      console.log(`Making ${email} an admin...`);
      makeUserAdmin(email);
      break;
      
    case 'remove-admin':
      if (!email) {
        console.error('❌ Email required for remove-admin command');
        console.error('Usage: node scripts/admin-manager.cjs remove-admin <email>');
        process.exit(1);
      }
      console.log(`Removing admin privileges from ${email}...`);
      removeUserAdmin(email);
      break;
      
    case 'list-admins':
      listAdmins();
      break;
      
    case 'list-users':
      listUsers();
      break;
      
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
      
    default:
      console.error('❌ Unknown command:', command);
      showHelp();
      process.exit(1);
  }
  
  if (command !== 'help' && command !== '--help' && command !== '-h') {
    console.log('\n⚠️  Remember: You may need to restart the Firebase emulator for changes to take effect.');
    console.log('   Users will need to sign out and sign back in to get new tokens.');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}
