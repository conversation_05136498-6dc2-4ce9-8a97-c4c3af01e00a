// Admin routes for the backend API
import { Hono } from 'hono';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import { adminMiddleware } from '../middleware/auth';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import { calculateDashboardStats, calculateBusinessTrends, calculateCategoryPerformance, calculateApplicationAnalytics } from '../lib/analytics';
import { bulkBusinessOperations, bulkReviewOperations, bulkApplicationOperations, exportBusinessesToCSV, importBusinessesFromCSV } from '../lib/bulk-operations';
import type { AdminResponse, AdminBusinessFilters, ReviewModerationFilters, AdminApplicationFilters } from '../types/admin';

// Create admin routes with admin middleware
const adminRoutes = new Hono();
adminRoutes.use('*', adminMiddleware);

// ===== DASHBOARD ANALYTICS =====
// Dashboard Statistics
adminRoutes.get('/dashboard/stats', async (c) => {
  try {
    const stats = await calculateDashboardStats();
    
    const response: AdminResponse<typeof stats> = {
      data: stats,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
    
    return c.json(response);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return c.json({ 
      error: 'Failed to fetch dashboard statistics',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Business Performance Trends
adminRoutes.get('/analytics/trends', async (c) => {
  try {
    const months = parseInt(c.req.query('months') || '12');
    const trends = await calculateBusinessTrends(months);
    
    const response: AdminResponse<typeof trends> = {
      data: trends,
      meta: {
        filters: { months },
        timestamp: new Date().toISOString(),
      },
    };
    
    return c.json(response);
  } catch (error) {
    console.error('Error fetching business trends:', error);
    return c.json({ 
      error: 'Failed to fetch business trends',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Category Performance
adminRoutes.get('/analytics/categories', async (c) => {
  try {
    const performance = await calculateCategoryPerformance();
    
    const response: AdminResponse<typeof performance> = {
      data: performance,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
    
    return c.json(response);
  } catch (error) {
    console.error('Error fetching category performance:', error);
    return c.json({ 
      error: 'Failed to fetch category performance',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Application Analytics
adminRoutes.get('/analytics/applications', async (c) => {
  try {
    const analytics = await calculateApplicationAnalytics();
    
    const response: AdminResponse<typeof analytics> = {
      data: analytics,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
    
    return c.json(response);
  } catch (error) {
    console.error('Error fetching application analytics:', error);
    return c.json({ 
      error: 'Failed to fetch application analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// ===== BULK OPERATIONS =====
// Bulk Business Operations
adminRoutes.post('/bulk/businesses', async (c) => {
  try {
    const body = await c.req.json();
    const user = c.get('user');
    
    if (!body.operation || !body.ids || !Array.isArray(body.ids)) {
      return c.json({ 
        error: 'Missing required fields: operation, ids',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const result = await bulkBusinessOperations(body, user.id);
    
    const response: AdminResponse<typeof result> = {
      data: result,
      meta: {
        operation: body.operation,
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error performing bulk business operation:', error);
    return c.json({ 
      error: 'Failed to perform bulk operation',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Bulk Review Operations
adminRoutes.post('/bulk/reviews', async (c) => {
  try {
    const body = await c.req.json();
    const user = c.get('user');
    
    if (!body.operation || !body.ids || !Array.isArray(body.ids)) {
      return c.json({ 
        error: 'Missing required fields: operation, ids',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const result = await bulkReviewOperations(body, user.id);
    
    const response: AdminResponse<typeof result> = {
      data: result,
      meta: {
        operation: body.operation,
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error performing bulk review operation:', error);
    return c.json({ 
      error: 'Failed to perform bulk operation',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Bulk Application Operations
adminRoutes.post('/bulk/applications', async (c) => {
  try {
    const body = await c.req.json();
    const user = c.get('user');
    
    if (!body.operation || !body.ids || !Array.isArray(body.ids)) {
      return c.json({ 
        error: 'Missing required fields: operation, ids',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const result = await bulkApplicationOperations(body, user.id);
    
    const response: AdminResponse<typeof result> = {
      data: result,
      meta: {
        operation: body.operation,
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error performing bulk application operation:', error);
    return c.json({ 
      error: 'Failed to perform bulk operation',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Export Businesses
adminRoutes.get('/export/businesses', async (c) => {
  try {
    const format = c.req.query('format') || 'csv';
    const status = c.req.query('status');
    
    if (format !== 'csv') {
      return c.json({ 
        error: 'Only CSV format is currently supported',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const csvData = await exportBusinessesToCSV({ status });
    
    return new Response(csvData, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="businesses-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });
  } catch (error) {
    console.error('Error exporting businesses:', error);
    return c.json({ 
      error: 'Failed to export businesses',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Import Businesses
adminRoutes.post('/import/businesses', async (c) => {
  try {
    const body = await c.req.json();
    const user = c.get('user');
    
    if (!body.csvData) {
      return c.json({ 
        error: 'Missing required field: csvData',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    const result = await importBusinessesFromCSV(body.csvData, user.id);
    
    const response: AdminResponse<typeof result> = {
      data: result,
      meta: {
        operation: 'import',
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error importing businesses:', error);
    return c.json({ 
      error: 'Failed to import businesses',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

export default adminRoutes;
