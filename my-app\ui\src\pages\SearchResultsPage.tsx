import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { SearchBar } from '@/components/search/SearchBar';
import { SearchResults } from '@/components/search/SearchResults';
import { FilterPanel } from '@/components/search/FilterPanel';
import { BusinessCard } from '@/components/business/BusinessCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSearch } from '@/hooks/useSearch';
import { useCategories } from '@/hooks/useCategories';
import { Skeleton } from '@/components/ui/skeleton';
import { MapPin, Filter, Grid, List, Map } from 'lucide-react';

type ViewMode = 'grid' | 'list' | 'map';

export function SearchResultsPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [showFilters, setShowFilters] = useState(false);
  
  const query = searchParams.get('q') || '';
  const location = searchParams.get('location') || '';
  const category = searchParams.get('category') || '';
  const page = parseInt(searchParams.get('page') || '1');
  
  const { businesses, loading, error, total, totalPages, search } = useSearch();
  const { categories } = useCategories();
  
  // Perform search when URL parameters change
  useEffect(() => {
    if (query) {
      search({
        query,
        page,
        category: category || undefined,
        limit: 12,
      });
    }
  }, [query, page, category]);

  const handleSearch = (newQuery: string, newLocation?: string) => {
    const params = new URLSearchParams();
    params.set('q', newQuery);
    if (newLocation) params.set('location', newLocation);
    if (category) params.set('category', category);
    setSearchParams(params);
  };

  const handleCategoryFilter = (categorySlug: string) => {
    const params = new URLSearchParams();
    params.set('q', query);
    if (location) params.set('location', location);
    if (categorySlug) params.set('category', categorySlug);
    setSearchParams(params);
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams();
    params.set('q', query);
    if (location) params.set('location', location);
    if (category) params.set('category', category);
    params.set('page', newPage.toString());
    setSearchParams(params);
  };

  const clearFilters = () => {
    const params = new URLSearchParams();
    params.set('q', query);
    if (location) params.set('location', location);
    setSearchParams(params);
  };

  if (!query) {
    return (
      <div className="min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <h1 className="text-2xl font-bold mb-4">Search Businesses</h1>
            <p className="text-muted-foreground mb-8">
              Find local businesses, services, and more
            </p>
            <SearchBar onSearch={handleSearch} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-muted/30">
      {/* Search Header */}
      <div className="bg-background border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-4xl mx-auto">
            <SearchBar
              initialQuery={query}
              initialLocation={location}
              onSearch={handleSearch}
              showAutocomplete={false}
            />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <div className={`lg:w-64 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <Card className="sticky top-6">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Filters</h3>
                  {category && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-xs"
                    >
                      Clear
                    </Button>
                  )}
                </div>
                
                {/* Category Filter */}
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Categories</h4>
                  <div className="space-y-2">
                    {categories?.slice(0, 10).map((cat) => (
                      <button
                        key={cat.id}
                        onClick={() => handleCategoryFilter(cat.slug)}
                        className={`w-full text-left px-3 py-2 rounded-md transition-colors text-sm ${
                          category === cat.slug
                            ? 'bg-primary text-primary-foreground'
                            : 'hover:bg-muted'
                        }`}
                      >
                        {cat.name}
                      </button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6">
              <div className="mb-4 sm:mb-0">
                <h1 className="text-2xl font-bold mb-1">
                  Search Results
                </h1>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>
                    {loading ? 'Searching...' : `${total} results found`}
                  </span>
                  {query && (
                    <>
                      <span>for</span>
                      <Badge variant="secondary">"{query}"</Badge>
                    </>
                  )}
                  {category && (
                    <>
                      <span>in</span>
                      <Badge variant="outline">
                        {categories?.find(c => c.slug === category)?.name || category}
                      </Badge>
                    </>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Mobile Filter Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </Button>

                {/* View Mode Toggle */}
                <div className="flex border rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'map' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('map')}
                    className="rounded-l-none"
                  >
                    <Map className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Error State */}
            {error && (
              <Card className="p-6 text-center">
                <p className="text-muted-foreground">{error}</p>
                <Button
                  variant="outline"
                  onClick={() => search({ query, page: 1 })}
                  className="mt-4"
                >
                  Try Again
                </Button>
              </Card>
            )}

            {/* Loading State */}
            {loading && (
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
                  : 'grid-cols-1'
              }`}>
                {Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <div className="h-48 bg-muted"></div>
                    <CardContent className="p-4">
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-1/2 mb-2" />
                      <Skeleton className="h-3 w-full" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Results Grid/List */}
            {!loading && !error && businesses.length > 0 && (
              <>
                <div className={`grid gap-6 ${
                  viewMode === 'grid' 
                    ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
                    : 'grid-cols-1'
                }`}>
                  {businesses.map((business) => (
                    <BusinessCard
                      key={business.id}
                      business={business}
                      layout={viewMode === 'list' ? 'horizontal' : 'vertical'}
                    />
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(page - 1)}
                        disabled={page <= 1}
                      >
                        Previous
                      </Button>
                      
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const pageNum = i + 1;
                          return (
                            <Button
                              key={pageNum}
                              variant={page === pageNum ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>
                      
                      <Button
                        variant="outline"
                        onClick={() => handlePageChange(page + 1)}
                        disabled={page >= totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}

            {/* No Results */}
            {!loading && !error && businesses.length === 0 && (
              <Card className="p-8 text-center">
                <div className="max-w-md mx-auto">
                  <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No results found</h3>
                  <p className="text-muted-foreground mb-4">
                    We couldn't find any businesses matching your search. Try adjusting your search terms or filters.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2 justify-center">
                    <Button
                      variant="outline"
                      onClick={clearFilters}
                    >
                      Clear Filters
                    </Button>
                    <Button
                      onClick={() => navigate('/businesses')}
                    >
                      Browse All Businesses
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}