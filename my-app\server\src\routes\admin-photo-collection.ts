/**
 * Admin Photo Collection Routes
 * Handles automated photo collection and approval workflow
 */

import { Hono } from 'hono';
import { photoCollectionService } from '../services/photo-collection';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import { eq, and } from 'drizzle-orm';

const adminPhotoRoutes = new Hono();

// Trigger photo collection for a specific business
adminPhotoRoutes.post('/businesses/:id/collect-photos', async (c) => {
  try {
    const businessId = parseInt(c.req.param('id'));
    const db = await getDatabase();

    // Get business details
    const [business] = await db
      .select()
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.id, businessId));

    if (!business) {
      return c.json({
        success: false,
        error: 'Business not found'
      }, 404);
    }

    // Collect photos
    const result = await photoCollectionService.collectBusinessPhotos({
      id: business.id,
      name: business.name,
      address: business.address,
      latitude: business.latitude,
      longitude: business.longitude,
    });

    return c.json({
      success: result.success,
      data: {
        business_id: businessId,
        photos_collected: result.photosCollected,
        errors: result.errors,
      }
    });

  } catch (error) {
    console.error('Error collecting photos:', error);
    return c.json({
      success: false,
      error: 'Failed to collect photos'
    }, 500);
  }
});

// Get pending photos for review
adminPhotoRoutes.get('/photos/pending', async (c) => {
  try {
    const businessId = c.req.query('business_id');
    const pendingPhotos = await photoCollectionService.getPendingPhotos(
      businessId ? parseInt(businessId) : undefined
    );

    return c.json({
      success: true,
      data: {
        pending_photos: pendingPhotos
      }
    });

  } catch (error) {
    console.error('Error fetching pending photos:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch pending photos'
    }, 500);
  }
});

// Approve a photo
adminPhotoRoutes.put('/photos/:id/approve', async (c) => {
  try {
    const photoId = parseInt(c.req.param('id'));
    const success = await photoCollectionService.approvePhoto(photoId);

    if (success) {
      return c.json({
        success: true,
        data: {
          message: 'Photo approved successfully'
        }
      });
    } else {
      return c.json({
        success: false,
        error: 'Failed to approve photo'
      }, 500);
    }

  } catch (error) {
    console.error('Error approving photo:', error);
    return c.json({
      success: false,
      error: 'Failed to approve photo'
    }, 500);
  }
});

// Reject a photo
adminPhotoRoutes.delete('/photos/:id/reject', async (c) => {
  try {
    const photoId = parseInt(c.req.param('id'));
    const success = await photoCollectionService.rejectPhoto(photoId);

    if (success) {
      return c.json({
        success: true,
        data: {
          message: 'Photo rejected successfully'
        }
      });
    } else {
      return c.json({
        success: false,
        error: 'Failed to reject photo'
      }, 500);
    }

  } catch (error) {
    console.error('Error rejecting photo:', error);
    return c.json({
      success: false,
      error: 'Failed to reject photo'
    }, 500);
  }
});

// Bulk approve all photos for a business
adminPhotoRoutes.put('/businesses/:id/photos/approve-all', async (c) => {
  try {
    const businessId = parseInt(c.req.param('id'));
    const db = await getDatabase();

    // Approve all pending photos for this business
    await db
      .update(businessSchema.businessImages)
      .set({ is_approved: true })
      .where(
        and(
          eq(businessSchema.businessImages.business_id, businessId),
          eq(businessSchema.businessImages.is_approved, false)
        )
      );

    return c.json({
      success: true,
      data: {
        message: 'All photos approved for business'
      }
    });

  } catch (error) {
    console.error('Error bulk approving photos:', error);
    return c.json({
      success: false,
      error: 'Failed to approve photos'
    }, 500);
  }
});

// Set photo as primary
adminPhotoRoutes.put('/photos/:id/set-primary', async (c) => {
  try {
    const photoId = parseInt(c.req.param('id'));
    const db = await getDatabase();

    // Get the photo to find business_id
    const [photo] = await db
      .select()
      .from(businessSchema.businessImages)
      .where(eq(businessSchema.businessImages.id, photoId));

    if (!photo) {
      return c.json({
        success: false,
        error: 'Photo not found'
      }, 404);
    }

    // Remove primary flag from all photos for this business
    await db
      .update(businessSchema.businessImages)
      .set({ is_primary: false })
      .where(eq(businessSchema.businessImages.business_id, photo.business_id));

    // Set this photo as primary
    await db
      .update(businessSchema.businessImages)
      .set({ is_primary: true, is_approved: true })
      .where(eq(businessSchema.businessImages.id, photoId));

    return c.json({
      success: true,
      data: {
        message: 'Photo set as primary'
      }
    });

  } catch (error) {
    console.error('Error setting primary photo:', error);
    return c.json({
      success: false,
      error: 'Failed to set primary photo'
    }, 500);
  }
});

// Manual photo addition
adminPhotoRoutes.post('/businesses/:id/photos/manual', async (c) => {
  try {
    const businessId = parseInt(c.req.param('id'));
    const body = await c.req.json();

    if (!body.image_url) {
      return c.json({
        success: false,
        error: 'image_url is required'
      }, 400);
    }

    const db = await getDatabase();

    const [newPhoto] = await db
      .insert(businessSchema.businessImages)
      .values({
        business_id: businessId,
        image_url: body.image_url,
        alt_text: body.alt_text || null,
        photo_source: 'manual',
        is_approved: true, // Manual photos are auto-approved
        display_order: body.display_order || 0,
      })
      .returning();

    return c.json({
      success: true,
      data: {
        photo: newPhoto,
        message: 'Photo added successfully'
      }
    });

  } catch (error) {
    console.error('Error adding manual photo:', error);
    return c.json({
      success: false,
      error: 'Failed to add photo'
    }, 500);
  }
});

export default adminPhotoRoutes;
