/**
 * Theme Components Index
 * 
 * Centralized exports for all theme-related components
 */

// Enhanced Mode Toggle Components
export { ModeToggle } from '../mode-toggle';
export { 
  ModeToggleButton, 
  ModeToggleCompact, 
  ModeToggleText, 
  ModeToggleAnimated 
} from './ModeToggleButton';
export { 
  ModeToggleSwitch, 
  ModeToggleSwitchMinimal, 
  ModeToggleSwitchCustom 
} from './ModeToggleSwitch';
export { 
  ModeToggleDropdown, 
  ModeToggleDropdownCompact, 
  ModeToggleDropdownDetailed 
} from './ModeToggleDropdown';

// Theme Selector Components
export { 
  ThemeSelector, 
  ThemeSelectorCompact, 
  ThemeSelectorDetailed 
} from './ThemeSelector';

// Hooks and Utilities
export { useThemePreference } from '../../hooks/useThemePreference';
export { 
  themeTransitions, 
  themeColors, 
  themeConfig, 
  getTransitionClasses, 
  getThemeAriaLabel, 
  getSystemTheme, 
  isValidTheme, 
  sanitizeTheme,
  type ThemeOption 
} from '../../lib/theme-utils';

// Re-export next-themes for convenience
export { useTheme, ThemeProvider } from 'next-themes';
