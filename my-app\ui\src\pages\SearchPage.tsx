import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { SearchBar } from '@/components/search/SearchBar';
import { FilterPanel } from '@/components/search/FilterPanel';
import { SearchResults } from '@/components/search/SearchResults';
import { api } from '@/lib/serverComm';
import { Button } from '@/components/ui/button';
import { SlidersHorizontal } from 'lucide-react';

interface Business {
  id: number;
  name: string;
  slug: string;
  short_description: string;
  address: string;
  phone: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
  };
}

interface SearchResponse {
  businesses: Business[];
  query: string;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function SearchPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Get search parameters
  const query = searchParams.get('q') || '';
  const category = searchParams.get('category') || '';
  const location = searchParams.get('location') || '';

  useEffect(() => {
    if (query.trim()) {
      performSearch();
    }
  }, [query, category, currentPage]);

  const performSearch = async () => {
    if (!query.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.searchBusinesses(query, {
        page: currentPage,
        limit: 12,
        category: category || undefined
      });
      setSearchResults(response);
    } catch (err) {
      setError('Failed to search businesses');
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (newQuery: string, newLocation?: string) => {
    const params = new URLSearchParams();
    if (newQuery) params.set('q', newQuery);
    if (newLocation) params.set('location', newLocation);
    if (category) params.set('category', category);
    
    setSearchParams(params);
    setCurrentPage(1);
  };

  const handleFilterChange = (filters: { category?: string }) => {
    const params = new URLSearchParams(searchParams);
    
    if (filters.category) {
      params.set('category', filters.category);
    } else {
      params.delete('category');
    }
    
    setSearchParams(params);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Search Header */}
      <div className="bg-muted/30 py-8">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto space-y-4">
            <h1 className="text-2xl font-bold">Search Businesses</h1>
            <SearchBar 
              initialQuery={query}
              initialLocation={location}
              onSearch={handleSearch}
            />
            
            {/* Filter Toggle */}
            <div className="flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                {searchResults && (
                  <>
                    {searchResults.pagination.total} results found
                    {query && ` for "${query}"`}
                    {category && ` in ${category}`}
                  </>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-64 flex-shrink-0">
              <FilterPanel
                selectedCategory={category}
                onFilterChange={handleFilterChange}
              />
            </div>
          )}

          {/* Search Results */}
          <div className="flex-1">
            <SearchResults
              results={searchResults}
              loading={loading}
              error={error}
              onPageChange={handlePageChange}
              currentPage={currentPage}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
