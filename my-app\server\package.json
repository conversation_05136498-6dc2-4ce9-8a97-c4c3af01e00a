{"name": "server", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx watch src/server.ts", "deploy": "npm run deploy:cf", "deploy:cf": "node scripts/deploy-cloudflare.js", "test": "vitest", "db:push": "drizzle-kit push:pg"}, "dependencies": {"@hono/node-server": "^1.12.0", "@neondatabase/serverless": "^0.9.0", "dotenv": "^16.0.0", "drizzle-orm": "^0.30.1", "firebase-admin": "^12.0.0", "fs-extra": "^11.2.0", "hono": "^4.0.5", "jose": "^5.2.3", "postgres": "^3.4.7"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240208.0", "@types/fs-extra": "^11.0.4", "dotenv-cli": "^7.3.0", "drizzle-kit": "^0.20.14", "tsx": "^4.7.0", "typescript": "^5.3.3", "vitest": "^1.2.2", "wrangler": "^4.16.0"}}