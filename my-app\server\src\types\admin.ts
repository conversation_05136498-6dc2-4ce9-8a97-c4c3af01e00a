// TypeScript interfaces for admin API responses and data structures

export interface AdminPaginationMeta {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface AdminResponseMeta {
  filters?: Record<string, any>;
  sort?: string;
  timestamp: string;
}

export interface AdminResponse<T> {
  data: T;
  pagination?: AdminPaginationMeta;
  meta?: AdminResponseMeta;
}

// Dashboard Statistics Interfaces
export interface DashboardStats {
  businesses: {
    total: number;
    active: number;
    inactive: number;
    featured: number;
    monthlyGrowth: number;
  };
  applications: {
    pending: number;
    approved: number;
    rejected: number;
    totalThisMonth: number;
    averageProcessingTime: number; // in hours
  };
  reviews: {
    total: number;
    averageRating: number;
    pending: number;
    approved: number;
    rejected: number;
    flagged: number;
  };
  users: {
    total: number;
    admins: number;
    monthlyGrowth: number;
    activeThisMonth: number;
  };
  categories: {
    total: number;
    businessDistribution: CategoryDistribution[];
  };
}

export interface CategoryDistribution {
  categoryId: number;
  categoryName: string;
  businessCount: number;
  percentage: number;
}

// Enhanced Business Interfaces
export interface AdminBusinessFilters {
  search?: string;
  category?: number;
  status?: 'active' | 'inactive' | 'all';
  featured?: boolean;
  location?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'name' | 'created_at' | 'rating' | 'reviews';
  sortOrder?: 'asc' | 'desc';
}

export interface BusinessAnalytics {
  views: number;
  clicks: number;
  conversions: number;
  reviewTrend: number; // percentage change in reviews
  ratingTrend: number; // percentage change in rating
}

export interface AdminBusinessResponse {
  id: number;
  name: string;
  slug: string;
  short_description: string | null;
  address: string;
  phone: string | null;
  email: string | null;
  website: string | null;
  is_featured: boolean;
  is_active: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: Date;
  category: {
    id: number;
    name: string;
    slug: string;
  };
  analytics?: BusinessAnalytics;
}

// Review Moderation Interfaces
export interface ReviewModerationFilters {
  status?: 'pending' | 'approved' | 'rejected' | 'flagged';
  businessId?: number;
  rating?: number;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'created_at' | 'rating' | 'business_name';
  sortOrder?: 'asc' | 'desc';
}

export interface AdminReviewResponse {
  id: number;
  business_id: number;
  business_name: string;
  rating: number;
  comment: string | null;
  author_name: string;
  author_email: string | null;
  is_verified: boolean;
  is_approved: boolean;
  is_flagged?: boolean;
  flag_reason?: string;
  created_at: Date;
  updated_at: Date;
}

// User Management Interfaces
export interface UserActivityMetrics {
  reviewsSubmitted: number;
  businessesViewed: number;
  lastLoginDate: Date | null;
  accountAge: number; // in days
  engagementScore: number; // calculated metric
}

export interface AdminUserResponse {
  id: string;
  email: string;
  display_name: string | null;
  photo_url: string | null;
  is_admin: boolean;
  created_at: Date;
  updated_at: Date;
  activity: UserActivityMetrics;
}

// Analytics Interfaces
export interface TrendData {
  period: string; // e.g., "2024-01", "2024-01-15"
  value: number;
  change?: number; // percentage change from previous period
}

export interface BusinessPerformanceMetrics {
  totalBusinesses: TrendData[];
  newRegistrations: TrendData[];
  averageRating: TrendData[];
  totalReviews: TrendData[];
}

export interface CategoryPerformanceMetrics {
  categoryId: number;
  categoryName: string;
  businessCount: number;
  averageRating: number;
  totalReviews: number;
  growthRate: number; // percentage
}

// Bulk Operations Interfaces
export interface BulkOperationRequest {
  operation: 'approve' | 'reject' | 'delete' | 'feature' | 'activate' | 'deactivate';
  ids: number[];
  notes?: string;
}

export interface BulkOperationResult {
  success: number;
  failed: number;
  errors: Array<{
    id: number;
    error: string;
  }>;
}

// Application Management Interfaces
export interface ApplicationAnalytics {
  averageProcessingTime: number; // in hours
  approvalRate: number; // percentage
  monthlySubmissions: TrendData[];
  categoryBreakdown: CategoryDistribution[];
}

export interface AdminApplicationFilters {
  status?: 'pending' | 'approved' | 'rejected';
  category?: number;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'submitted_at' | 'business_name' | 'category';
  sortOrder?: 'asc' | 'desc';
}

// Error Response Interface
export interface AdminErrorResponse {
  error: string;
  details?: string;
  code?: string;
  timestamp: string;
}

// Audit Log Interface
export interface AdminAuditLog {
  id: number;
  admin_id: string;
  admin_email: string;
  action: string;
  resource_type: 'business' | 'application' | 'review' | 'user' | 'category';
  resource_id: number | string;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

// Export Data Interfaces
export interface ExportRequest {
  type: 'businesses' | 'reviews' | 'applications' | 'users';
  format: 'csv' | 'json' | 'xlsx';
  filters?: Record<string, any>;
  dateFrom?: string;
  dateTo?: string;
}

export interface ExportResponse {
  downloadUrl: string;
  filename: string;
  size: number;
  recordCount: number;
  expiresAt: Date;
}
