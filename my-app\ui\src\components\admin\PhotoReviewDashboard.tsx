import { useState, useEffect } from 'react';
import { PhotoReviewCard, type PendingPhoto } from './PhotoReviewCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, Search, Filter, CheckCircle, XCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { fetchWithAuth } from '@/lib/admin-api';

export function PhotoReviewDashboard() {
  const [pendingPhotos, setPendingPhotos] = useState<PendingPhoto[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sourceFilter, setSourceFilter] = useState<string>('all');
  const { toast } = useToast();

  const fetchPendingPhotos = async () => {
    try {
      setLoading(true);
      const response = await fetchWithAuth('/api/v1/admin/photos/pending');
      const data = await response.json();
      
      if (data.success) {
        setPendingPhotos(data.data.pending_photos);
      } else {
        throw new Error(data.error || 'Failed to fetch pending photos');
      }
    } catch (error) {
      console.error('Error fetching pending photos:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch pending photos',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPendingPhotos();
  }, []);

  const handleApprove = async (photoId: number) => {
    try {
      const response = await fetchWithAuth(`/api/v1/admin/photos/${photoId}/approve`, {
        method: 'PUT',
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPendingPhotos(prev => prev.filter(photo => photo.id !== photoId));
        toast({
          title: 'Success',
          description: 'Photo approved successfully',
        });
      } else {
        throw new Error(data.error || 'Failed to approve photo');
      }
    } catch (error) {
      console.error('Error approving photo:', error);
      toast({
        title: 'Error',
        description: 'Failed to approve photo',
        variant: 'destructive',
      });
    }
  };

  const handleReject = async (photoId: number) => {
    try {
      const response = await fetchWithAuth(`/api/v1/admin/photos/${photoId}/reject`, {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPendingPhotos(prev => prev.filter(photo => photo.id !== photoId));
        toast({
          title: 'Success',
          description: 'Photo rejected successfully',
        });
      } else {
        throw new Error(data.error || 'Failed to reject photo');
      }
    } catch (error) {
      console.error('Error rejecting photo:', error);
      toast({
        title: 'Error',
        description: 'Failed to reject photo',
        variant: 'destructive',
      });
    }
  };

  const handleSetPrimary = async (photoId: number) => {
    try {
      const response = await fetchWithAuth(`/api/v1/admin/photos/${photoId}/set-primary`, {
        method: 'PUT',
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPendingPhotos(prev => prev.filter(photo => photo.id !== photoId));
        toast({
          title: 'Success',
          description: 'Photo set as primary and approved',
        });
      } else {
        throw new Error(data.error || 'Failed to set primary photo');
      }
    } catch (error) {
      console.error('Error setting primary photo:', error);
      toast({
        title: 'Error',
        description: 'Failed to set primary photo',
        variant: 'destructive',
      });
    }
  };

  // Filter photos based on search and source
  const filteredPhotos = pendingPhotos.filter(photo => {
    const matchesSearch = photo.business_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSource = sourceFilter === 'all' || photo.photo_source === sourceFilter;
    return matchesSearch && matchesSource;
  });

  // Group photos by business for bulk actions
  const photosByBusiness = filteredPhotos.reduce((acc, photo) => {
    if (!acc[photo.business_id]) {
      acc[photo.business_id] = {
        business_name: photo.business_name,
        photos: []
      };
    }
    acc[photo.business_id].photos.push(photo);
    return acc;
  }, {} as Record<number, { business_name: string; photos: PendingPhoto[] }>);

  const handleBulkApprove = async (businessId: number) => {
    try {
      const response = await fetchWithAuth(`/api/v1/admin/businesses/${businessId}/photos/approve-all`, {
        method: 'PUT',
      });
      
      const data = await response.json();
      
      if (data.success) {
        setPendingPhotos(prev => prev.filter(photo => photo.business_id !== businessId));
        toast({
          title: 'Success',
          description: 'All photos approved for business',
        });
      } else {
        throw new Error(data.error || 'Failed to approve photos');
      }
    } catch (error) {
      console.error('Error bulk approving photos:', error);
      toast({
        title: 'Error',
        description: 'Failed to approve photos',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-2">
          <RefreshCw className="w-4 h-4 animate-spin" />
          <span>Loading pending photos...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Photo Review</h2>
          <p className="text-muted-foreground">
            Review and approve photos collected from external sources
          </p>
        </div>
        <Button onClick={fetchPendingPhotos} variant="outline" size="sm">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Pending Photos</span>
            </div>
            <p className="text-2xl font-bold">{pendingPhotos.length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium">Businesses with Photos</span>
            </div>
            <p className="text-2xl font-bold">{Object.keys(photosByBusiness).length}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium">Google Places</span>
            </div>
            <p className="text-2xl font-bold">
              {pendingPhotos.filter(p => p.photo_source === 'google_places').length}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search by business name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <Select value={sourceFilter} onValueChange={setSourceFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by source" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Sources</SelectItem>
            <SelectItem value="google_places">Google Places</SelectItem>
            <SelectItem value="yelp">Yelp</SelectItem>
            <SelectItem value="manual">Manual</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Photos Grid */}
      {filteredPhotos.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">All caught up!</h3>
            <p className="text-muted-foreground">
              No pending photos to review at the moment.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-8">
          {Object.entries(photosByBusiness).map(([businessId, businessData]) => (
            <div key={businessId} className="space-y-4">
              {/* Business Header with Bulk Actions */}
              <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                <div>
                  <h3 className="font-semibold">{businessData.business_name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {businessData.photos.length} pending photo{businessData.photos.length !== 1 ? 's' : ''}
                  </p>
                </div>
                <Button
                  onClick={() => handleBulkApprove(parseInt(businessId))}
                  variant="outline"
                  size="sm"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Approve All
                </Button>
              </div>
              
              {/* Photos Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {businessData.photos.map((photo) => (
                  <PhotoReviewCard
                    key={photo.id}
                    photo={photo}
                    onApprove={handleApprove}
                    onReject={handleReject}
                    onSetPrimary={handleSetPrimary}
                    isLoading={loading}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
