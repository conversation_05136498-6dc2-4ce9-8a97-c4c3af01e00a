// Public business routes
import { Hono } from 'hono';
import { eq, and, desc, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';

const publicBusinessRoutes = new Hono();

// Get all businesses (public)
publicBusinessRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const category = c.req.query('category');
    const search = c.req.query('search');
    const featured = c.req.query('featured') === 'true';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businesses.is_active, true));

    if (category) {
      query = query.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.category_id, parseInt(category))
      ));
    }

    if (search) {
      query = query.where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`(${businessSchema.businesses.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.short_description} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.address} ILIKE ${`%${search}%`})`
      ));
    }

    if (featured) {
      query = query.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.is_featured, true)
      ));
    }

    const businesses = await query
      .orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    let countQuery = db.select({ total: count() }).from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.is_active, true));

    if (category) {
      countQuery = countQuery.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.category_id, parseInt(category))
      ));
    }

    if (search) {
      countQuery = countQuery.where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`(${businessSchema.businesses.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.short_description} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.address} ILIKE ${`%${search}%`})`
      ));
    }

    if (featured) {
      countQuery = countQuery.where(and(
        eq(businessSchema.businesses.is_active, true),
        eq(businessSchema.businesses.is_featured, true)
      ));
    }

    const [{ total }] = await countQuery;

    return c.json({
      success: true,
      data: {
        businesses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching businesses:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch businesses' 
    }, 500);
  }
});

// Get business by slug (public)
publicBusinessRoutes.get('/:slug', async (c) => {
  try {
    const slug = c.req.param('slug');
    const db = await getDatabase();

    const [business] = await db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        description: businessSchema.businesses.description,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.slug, slug),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      return c.json({ 
        success: false,
        error: 'Business not found' 
      }, 404);
    }

    // Get business hours
    const hours = await db
      .select()
      .from(businessSchema.businessHours)
      .where(eq(businessSchema.businessHours.business_id, business.id))
      .orderBy(businessSchema.businessHours.day_of_week);

    // Get approved business images only
    const images = await db
      .select()
      .from(businessSchema.businessImages)
      .where(and(
        eq(businessSchema.businessImages.business_id, business.id),
        eq(businessSchema.businessImages.is_approved, true)
      ))
      .orderBy(businessSchema.businessImages.display_order);

    // Get approved reviews
    const reviews = await db
      .select()
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, business.id),
        eq(businessSchema.reviews.is_approved, true)
      ))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(10);

    return c.json({
      success: true,
      data: {
        business: {
          ...business,
          hours,
          images,
          reviews
        }
      }
    });
  } catch (error) {
    console.error('Error fetching business:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch business' 
    }, 500);
  }
});

// Get business reviews
publicBusinessRoutes.get('/:id/reviews', async (c) => {
  try {
    const businessId = parseInt(c.req.param('id'));
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Check if business exists and is active
    const [business] = await db
      .select({ id: businessSchema.businesses.id })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.id, businessId),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      return c.json({ 
        success: false,
        error: 'Business not found' 
      }, 404);
    }

    // Get approved reviews
    const reviews = await db
      .select()
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, businessId),
        eq(businessSchema.reviews.is_approved, true)
      ))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, businessId),
        eq(businessSchema.reviews.is_approved, true)
      ));

    return c.json({
      success: true,
      data: {
        reviews,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching business reviews:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch business reviews' 
    }, 500);
  }
});

export default publicBusinessRoutes;