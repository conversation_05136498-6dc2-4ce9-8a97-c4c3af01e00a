// Admin review moderation routes
import { Hono } from 'hono';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import type { AdminResponse } from '../types/admin';

const reviewRoutes = new Hono();

// ===== REVIEW MODERATION SYSTEM =====
// Get All Reviews with Moderation Status
reviewRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status'); // pending, approved, rejected, all
    const businessId = c.req.query('businessId');
    const rating = c.req.query('rating');
    const sortBy = c.req.query('sortBy') || 'created_at';
    const sortOrder = c.req.query('sortOrder') || 'desc';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Build where conditions
    const whereConditions = [];
    
    if (status === 'pending') {
      whereConditions.push(eq(businessSchema.reviews.is_approved, false));
    } else if (status === 'approved') {
      whereConditions.push(eq(businessSchema.reviews.is_approved, true));
    }
    // Note: We don't have a rejected status in current schema, treating non-approved as pending
    
    if (businessId) {
      whereConditions.push(eq(businessSchema.reviews.business_id, parseInt(businessId)));
    }
    
    if (rating) {
      whereConditions.push(eq(businessSchema.reviews.rating, parseInt(rating)));
    }

    let query = db
      .select({
        id: businessSchema.reviews.id,
        business_id: businessSchema.reviews.business_id,
        business_name: businessSchema.businesses.name,
        rating: businessSchema.reviews.rating,
        comment: businessSchema.reviews.comment,
        author_name: businessSchema.reviews.author_name,
        author_email: businessSchema.reviews.author_email,
        is_verified: businessSchema.reviews.is_verified,
        is_approved: businessSchema.reviews.is_approved,
        created_at: businessSchema.reviews.created_at,
        updated_at: businessSchema.reviews.updated_at,
      })
      .from(businessSchema.reviews)
      .leftJoin(businessSchema.businesses, eq(businessSchema.reviews.business_id, businessSchema.businesses.id));

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Apply sorting
    const orderColumn = sortBy === 'rating' ? businessSchema.reviews.rating :
                       sortBy === 'business_name' ? businessSchema.businesses.name :
                       businessSchema.reviews.created_at;
    
    const orderDirection = sortOrder === 'asc' ? asc : desc;
    query = query.orderBy(orderDirection(orderColumn));

    const reviews = await query.limit(limit).offset(offset);

    // Get total count with same filters
    let countQuery = db.select({ total: count() }).from(businessSchema.reviews);
    if (businessId) {
      countQuery = countQuery.leftJoin(businessSchema.businesses, eq(businessSchema.reviews.business_id, businessSchema.businesses.id));
    }
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    const response: AdminResponse<typeof reviews> = {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        filters: { status, businessId, rating, sortBy, sortOrder },
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return c.json({ 
      error: 'Failed to fetch reviews',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Approve Review
reviewRoutes.put('/:id/approve', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    // Check if review exists
    const [existingReview] = await db
      .select()
      .from(businessSchema.reviews)
      .where(eq(businessSchema.reviews.id, id))
      .limit(1);

    if (!existingReview) {
      return c.json({ 
        error: 'Review not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    if (existingReview.is_approved) {
      return c.json({ 
        error: 'Review is already approved',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    // Approve the review
    const [updatedReview] = await db
      .update(businessSchema.reviews)
      .set({
        is_approved: true,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.reviews.id, id))
      .returning();

    // Update business average rating and review count
    await updateBusinessRating(existingReview.business_id);

    const response: AdminResponse<typeof updatedReview> = {
      data: updatedReview,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error approving review:', error);
    return c.json({ 
      error: 'Failed to approve review',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Reject Review (set to not approved)
reviewRoutes.put('/:id/reject', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    const db = await getDatabase();

    // Check if review exists
    const [existingReview] = await db
      .select()
      .from(businessSchema.reviews)
      .where(eq(businessSchema.reviews.id, id))
      .limit(1);

    if (!existingReview) {
      return c.json({ 
        error: 'Review not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Reject the review (set to not approved)
    const [updatedReview] = await db
      .update(businessSchema.reviews)
      .set({
        is_approved: false,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.reviews.id, id))
      .returning();

    // Update business average rating and review count
    await updateBusinessRating(existingReview.business_id);

    return c.json({
      message: 'Review rejected successfully',
      reason: body.reason || 'No reason provided',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error rejecting review:', error);
    return c.json({ 
      error: 'Failed to reject review',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Delete Review
reviewRoutes.delete('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    // Check if review exists
    const [existingReview] = await db
      .select()
      .from(businessSchema.reviews)
      .where(eq(businessSchema.reviews.id, id))
      .limit(1);

    if (!existingReview) {
      return c.json({ 
        error: 'Review not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Delete the review
    await db
      .delete(businessSchema.reviews)
      .where(eq(businessSchema.reviews.id, id));

    // Update business average rating and review count
    await updateBusinessRating(existingReview.business_id);

    return c.json({
      message: 'Review deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting review:', error);
    return c.json({ 
      error: 'Failed to delete review',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get Reviews by Business
reviewRoutes.get('/business/:businessId', async (c) => {
  try {
    const businessId = parseInt(c.req.param('businessId'));
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status'); // pending, approved, all
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Check if business exists
    const [business] = await db
      .select({ name: businessSchema.businesses.name })
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.id, businessId))
      .limit(1);

    if (!business) {
      return c.json({ 
        error: 'Business not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Build where conditions
    const whereConditions = [eq(businessSchema.reviews.business_id, businessId)];
    
    if (status === 'pending') {
      whereConditions.push(eq(businessSchema.reviews.is_approved, false));
    } else if (status === 'approved') {
      whereConditions.push(eq(businessSchema.reviews.is_approved, true));
    }

    const reviews = await db
      .select({
        id: businessSchema.reviews.id,
        rating: businessSchema.reviews.rating,
        comment: businessSchema.reviews.comment,
        author_name: businessSchema.reviews.author_name,
        author_email: businessSchema.reviews.author_email,
        is_verified: businessSchema.reviews.is_verified,
        is_approved: businessSchema.reviews.is_approved,
        created_at: businessSchema.reviews.created_at,
        updated_at: businessSchema.reviews.updated_at,
      })
      .from(businessSchema.reviews)
      .where(and(...whereConditions))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.reviews)
      .where(and(...whereConditions));

    const response: AdminResponse<typeof reviews> = {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        businessName: business.name,
        filters: { status },
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching business reviews:', error);
    return c.json({ 
      error: 'Failed to fetch business reviews',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

/**
 * Update business average rating and total review count
 */
async function updateBusinessRating(businessId: number): Promise<void> {
  const db = await getDatabase();

  // Calculate new average rating and total reviews for approved reviews only
  const [stats] = await db
    .select({
      averageRating: sql<number>`avg(${businessSchema.reviews.rating})`,
      totalReviews: count(businessSchema.reviews.id),
    })
    .from(businessSchema.reviews)
    .where(and(
      eq(businessSchema.reviews.business_id, businessId),
      eq(businessSchema.reviews.is_approved, true)
    ));

  await db
    .update(businessSchema.businesses)
    .set({
      average_rating: stats.averageRating ? stats.averageRating.toFixed(2) : '0.00',
      total_reviews: stats.totalReviews,
      updated_at: new Date(),
    })
    .where(eq(businessSchema.businesses.id, businessId));
}

export default reviewRoutes;
