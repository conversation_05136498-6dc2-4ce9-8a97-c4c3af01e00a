I'll use the PRP (Prompt-Response Pattern) generator to create a comprehensive frontend completion roadmap for the business directory application.

Now I'll generate a comprehensive PRP for the Frontend Completion Roadmap following the research process and template structure.

## **RESEARCH PROCESS**

### **1. Codebase Analysis**

### **2. External Research**

### **3. ULTRATHINK PLANNING**

Based on my codebase analysis, I can see that:

1. **Existing Structure**: The app already has a solid foundation with ShadCN/UI components, React Router, and TypeScript
2. **Homepage Components**: Already exist but need completion and API integration
3. **Business Components**: Partially implemented but missing key functionality
4. **Design System**: Well-established with consistent patterns
5. **API Integration**: Backend exists but frontend needs to connect to it

**APPROACH**: Create a comprehensive PRP that builds upon existing patterns while completing missing functionality.

---

# **PRP: Business Directory Frontend Completion**

## **Context & Objective**

Complete the business directory frontend by implementing missing components, pages, and features while maintaining consistency with the existing ShadCN/UI design system and React architecture.

### **Current State Analysis**
- ✅ **Foundation**: React 18 + TypeScript + Vite + ShadCN/UI established
- ✅ **Routing**: React Router v6 configured with admin and public routes
- ✅ **Design System**: Comprehensive UI components available
- ✅ **Homepage Structure**: Basic components exist but need completion
- ✅ **Business Components**: Partial implementation exists
- ❌ **API Integration**: Frontend not connected to backend APIs
- ❌ **Search System**: Missing search functionality
- ❌ **Map Integration**: No Mapbox implementation
- ❌ **Complete Business Profiles**: Missing key sections

## **Implementation Blueprint**

### **Phase 1: API Integration Foundation**
```typescript
// 1. Extend existing serverComm.ts patterns
// File: ui/src/lib/business-api.ts
export const businessApi = {
  getBusinesses: (params: BusinessListParams) => Promise<BusinessListResponse>,
  getBusiness: (slug: string) => Promise<BusinessResponse>,
  getCategories: () => Promise<CategoriesResponse>,
  searchBusinesses: (query: SearchParams) => Promise<SearchResponse>
};

// 2. Create React hooks for data fetching
// File: ui/src/hooks/useBusinesses.ts
export function useBusinesses(params: BusinessListParams) {
  // Follow existing patterns in the codebase
}
```

### **Phase 2: Complete Homepage Components**
```typescript
// 3. Enhance existing homepage components with real data
// Files to update:
// - ui/src/components/homepage/CategoryHighlights.tsx
// - ui/src/components/homepage/FeaturedBusinesses.tsx
// - ui/src/components/homepage/NewBusinesses.tsx

// Pattern to follow from existing HeroSection.tsx:
const [data, setData] = useState<BusinessData[]>([]);
const [loading, setLoading] = useState(true);

useEffect(() => {
  fetchData().then(setData).finally(() => setLoading(false));
}, []);
```

### **Phase 3: Business Profile System**
```typescript
// 4. Complete business profile components
// Files to enhance:
// - ui/src/components/business/BusinessProfile.tsx (exists, needs completion)
// - ui/src/components/business/BusinessHeader.tsx (exists, needs enhancement)
// - ui/src/components/business/ContactInfo.tsx (exists, needs completion)

// 5. Add missing components:
// - ui/src/components/business/BusinessMap.tsx (new - Mapbox integration)
// - ui/src/components/business/BusinessActions.tsx (new - call, directions)
// - ui/src/components/business/SocialLinks.tsx (new - social media)
```

### **Phase 4: Search & Discovery**
```typescript
// 6. Implement search system
// Files to create:
// - ui/src/components/search/SearchBar.tsx
// - ui/src/components/search/SearchFilters.tsx
// - ui/src/components/search/SearchResults.tsx
// - ui/src/pages/SearchResultsPage.tsx

// 7. Enhance existing SearchPage.tsx with real functionality
```

## **Critical Context & Patterns**

### **Existing Component Patterns to Follow**
```typescript
// From ui/src/components/homepage/HeroSection.tsx:
interface ComponentProps {
  onAction?: (param: string) => void;
}

export function Component({ onAction }: ComponentProps) {
  const [state, setState] = useState('');
  const navigate = useNavigate();
  
  const handleAction = () => {
    if (onAction) {
      onAction(state);
    } else {
      navigate('/target');
    }
  };
  
  return (
    <div className="container mx-auto">
      {/* ShadCN/UI components */}
    </div>
  );
}
```

### **API Integration Pattern**
```typescript
// Follow pattern from existing admin components:
// ui/src/pages/admin/AdminDashboard.tsx lines 25-32

const [data, setData] = useState<DataType[]>([]);
const [loading, setLoading] = useState(true);
const [error, setError] = useState<string | null>(null);

useEffect(() => {
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await api.getData();
      setData(response.data);
    } catch (err) {
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };
  fetchData();
}, []);
```

### **ShadCN/UI Component Usage**
```typescript
// Consistent with existing patterns in ui/src/components/ui/
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

// Loading states pattern:
{loading ? (
  <Skeleton className="h-4 w-full" />
) : (
  <div>{content}</div>
)}
```

## **External Dependencies & Documentation**

### **Mapbox Integration**
- **Documentation**: https://docs.mapbox.com/mapbox-gl-js/api/
- **React Integration**: https://docs.mapbox.com/help/tutorials/use-mapbox-gl-js-with-react/
- **Installation**: `npm install mapbox-gl @types/mapbox-gl`

### **Image Handling**
- **Existing Pattern**: Use `ui/src/components/ui/lazy-image.tsx` for optimized loading
- **Fallback Images**: Implement placeholder system for missing business images

### **Form Handling**
- **Pattern**: Follow existing form patterns in the codebase
- **Validation**: Use existing validation patterns from admin components

## **Implementation Tasks (Ordered)**

### **Week 1: Foundation & API Integration**
1. **Create Business API Client** (`ui/src/lib/business-api.ts`)
   - Extend existing serverComm.ts patterns
   - Implement all public business endpoints
   - Add TypeScript interfaces for responses

2. **Create Data Fetching Hooks** (`ui/src/hooks/`)
   - `useBusinesses.ts` - Business listing with pagination
   - `useBusiness.ts` - Single business profile
   - `useCategories.ts` - Category listing
   - `useSearch.ts` - Search functionality

3. **Update Homepage Components with Real Data**
   - Enhance `CategoryHighlights.tsx` with API integration
   - Complete `FeaturedBusinesses.tsx` with real business data
   - Update `NewBusinesses.tsx` with recent businesses

### **Week 2: Business Profile System**
4. **Complete Business Profile Components**
   - Enhance `BusinessProfile.tsx` with all sections
   - Complete `BusinessHeader.tsx` with hero image and rating
   - Finish `ContactInfo.tsx` with all contact methods
   - Update `BusinessHours.tsx` with operating hours display

5. **Create Missing Business Components**
   - `BusinessMap.tsx` - Mapbox integration for location
   - `BusinessActions.tsx` - Call, directions, website buttons
   - `SocialLinks.tsx` - Social media links display
   - `BusinessAmenities.tsx` - Features and amenities

6. **Enhance Business Profile Page**
   - Update `BusinessProfilePage.tsx` with complete layout
   - Add SEO meta tags and structured data
   - Implement error handling for missing businesses

### **Week 3: Search & Discovery**
7. **Implement Search System**
   - Create `SearchBar.tsx` with autocomplete
   - Build `SearchFilters.tsx` with category and location filters
   - Develop `SearchResults.tsx` with list and map views
   - Create `LocationSearch.tsx` for location-based filtering

8. **Complete Search Pages**
   - Enhance `SearchPage.tsx` with full functionality
   - Create `SearchResultsPage.tsx` for search results
   - Update `CategoryDetailPage.tsx` with business listings

9. **Add Interactive Map Features**
   - Create `InteractiveMap.tsx` with Mapbox GL JS
   - Build `BusinessMarker.tsx` for custom map markers
   - Develop `MarkerPopup.tsx` for business info popups
   - Add `MapControls.tsx` for zoom and filter controls

### **Week 4: Enhancement & Polish**
10. **Business Listing & Cards**
    - Create `BusinessListPage.tsx` for browsing all businesses
    - Enhance `BusinessCard.tsx` with complete information
    - Add `BusinessGrid.tsx` for responsive grid layouts
    - Implement `BusinessFilters.tsx` for advanced filtering

11. **User Experience Enhancements**
    - Add loading skeletons throughout the app
    - Implement error boundaries and fallback UI
    - Add responsive design improvements
    - Optimize performance with lazy loading

12. **Final Integration & Testing**
    - Connect all components to backend APIs
    - Test all user workflows end-to-end
    - Optimize bundle size and performance
    - Add accessibility improvements

## **Error Handling Strategy**

### **API Error Handling**
```typescript
// Pattern for all API calls:
try {
  const response = await businessApi.getBusinesses(params);
  setData(response.data);
} catch (error) {
  if (error.status === 404) {
    setError('Businesses not found');
  } else if (error.status >= 500) {
    setError('Server error. Please try again later.');
  } else {
    setError('Something went wrong. Please try again.');
  }
}
```

### **Component Error Boundaries**
```typescript
// Add error boundaries for major sections:
<ErrorBoundary fallback={<BusinessProfileError />}>
  <BusinessProfile business={business} />
</ErrorBoundary>
```

## **Validation Gates**

### **Development Validation**
```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Component testing
npm run test

# Build verification
npm run build
```

### **Functional Validation**
```bash
# Start development server
npm run dev

# Test core workflows:
# 1. Homepage loads with real data
# 2. Search functionality works
# 3. Business profiles display correctly
# 4. Category browsing functions
# 5. Map integration works
# 6. Mobile responsiveness
```

### **Performance Validation**
```bash
# Bundle analysis
npm run build && npm run analyze

# Lighthouse audit
# - Performance score > 90
# - Accessibility score > 95
# - Best practices score > 90
# - SEO score > 90
```

## **Success Criteria**

### **Functional Requirements**
- [ ] Homepage displays real business data from API
- [ ] Search functionality works with location and keyword filters
- [ ] Business profiles show complete information with map
- [ ] Category browsing displays businesses correctly
- [ ] Mobile-responsive design works on all screen sizes
- [ ] Loading states and error handling work properly

### **Technical Requirements**
- [ ] All components follow existing ShadCN/UI patterns
- [ ] TypeScript types are properly defined
- [ ] API integration follows existing patterns
- [ ] Performance metrics meet targets
- [ ] Accessibility standards are met
- [ ] SEO optimization is implemented

### **User Experience Requirements**
- [ ] Fast loading times (< 3 seconds)
- [ ] Intuitive navigation and search
- [ ] Clear business information display
- [ ] Responsive design on all devices
- [ ] Proper error messages and fallbacks
- [ ] Smooth animations and transitions

## **File Structure**

```
ui/src/
├── lib/
│   ├── business-api.ts          # New - Business API client
│   └── map-utils.ts             # New - Mapbox utilities
├── hooks/
│   ├── useBusinesses.ts         # New - Business data fetching
│   ├── useBusiness.ts           # New - Single business
│   ├── useCategories.ts         # New - Categories
│   └── useSearch.ts             # New - Search functionality
├── components/
│   ├── homepage/                # Enhance existing
│   │   ├── HeroSection.tsx      # ✅ Exists - enhance
│   │   ├── CategoryHighlights.tsx # ✅ Exists - complete
│   │   ├── FeaturedBusinesses.tsx # ✅ Exists - complete
│   │   └── NewBusinesses.tsx    # ✅ Exists - complete
│   ├── business/                # Enhance existing
│   │   ├── BusinessProfile.tsx  # ✅ Exists - complete
│   │   ├── BusinessHeader.tsx   # ✅ Exists - enhance
│   │   ├── ContactInfo.tsx      # ✅ Exists - complete
│   │   ├── BusinessHours.tsx    # ✅ Exists - complete
│   │   ├── PhotoGallery.tsx     # ✅ Exists - enhance
│   │   ├── ReviewsSection.tsx   # ✅ Exists - complete
│   │   ├── BusinessMap.tsx      # New - Mapbox integration
│   │   ├── BusinessActions.tsx  # New - Action buttons
│   │   └── SocialLinks.tsx      # New - Social media
│   ├── search/                  # New directory
│   │   ├── SearchBar.tsx        # New - Main search
│   │   ├── SearchFilters.tsx    # New - Advanced filters
│   │   ├── SearchResults.tsx    # New - Results display
│   │   └── LocationSearch.tsx   # New - Location picker
│   └── map/                     # New directory
│       ├── InteractiveMap.tsx   # New - Mapbox component
│       ├── BusinessMarker.tsx   # New - Custom markers
│       ├── MarkerPopup.tsx      # New - Info popups
│       └── MapControls.tsx      # New - Map controls
└── pages/
    ├── HomePage.tsx             # ✅ Exists - enhance
    ├── SearchPage.tsx           # ✅ Exists - complete
    ├── BusinessProfilePage.tsx  # ✅ Exists - complete
    ├── CategoriesPage.tsx       # ✅ Exists - enhance
    ├── CategoryDetailPage.tsx   # ✅ Exists - complete
    └── SearchResultsPage.tsx    # New - Search results
```

## **Quality Score: 9/10**

**Confidence Level**: Very High for one-pass implementation success

**Strengths**:
- Comprehensive context from existing codebase analysis
- Clear implementation path following established patterns
- Detailed task breakdown with specific file references
- Proper error handling and validation strategies
- External documentation references included

**Risk Mitigation**:
- All patterns follow existing codebase conventions
- ShadCN/UI components already established
- TypeScript interfaces ensure type safety
- Validation gates prevent regression
- Incremental implementation reduces complexity

---

**Save as**: `my-app/context-engineering-intro-main/PRPs/frontend-completion-roadmap.md`
