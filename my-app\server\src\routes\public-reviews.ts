// Public review routes
import { Hono } from 'hono';
import { eq, and, desc } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';

const publicReviewRoutes = new Hono();

// Submit review (public)
publicReviewRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Validate required fields
    if (!body.business_id || !body.rating || !body.author_name) {
      return c.json({ 
        success: false,
        error: 'Missing required fields: business_id, rating, author_name' 
      }, 400);
    }

    if (body.rating < 1 || body.rating > 5) {
      return c.json({ 
        success: false,
        error: 'Rating must be between 1 and 5' 
      }, 400);
    }

    // Validate business exists and is active
    const [business] = await db
      .select({ id: businessSchema.businesses.id })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.id, body.business_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      return c.json({ 
        success: false,
        error: 'Business not found' 
      }, 400);
    }

    // Validate email format if provided
    if (body.author_email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(body.author_email)) {
        return c.json({ 
          success: false,
          error: 'Invalid email format' 
        }, 400);
      }
    }

    const [review] = await db
      .insert(businessSchema.reviews)
      .values({
        business_id: body.business_id,
        rating: body.rating,
        comment: body.comment || null,
        author_name: body.author_name,
        author_email: body.author_email || null,
        is_verified: false,
        is_approved: false, // Reviews need admin approval
      })
      .returning();

    return c.json({
      success: true,
      data: {
        message: 'Review submitted successfully and is pending approval',
        review_id: review.id
      }
    }, 201);
  } catch (error) {
    console.error('Error submitting review:', error);
    return c.json({ 
      success: false,
      error: 'Failed to submit review' 
    }, 500);
  }
});

// Get review status (public - requires review ID)
publicReviewRoutes.get('/:id/status', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    const [review] = await db
      .select({
        id: businessSchema.reviews.id,
        business_id: businessSchema.reviews.business_id,
        rating: businessSchema.reviews.rating,
        author_name: businessSchema.reviews.author_name,
        is_approved: businessSchema.reviews.is_approved,
        created_at: businessSchema.reviews.created_at,
        updated_at: businessSchema.reviews.updated_at,
      })
      .from(businessSchema.reviews)
      .where(eq(businessSchema.reviews.id, id))
      .limit(1);

    if (!review) {
      return c.json({ 
        success: false,
        error: 'Review not found' 
      }, 404);
    }

    return c.json({
      success: true,
      data: { review }
    });
  } catch (error) {
    console.error('Error fetching review status:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch review status' 
    }, 500);
  }
});

export default publicReviewRoutes;