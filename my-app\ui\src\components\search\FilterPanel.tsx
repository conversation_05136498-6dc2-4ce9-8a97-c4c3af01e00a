import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { api } from '@/lib/serverComm';
import { Star, X } from 'lucide-react';

interface Category {
  id: number;
  name: string;
  slug: string;
  business_count: number;
}

interface FilterPanelProps {
  selectedCategory?: string;
  onFilterChange: (filters: {
    category?: string;
    rating?: number;
    features?: string[];
  }) => void;
}

export function FilterPanel({ selectedCategory, onFilterChange }: FilterPanelProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  // Common business features/services
  const businessFeatures = [
    'Free WiFi',
    'Parking Available',
    'Wheelchair Accessible',
    'Accepts Credit Cards',
    'Delivery Available',
    'Online Booking',
    'Pet Friendly',
    'Outdoor Seating',
    '24/7 Service',
    'Family Friendly'
  ];

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await api.getCategories();
      setCategories(response.categories.sort((a: Category, b: Category) => 
        b.business_count - a.business_count
      ));
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (categorySlug: string) => {
    const newCategory = selectedCategory === categorySlug ? undefined : categorySlug;
    onFilterChange({
      category: newCategory,
      rating: selectedRating || undefined,
      features: selectedFeatures
    });
  };

  const handleRatingChange = (rating: number) => {
    const newRating = selectedRating === rating ? null : rating;
    setSelectedRating(newRating);
    onFilterChange({
      category: selectedCategory,
      rating: newRating || undefined,
      features: selectedFeatures
    });
  };

  const handleFeatureChange = (feature: string, checked: boolean) => {
    const newFeatures = checked
      ? [...selectedFeatures, feature]
      : selectedFeatures.filter(f => f !== feature);
    
    setSelectedFeatures(newFeatures);
    onFilterChange({
      category: selectedCategory,
      rating: selectedRating || undefined,
      features: newFeatures
    });
  };

  const clearAllFilters = () => {
    setSelectedRating(null);
    setSelectedFeatures([]);
    onFilterChange({});
  };

  const hasActiveFilters = selectedCategory || selectedRating || selectedFeatures.length > 0;

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded animate-pulse"></div>
            <div className="h-4 bg-muted rounded animate-pulse"></div>
            <div className="h-4 bg-muted rounded animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Filters</CardTitle>
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearAllFilters}>
              <X className="w-4 h-4 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Categories */}
        <div>
          <h3 className="font-semibold mb-3">Categories</h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category.slug}`}
                  checked={selectedCategory === category.slug}
                  onCheckedChange={() => handleCategoryChange(category.slug)}
                />
                <Label 
                  htmlFor={`category-${category.slug}`}
                  className="text-sm cursor-pointer flex-1"
                >
                  {category.name}
                  <span className="text-muted-foreground ml-1">
                    ({category.business_count})
                  </span>
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Rating Filter */}
        <div>
          <h3 className="font-semibold mb-3">Minimum Rating</h3>
          <div className="space-y-2">
            {[4, 3, 2, 1].map((rating) => (
              <div key={rating} className="flex items-center space-x-2">
                <Checkbox
                  id={`rating-${rating}`}
                  checked={selectedRating === rating}
                  onCheckedChange={() => handleRatingChange(rating)}
                />
                <Label 
                  htmlFor={`rating-${rating}`}
                  className="text-sm cursor-pointer flex items-center"
                >
                  <div className="flex items-center">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${
                          i < rating
                            ? 'fill-yellow-400 text-yellow-400'
                            : 'text-muted-foreground'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="ml-2">& up</span>
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Features */}
        <div>
          <h3 className="font-semibold mb-3">Features</h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {businessFeatures.map((feature) => (
              <div key={feature} className="flex items-center space-x-2">
                <Checkbox
                  id={`feature-${feature}`}
                  checked={selectedFeatures.includes(feature)}
                  onCheckedChange={(checked) => 
                    handleFeatureChange(feature, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`feature-${feature}`}
                  className="text-sm cursor-pointer"
                >
                  {feature}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
