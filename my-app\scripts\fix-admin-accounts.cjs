const { writeFileSync } = require('fs');
const { join } = require('path');

// Create accounts.json with proper admin privileges for Firebase emulator
const accountsData = {
  "kind": "identitytoolkit#DownloadAccountResponse",
  "users": [
    {
      "localId": "adminTestUser123",
      "lastLoginAt": "**********000",
      "emailVerified": true,
      "email": "<EMAIL>",
      "salt": "fakeSalt",
      "passwordHash": "fakeHash:salt=fakeSalt:password=admin123",
      "passwordUpdatedAt": **********000,
      "validSince": "**********",
      "createdAt": "**********000",
      "providerUserInfo": [
        {
          "providerId": "password",
          "email": "<EMAIL>",
          "federatedId": "<EMAIL>",
          "rawId": "<EMAIL>"
        }
      ],
      "lastRefreshAt": "2025-08-02T17:45:17.972Z",
      "customClaims": {
        "admin": true,
        "roles": ["admin"],
        "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]
      }
    },
    {
      "localId": "kUpCde9F1EC48ygdPct0YJktFQdC",
      "lastLoginAt": "**********000",
      "emailVerified": true,
      "email": "<EMAIL>",
      "salt": "fakeSaltOld",
      "passwordHash": "fakeHash:salt=fakeSaltOld:password=password",
      "passwordUpdatedAt": **********000,
      "validSince": "**********",
      "createdAt": "**********000",
      "providerUserInfo": [
        {
          "providerId": "password",
          "email": "<EMAIL>",
          "federatedId": "<EMAIL>",
          "rawId": "<EMAIL>"
        }
      ],
      "lastRefreshAt": "2025-08-02T17:45:17.972Z",
      "customClaims": {
        "admin": true,
        "roles": ["admin"],
        "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]
      }
    }
  ]
};

const accountsPath = join(__dirname, '..', 'data', 'firebase-emulator', 'auth_export', 'accounts.json');

try {
  writeFileSync(accountsPath, JSON.stringify(accountsData));
  console.log('✅ Successfully updated admin accounts!');
  console.log('📧 Admin accounts available:');
  console.log('   1. <EMAIL> / admin123 (primary admin account)');
  console.log('   2. <EMAIL> / password (secondary admin account)');
  console.log('');
  console.log('🔄 Restart the development server to pick up the changes.');
  console.log('   Press Ctrl+C in the terminal running "pnpm run dev" and run it again.');
} catch (error) {
  console.error('❌ Error updating accounts:', error.message);
}
