import { useState, useEffect } from 'react';
import { api } from '@/lib/serverComm';
import { useDebounce } from './useDebounce';

interface SearchParams {
  query: string;
  page?: number;
  limit?: number;
  category?: string;
}

interface Business {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  address: string;
  latitude: string;
  longitude: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: string;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
    description: string;
  };
}

interface SearchResponse {
  businesses: Business[];
  total: number;
  page: number;
  totalPages: number;
  query: string;
}

interface UseSearchReturn {
  businesses: Business[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  totalPages: number;
  query: string;
  search: (params: SearchParams) => void;
  clearResults: () => void;
}

export function useSearch(debounceMs: number = 300): UseSearchReturn {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [query, setQuery] = useState('');
  const [searchParams, setSearchParams] = useState<SearchParams | null>(null);
  
  const debouncedSearchParams = useDebounce(searchParams, debounceMs);

  const search = (params: SearchParams) => {
    setQuery(params.query);
    setSearchParams(params);
  };

  const clearResults = () => {
    setBusinesses([]);
    setTotal(0);
    setPage(1);
    setTotalPages(0);
    setQuery('');
    setSearchParams(null);
    setError(null);
  };

  const performSearch = async (params: SearchParams) => {
    if (!params.query.trim()) {
      clearResults();
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await api.searchBusinesses(params.query, {
        page: params.page,
        limit: params.limit,
        category: params.category,
      });
      
      if (response && response.businesses && Array.isArray(response.businesses)) {
        setBusinesses(response.businesses);
        setTotal(response.total || response.businesses.length);
        setPage(response.page || 1);
        setTotalPages(response.totalPages || 1);
      } else if (response && Array.isArray(response)) {
        // Handle case where response is directly an array
        setBusinesses(response);
        setTotal(response.length);
        setPage(1);
        setTotalPages(1);
      } else {
        setBusinesses([]);
        setTotal(0);
        setPage(1);
        setTotalPages(0);
        console.warn('Unexpected search response format:', response);
      }
    } catch (err) {
      setError('Failed to search businesses');
      console.error('Error searching businesses:', err);
      setBusinesses([]);
      setTotal(0);
      setPage(1);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (debouncedSearchParams) {
      performSearch(debouncedSearchParams);
    }
  }, [debouncedSearchParams]);

  return {
    businesses,
    loading,
    error,
    total,
    page,
    totalPages,
    query,
    search,
    clearResults,
  };
}