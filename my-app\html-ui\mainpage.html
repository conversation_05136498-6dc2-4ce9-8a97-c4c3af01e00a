<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Space+Grotesk%3Awght%40400%3B500%3B700"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden" style='font-family: "Space Grotesk", "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f1f2f3] px-10 py-3">
          <div class="flex items-center gap-8">
            <div class="flex items-center gap-4 text-[#131416]">
              <div class="size-4">
                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M39.475 21.6262C40.358 21.4363 40.6863 21.5589 40.7581 21.5934C40.7876 21.655 40.8547 21.857 40.8082 22.3336C40.7408 23.0255 40.4502 24.0046 39.8572 25.2301C38.6799 27.6631 36.5085 30.6631 33.5858 33.5858C30.6631 36.5085 27.6632 38.6799 25.2301 39.8572C24.0046 40.4502 23.0255 40.7407 22.3336 40.8082C21.8571 40.8547 21.6551 40.7875 21.5934 40.7581C21.5589 40.6863 21.4363 40.358 21.6262 39.475C21.8562 38.4054 22.4689 36.9657 23.5038 35.2817C24.7575 33.2417 26.5497 30.9744 28.7621 28.762C30.9744 26.5497 33.2417 24.7574 35.2817 23.5037C36.9657 22.4689 38.4054 21.8562 39.475 21.6262ZM4.41189 29.2403L18.7597 43.5881C19.8813 44.7097 21.4027 44.9179 22.7217 44.7893C24.0585 44.659 25.5148 44.1631 26.9723 43.4579C29.9052 42.0387 33.2618 39.5667 36.4142 36.4142C39.5667 33.2618 42.0387 29.9052 43.4579 26.9723C44.1631 25.5148 44.659 24.0585 44.7893 22.7217C44.9179 21.4027 44.7097 19.8813 43.5881 18.7597L29.2403 4.41187C27.8527 3.02428 25.8765 3.02573 24.2861 3.36776C22.6081 3.72863 20.7334 4.58419 18.8396 5.74801C16.4978 7.18716 13.9881 9.18353 11.5858 11.5858C9.18354 13.988 7.18717 16.4978 5.74802 18.8396C4.58421 20.7334 3.72865 22.6081 3.36778 24.2861C3.02574 25.8765 3.02429 27.8527 4.41189 29.2403Z"
                    fill="currentColor"
                  ></path>
                </svg>
              </div>
              <h2 class="text-[#131416] text-lg font-bold leading-tight tracking-[-0.015em]">LocalBiz</h2>
            </div>
            <div class="flex items-center gap-9">
              <a class="text-[#131416] text-sm font-medium leading-normal" href="#">Home</a>
              <a class="text-[#131416] text-sm font-medium leading-normal" href="#">Categories</a>
              <a class="text-[#131416] text-sm font-medium leading-normal" href="#">Deals</a>
              <a class="text-[#131416] text-sm font-medium leading-normal" href="#">About</a>
            </div>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <label class="flex flex-col min-w-40 !h-10 max-w-64">
              <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                <div
                  class="text-[#6b7580] flex border-none bg-[#f1f2f3] items-center justify-center pl-4 rounded-l-xl border-r-0"
                  data-icon="MagnifyingGlass"
                  data-size="24px"
                  data-weight="regular"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                    ></path>
                  </svg>
                </div>
                <input
                  placeholder="Search"
                  class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#131416] focus:outline-0 focus:ring-0 border-none bg-[#f1f2f3] focus:border-none h-full placeholder:text-[#6b7580] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                  value=""
                />
              </div>
            </label>
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#cbdbeb] text-[#131416] text-sm font-bold leading-normal tracking-[0.015em]"
            >
              <span class="truncate">List Your Business</span>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCpPEMQKrCNEqV6IgDYAOHWrVjWEs2FZ2Fvq9f0Xg1gG13fW_xU-dkZIf7Oz4ZF0FsVUx7dI-Mxd3ZWxHlUaClb3gN8Q7xstg-ieFaU50W-zt-8RsedAZCoO267zk99lopBBFrqT-2tMMhdxbFnw2Q2maHUXgADvm7xFUSIKkoGuoxB62DRKMvWEmbdnk98gyVUQX6lftgZwnReVtKFwx770lTvUvHOk8WLNlvbLtjfJEBUwtUJ5zAAtxw-ahB7mRVFMlaSpYpTmcfS");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="@container">
              <div class="@[480px]:p-4">
                <div
                  class="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat @[480px]:gap-8 @[480px]:rounded-xl items-center justify-center p-4"
                  style='background-image: linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuCWtKVUafEKWOnZt2gQaZOSKZkhbYF3hMxUiwG--wmT_xB3v7CtQ0--pUpOViHMEMjJVwJXS7EYNUq9TfRGjNFlWS3e6JBlW3sxvyxuIMIEiO07_ec_mGlDORwWCx5NHMzT39IRIPJQgdnJIwCZT9YCKno4HatPCyg7gotXHfjmR4qCq5mnd0tNfp2hrLHArBBdcPWw92bbXfus9Qm7xFt-BJDmoZJudfTbbDYKsswCcZCGhKK9bJx4y9WuQR0Inr23I1UEoVLKLjvB");'
                >
                  <div class="flex flex-col gap-2 text-center">
                    <h1
                      class="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]"
                    >
                      Explore Local Businesses Worldwide
                    </h1>
                    <h2 class="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
                      Discover top-rated services and businesses in any location. Use the interactive globe to find businesses near you or anywhere in the world.
                    </h2>
                  </div>
                  <label class="flex flex-col min-w-40 h-14 w-full max-w-[480px] @[480px]:h-16">
                    <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                      <div
                        class="text-[#6b7580] flex border border-[#dee0e3] bg-white items-center justify-center pl-[15px] rounded-l-xl border-r-0"
                        data-icon="MagnifyingGlass"
                        data-size="20px"
                        data-weight="regular"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                          <path
                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                          ></path>
                        </svg>
                      </div>
                      <input
                        placeholder="Search for businesses or services"
                        class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#131416] focus:outline-0 focus:ring-0 border border-[#dee0e3] bg-white focus:border-[#dee0e3] h-full placeholder:text-[#6b7580] px-[15px] rounded-r-none border-r-0 pr-2 rounded-l-none border-l-0 pl-2 text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal"
                        value=""
                      />
                      <div class="flex items-center justify-center rounded-r-xl border-l-0 border border-[#dee0e3] bg-white pr-[7px]">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#cbdbeb] text-[#131416] text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em]"
                        >
                          <span class="truncate">Search</span>
                        </button>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>
            <div class="@container flex flex-col h-full flex-1">
              <div class="flex flex-1 flex-col @[480px]:px-4 @[480px]:py-3">
                <div
                  class="bg-cover bg-center flex min-h-[320px] flex-1 flex-col justify-between px-4 pb-4 pt-5 @[480px]:rounded-xl @[480px]:px-8 @[480px]:pb-6 @[480px]:pt-8"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDhpzUVgQaKsh3kJQieDYwsaP8Jyx8f8phDDtiw4_em7NGlViZAYSIZBQxozEGKUzGa3XY7wiWPQ5XUFsIYkhreH0ZyrfHA2mSrzu5zebvkQstBdrIGQeyRVLuaJHvRUd-J3HNAjY2vRC4HJU4V8DKJK4wTefgu6ahpYnTN5i8mfkWKoSaKrqDS1Ty0iUBscmn-rC8nBIe36RiMP09Xn-wjijmd02OUKYKXIPNMobBPyFoMeYdVledjBX-neVw69nVD2zkSJD8oz0Nq");'
                >
                  <label class="flex flex-col min-w-40 h-12">
                    <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                      <div
                        class="text-[#6b7580] flex border-none bg-white items-center justify-center pl-4 rounded-l-xl border-r-0"
                        data-icon="MagnifyingGlass"
                        data-size="24px"
                        data-weight="regular"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                          <path
                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                          ></path>
                        </svg>
                      </div>
                      <input
                        placeholder="Search for a location"
                        class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#131416] focus:outline-0 focus:ring-0 border-none bg-white focus:border-none h-full placeholder:text-[#6b7580] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                        value=""
                      />
                    </div>
                  </label>
                  <div class="flex flex-col items-end gap-3">
                    <div class="flex flex-col gap-0.5">
                      <button class="flex size-10 items-center justify-center rounded-t-full bg-white shadow-[0_2px_4px_rgba(0,0,0,0.1)]">
                        <div class="text-[#131416]" data-icon="Plus" data-size="24px" data-weight="regular">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z"></path>
                          </svg>
                        </div>
                      </button>
                      <button class="flex size-10 items-center justify-center rounded-b-full bg-white shadow-[0_2px_4px_rgba(0,0,0,0.1)]">
                        <div class="text-[#131416]" data-icon="Minus" data-size="24px" data-weight="regular">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128Z"></path>
                          </svg>
                        </div>
                      </button>
                    </div>
                    <button class="flex size-10 items-center justify-center rounded-full bg-white shadow-[0_2px_4px_rgba(0,0,0,0.1)]">
                      <div class="text-[#131416]" data-icon="NavigationArrow" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256" transform="scale(-1, 1)">
                          <path
                            d="M229.33,98.21,53.41,33l-.16-.05A16,16,0,0,0,32.9,53.25a1,1,0,0,0,.05.16L98.21,229.33A15.77,15.77,0,0,0,113.28,240h.3a15.77,15.77,0,0,0,15-11.29l23.56-76.56,76.56-23.56a16,16,0,0,0,.62-30.38ZM224,113.3l-76.56,23.56a16,16,0,0,0-10.58,10.58L113.3,224h0l-.06-.17L48,48l175.82,65.22.16.06Z"
                          ></path>
                        </svg>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <h2 class="text-[#131416] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Featured Businesses</h2>
            <div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&amp;::-webkit-scrollbar]:hidden">
              <div class="flex items-stretch p-4 gap-3">
                <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                  <div
                    class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex flex-col"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDanNGuzgepwuU-SRHcBo33f8hX6SQEehZLh87Tze7izia_TCsJ5Tdrb7G4w_WKzxksDVtqxTXDCUcnqO8hvxSOxja-kWFn6vmZz-wbf-nZlCHQ-TBehUqhzY6JOuZSwA5v7k0L3_ERg3dKSoa1L8eSM2araJtXdjsoZOdIYeXUFkU_NkcFMDpsUkrX9EfxYUgnkWgVbExAYtz-GrpdLKdUX0-NCe9D1gTaX_mqSrDSD6SGTlrFpPJJC5lAMJWivYpFQedbiFQllzdL");'
                  ></div>
                  <div>
                    <p class="text-[#131416] text-base font-medium leading-normal">The Cozy Corner Cafe</p>
                    <p class="text-[#6b7580] text-sm font-normal leading-normal">A charming cafe with a warm atmosphere.</p>
                  </div>
                </div>
                <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                  <div
                    class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex flex-col"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB25r3GgIzGp5iWeBBDpX5lgPVfTiqABcz0BOx38jm66EWzJxOIJJCL64eYPyTZ15X8Xmb8GKwWbV8Zqz0BVWu9mXbKNDXXcqV6kyZmrTSeo_yx4mnX10j8zatWbFbulh4Om7VOsmOAMhsR-zCYfdu1q-5H7fbmxFRsN-QKJHH_5McmeVZVOsu98zuCn-15VvgDDJTfy6UbG36exIifYuvVICeoC1Yon0Kip0183piGYMQDaaCo_BjCzxwwGwQajekiDKXICubMsFyV");'
                  ></div>
                  <div>
                    <p class="text-[#131416] text-base font-medium leading-normal">AutoFix Mechanics</p>
                    <p class="text-[#6b7580] text-sm font-normal leading-normal">Expert auto repair services for all makes and models.</p>
                  </div>
                </div>
                <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                  <div
                    class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex flex-col"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBaQxLwimoQDAnpgdfT3kKnzVe_t86Vin4Deab4mtfGrjThSy818wiK5zl5Zq-k3Bv6Fa45EdCHaTDLYE9b1Q8qhaaBUSAzZwjQUnyb_GETV4Se_RY263Rw9HlCwk6IzMtOjpW85N4RyIwrgyFpwsAi4IOqegegXz6BDmAFQM2UU6wJYjN8jQgOSmcq4W9kYcJLnO41yk2g-QnhKiEtQUEx1XSC6d0hwJzz1dMtBBAmEm_LYUNrBAhirwG824uvGrjqxcRVXlOV1uYn");'
                  ></div>
                  <div>
                    <p class="text-[#131416] text-base font-medium leading-normal">Glamour Hair Studio</p>
                    <p class="text-[#6b7580] text-sm font-normal leading-normal">Modern salon offering the latest hair and beauty treatments.</p>
                  </div>
                </div>
                <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                  <div
                    class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex flex-col"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuD16viCbT97AZzVB_yKDUVmm-M9wqf9oZwPHDZGFQ3cpepLJT6qFIue0_sk1YHqBf57tbyV18jACkSWOiB6-zVWm7gt0wtm-jXzgV3KUj3h67lVlYA1eJEDh4YQ4y0YkGHqKC1wGSS7H5YUVKYNunRdLoWwkWzVZNA1Rrq4DKoiftTLkhPPX_Hcly2I-wRLrsh8KfbbYbV_4fIPioxnIDhDAR-H32ZR7znUd9QJwY5Chos5ScO8T-wKbZIZOPEXmRuriJxLRVIIu_2R");'
                  ></div>
                  <div>
                    <p class="text-[#131416] text-base font-medium leading-normal">Sweet Delights Bakery</p>
                    <p class="text-[#6b7580] text-sm font-normal leading-normal">Artisan bakery with fresh, daily baked goods.</p>
                  </div>
                </div>
              </div>
            </div>
            <h2 class="text-[#131416] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Popular Categories</h2>
            <div class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
              <div class="flex flex-col gap-3 pb-3">
                <div
                  class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDmyPWFdYKKBXX3_ioMw4F7A6TVw7pI8ArMZfsieIEBXRRb_TRnV-H6c1uLebemK9hEpBIi8ZHeH0o0dQe6cGwGyAe6X9-KRWdHF9thegpfN_kUSX63wg9Vr_KQ_s_7IYUsCX1TnWK5ES-A2GUD0jWz0jX6HnHANwEnIGEeNDo9AO_4VpQJAGfQacmblLPY-pw4EbMYhGktYIlXY0-CYe6iXnzdfanO_eiaKLLVNxSzzGUhVIYckinXsaA4t4ojTU4p4TbDlufNI9ZS");'
                ></div>
                <p class="text-[#131416] text-base font-medium leading-normal">Restaurants</p>
              </div>
              <div class="flex flex-col gap-3 pb-3">
                <div
                  class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuC5mmF6rsg010ZMovdwGxRG2GA2AXlQjcq2ZPwTCTV6sCVS-mW6K0s63TGhkzed-Lvihqin5gcUE8q835FaCwYhmIzbQk2tAfmabOto7x-qMEPfsTlw22CFz_JA-43X1mN6rVXawWc5Y_25SJpDjXF_20B0s1azkZT4xm7O-MBHWCH2-2Fps2Nh6x5c2M8adPUL21G46PpNpC3CwIvKryknHVvQZBsHlUHp0P7425YFFCrxKjrtjMXcQFM8ccNx3uPNXR43Q_UyKaHV");'
                ></div>
                <p class="text-[#131416] text-base font-medium leading-normal">Home Services</p>
              </div>
              <div class="flex flex-col gap-3 pb-3">
                <div
                  class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBvWDXPsppfC1z4o7bv_2B2AlcSOS7rYcvVobpvpDgE_B96-sHIv_kYrTASxZl3cb-1enuP74c7aGUB4i3boF5IcgV5aWm0R1kKfIPBWS7FjvQ6s_O0UPms3uziesufReV22N2fCUIzqY7wjDqP235l1xWwJCIiLae_AfsZG8Gr6GEpMKMFLJAXnVIwiPGoZ9HGWR4aUiW8PYGlrc416oOCNGDT--aXYm87EO_7qTXopYcMpBxTPOa29qxg_y0UydA0FRhKsaQdiz6t");'
                ></div>
                <p class="text-[#131416] text-base font-medium leading-normal">Beauty &amp; Spas</p>
              </div>
              <div class="flex flex-col gap-3 pb-3">
                <div
                  class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAU5QAHA4PE9eNNvs0yjO3mS1mzs6p_3Nyxjmr9gTxV5pYBfQJs0DcBVcIBvv43JK5yj1PFWXK3fOr8_6y0qd3QxFo6jpTgAs2Tw0ylT60leU2rnHpEGz4vGY-Hg4EKmlwEeVhSv4iV3tcD18fl1SD9LhKGCP7K5T2B1ndC8QbU485guVytxswE_wObZi7U_l1lu-FEDrrnKVhZmhjoxNl-dF8bA0ytj9UYgdrhB9n5U-jqQVxF9QFXtnldTyTStEkFXa0yLGi7QZix");'
                ></div>
                <p class="text-[#131416] text-base font-medium leading-normal">Automotive</p>
              </div>
              <div class="flex flex-col gap-3 pb-3">
                <div
                  class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDwlpvjAKwURjPR_afAmTcCkiSXHFDH2tVbMXiHKBkIouJDqeitmo_6C9eU2HzXsSZIEYD1BbLzE3iilbvicDbrthDEqtSeVIR8iRWn6LPEh5myXdlngtnQJAOaDD3NtWlcptmXi1g9rudD29krsC9NZFMxMCVCiEQ-snrwl0FmUTXD8Ncu3XJ7K3Jw76SqkEwbE0WT4ZEl-R8KPTiYNidFrETPh2dgDQopXY_z8zBYgQI8O_gmVfNZcEnwmk3hzOA9bnGIbiI4QYtq");'
                ></div>
                <p class="text-[#131416] text-base font-medium leading-normal">Health &amp; Medical</p>
              </div>
              <div class="flex flex-col gap-3 pb-3">
                <div
                  class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                  style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAVNP6D6aKGejyRsrRRRK-PqHO1LfphtxCT7R7wugAYBTLi5tFoeBeQxmkxB2UpsqSEw61S3OPzlxOKmyhpsO7txR71r1Aq0GR2_UhGztxeYzxw7dD84Cz3hVslJBbAE6Jp4bmw8CclIcrje0s_Na2hOhVaFjQ_9hrLJBK92YMGm4S6y_ZEhUbAP5qgrLL4-qdnqfuewzKuG2eRsIrniKUmL6pAodRezF62WEOtxnlVYTZCZ3pvVvRs6qH8yXVh1aVyJVI6c5T2ShvB");'
                ></div>
                <p class="text-[#131416] text-base font-medium leading-normal">Professional Services</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
