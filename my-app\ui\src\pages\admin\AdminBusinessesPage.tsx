import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Building, 
  Search, 
  Star, 
  MapPin, 
  Phone, 
  Mail, 
  Globe,
  Edit,
  Trash2,
  Eye,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { adminApi } from '@/lib/admin-api';

export function AdminBusinessesPage() {
  const [businesses, setBusinesses] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchBusinesses = async (page = 1, search = '') => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await adminApi.getAdminBusinesses({
        page,
        limit: 10,
        search: search || undefined,
        sort: 'created_at',
        order: 'desc'
      });
      
      setBusinesses(response.data?.businesses || []);
      setTotalPages(response.data?.pagination?.totalPages || 1);
      setCurrentPage(page);
    } catch (err) {
      console.error('Error fetching businesses:', err);
      setError('Failed to load businesses. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBusinesses(1, searchQuery);
  }, []);

  const handleSearch = () => {
    fetchBusinesses(1, searchQuery);
  };

  const handleToggleFeatured = async (businessId: number) => {
    try {
      await adminApi.toggleBusinessFeatured(businessId);
      fetchBusinesses(currentPage, searchQuery);
    } catch (err) {
      console.error('Error toggling featured status:', err);
    }
  };

  const handleToggleStatus = async (businessId: number) => {
    try {
      await adminApi.toggleBusinessStatus(businessId);
      fetchBusinesses(currentPage, searchQuery);
    } catch (err) {
      console.error('Error toggling business status:', err);
    }
  };

  if (loading && businesses.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span className="text-muted-foreground">Loading businesses...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Business Management</h1>
          <p className="text-muted-foreground">
            Manage and moderate business listings
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search businesses..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <Button onClick={handleSearch}>Search</Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-destructive">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Business List */}
      <div className="space-y-4">
        {businesses.length > 0 ? (
          businesses.map((business) => (
            <Card key={business.id}>
              <CardContent className="pt-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center gap-3">
                      <Building className="w-5 h-5 text-muted-foreground" />
                      <h3 className="text-lg font-semibold">{business.name}</h3>
                      <div className="flex gap-2">
                        {business.is_featured && (
                          <Badge variant="default">Featured</Badge>
                        )}
                        <Badge 
                          variant={business.status === 'active' ? 'default' : 'secondary'}
                        >
                          {business.status}
                        </Badge>
                      </div>
                    </div>
                    
                    <p className="text-muted-foreground">{business.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      {business.address && (
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-muted-foreground" />
                          <span>{business.address}</span>
                        </div>
                      )}
                      {business.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-muted-foreground" />
                          <span>{business.phone}</span>
                        </div>
                      )}
                      {business.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-muted-foreground" />
                          <span>{business.email}</span>
                        </div>
                      )}
                      {business.website && (
                        <div className="flex items-center gap-2">
                          <Globe className="w-4 h-4 text-muted-foreground" />
                          <span>{business.website}</span>
                        </div>
                      )}
                    </div>

                    {business.average_rating && (
                      <div className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm">
                          {business.average_rating.toFixed(1)} ({business.review_count || 0} reviews)
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col gap-2 ml-4">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-2" />
                      View
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                    <Button 
                      size="sm" 
                      variant={business.is_featured ? "default" : "outline"}
                      onClick={() => handleToggleFeatured(business.id)}
                    >
                      {business.is_featured ? 'Unfeature' : 'Feature'}
                    </Button>
                    <Button 
                      size="sm" 
                      variant={business.status === 'active' ? "secondary" : "default"}
                      onClick={() => handleToggleStatus(business.id)}
                    >
                      {business.status === 'active' ? 'Deactivate' : 'Activate'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8 text-muted-foreground">
                No businesses found.
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button 
            variant="outline" 
            disabled={currentPage === 1}
            onClick={() => fetchBusinesses(currentPage - 1, searchQuery)}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {currentPage} of {totalPages}
          </span>
          <Button 
            variant="outline" 
            disabled={currentPage === totalPages}
            onClick={() => fetchBusinesses(currentPage + 1, searchQuery)}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
