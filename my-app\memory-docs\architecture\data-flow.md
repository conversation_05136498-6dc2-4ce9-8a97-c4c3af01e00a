# Data Flow Architecture

## 🔄 Core Data Flows

### 1. Business Directory Browsing Flow

```mermaid
sequenceDiagram
    participant User
    participant React
    participant API
    participant DB
    
    User->>React: Visit homepage
    React->>API: GET /api/businesses?featured=true
    API->>DB: SELECT * FROM businesses WHERE is_featured=true
    DB-->>API: Featured businesses data
    API-->>React: JSON response
    React-->>User: Rendered business cards
    
    User->>React: Search for "restaurants"
    React->>API: GET /api/search?q=restaurants
    API->>DB: Full-text search query
    DB-->>API: Matching businesses
    API-->>React: Search results
    React-->>User: Search results page
```

### 2. Business Application Workflow

```mermaid
sequenceDiagram
    participant Owner as Business Owner
    participant Form as Application Form
    participant API
    participant DB
    participant Admin
    
    Owner->>Form: Fill application details
    Form->>API: POST /api/applications
    API->>DB: INSERT INTO applications
    DB-->>API: Application ID
    API-->>Form: Success response
    Form-->>Owner: Confirmation message
    
    Note over Admin: Admin reviews application
    Admin->>API: PUT /api/admin/applications/:id/approve
    API->>DB: UPDATE applications SET status='approved'
    API->>DB: INSERT INTO businesses (from application)
    DB-->>API: New business created
    API-->>Admin: Success response
```

### 3. Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant User
    participant Firebase
    participant React
    participant API
    participant DB
    
    User->>Firebase: Login with Google
    Firebase-->>React: JWT token
    React->>API: Request with Authorization header
    API->>Firebase: Verify JWT token
    Firebase-->>API: User claims
    API->>DB: SELECT user WHERE id=claims.uid
    DB-->>API: User data with roles
    API-->>React: Protected resource
    React-->>User: Authorized content
```

## 📊 Data Models & Relationships

### Core Entity Relationships

```mermaid
erDiagram
    USERS ||--o{ REVIEWS : writes
    BUSINESSES ||--o{ REVIEWS : receives
    CATEGORIES ||--o{ BUSINESSES : contains
    BUSINESSES ||--o{ PHOTOS : has
    APPLICATIONS ||--|| BUSINESSES : becomes
    
    USERS {
        string id PK
        string email
        string display_name
        boolean is_admin
        timestamp created_at
    }
    
    BUSINESSES {
        int id PK
        string name
        string slug
        int category_id FK
        string description
        string address
        decimal latitude
        decimal longitude
        boolean is_featured
        boolean is_active
        decimal average_rating
        int total_reviews
    }
    
    CATEGORIES {
        int id PK
        string name
        string slug
        string icon
        boolean is_active
    }
    
    REVIEWS {
        int id PK
        int business_id FK
        int rating
        string comment
        string author_name
        boolean is_approved
    }
```

## 🔍 Search & Discovery Patterns

### Search Implementation
- **Full-text search** on business names and descriptions
- **Category filtering** with slug-based URLs
- **Geographic search** using latitude/longitude coordinates
- **Featured business promotion** with priority ranking

### Data Indexing Strategy
```sql
-- Performance indexes for common queries
CREATE INDEX idx_businesses_category ON businesses(category_id);
CREATE INDEX idx_businesses_featured ON businesses(is_featured, is_active);
CREATE INDEX idx_businesses_location ON businesses(latitude, longitude);
CREATE INDEX idx_reviews_business ON reviews(business_id, is_approved);
```

## 🎯 State Management Patterns

### Frontend State Flow
```
User Action → Component State → API Call → Response → State Update → UI Re-render
```

### Key State Categories
- **Authentication State**: User login status, admin roles
- **Business Data**: Listings, details, search results
- **UI State**: Loading states, form validation, modals
- **Theme State**: Dark/light mode preferences

### Data Caching Strategy
- **React Query**: API response caching and synchronization
- **Local Storage**: Theme preferences, user settings
- **Session Storage**: Form data persistence
- **Memory Cache**: Frequently accessed business data

## 🔄 Real-time Updates

### Admin Dashboard Updates
- **Application notifications**: New business applications
- **Review moderation**: Pending review approvals
- **Photo management**: Uploaded image processing

### Public Interface Updates
- **Business status changes**: Approval/rejection notifications
- **Review publication**: Approved reviews appear immediately
- **Featured business rotation**: Dynamic homepage content

## 📈 Performance Optimization Patterns

### Database Query Optimization
- **Eager loading**: Related data in single queries
- **Pagination**: Limit result sets for large datasets
- **Selective fields**: Only fetch required columns
- **Connection pooling**: Efficient database connections

### Frontend Performance
- **Code splitting**: Route-based lazy loading
- **Image optimization**: Responsive images with proper sizing
- **Bundle optimization**: Tree shaking and minification
- **Caching headers**: Static asset caching

---

**Created**: 2025-01-08  
**Last Updated**: 2025-01-08  
**Reviewer**: Code Archaeologist Agent
