// Analytics calculation utilities for admin dashboard

import { eq, and, gte, lte, count, avg, sql, desc } from 'drizzle-orm';
import { getDatabase } from './db';
import * as businessSchema from '../schema/business';
import * as userSchema from '../schema/users';
import type { 
  DashboardStats, 
  CategoryDistribution, 
  TrendData, 
  BusinessPerformanceMetrics,
  CategoryPerformanceMetrics,
  ApplicationAnalytics
} from '../types/admin';

/**
 * Calculate comprehensive dashboard statistics
 */
export async function calculateDashboardStats(): Promise<DashboardStats> {
  const db = await getDatabase();
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const firstOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  // Business statistics
  const [businessStats] = await db
    .select({
      total: count(),
      active: sql<number>`count(case when ${businessSchema.businesses.is_active} = true then 1 end)`,
      inactive: sql<number>`count(case when ${businessSchema.businesses.is_active} = false then 1 end)`,
      featured: sql<number>`count(case when ${businessSchema.businesses.is_featured} = true then 1 end)`,
    })
    .from(businessSchema.businesses);

  // Monthly business growth
  const [monthlyBusinesses] = await db
    .select({ count: count() })
    .from(businessSchema.businesses)
    .where(gte(businessSchema.businesses.created_at, firstOfMonth));

  const [previousMonthBusinesses] = await db
    .select({ count: count() })
    .from(businessSchema.businesses)
    .where(and(
      gte(businessSchema.businesses.created_at, new Date(now.getFullYear(), now.getMonth() - 1, 1)),
      lte(businessSchema.businesses.created_at, new Date(now.getFullYear(), now.getMonth(), 0))
    ));

  const monthlyGrowth = previousMonthBusinesses.count > 0 
    ? ((monthlyBusinesses.count - previousMonthBusinesses.count) / previousMonthBusinesses.count) * 100 
    : 0;

  // Application statistics
  const [applicationStats] = await db
    .select({
      pending: sql<number>`count(case when ${businessSchema.businessApplications.status} = 'pending' then 1 end)`,
      approved: sql<number>`count(case when ${businessSchema.businessApplications.status} = 'approved' then 1 end)`,
      rejected: sql<number>`count(case when ${businessSchema.businessApplications.status} = 'rejected' then 1 end)`,
    })
    .from(businessSchema.businessApplications);

  const [monthlyApplications] = await db
    .select({ count: count() })
    .from(businessSchema.businessApplications)
    .where(gte(businessSchema.businessApplications.submitted_at, firstOfMonth));

  // Calculate average processing time for approved/rejected applications
  const [avgProcessingTime] = await db
    .select({
      avgHours: sql<number>`avg(extract(epoch from (${businessSchema.businessApplications.reviewed_at} - ${businessSchema.businessApplications.submitted_at})) / 3600)`
    })
    .from(businessSchema.businessApplications)
    .where(sql`${businessSchema.businessApplications.reviewed_at} is not null`);

  // Review statistics
  const [reviewStats] = await db
    .select({
      total: count(),
      averageRating: avg(businessSchema.reviews.rating),
      pending: sql<number>`count(case when ${businessSchema.reviews.is_approved} = false then 1 end)`,
      approved: sql<number>`count(case when ${businessSchema.reviews.is_approved} = true then 1 end)`,
    })
    .from(businessSchema.reviews);

  // User statistics
  const [userStats] = await db
    .select({
      total: count(),
      admins: sql<number>`count(case when ${userSchema.users.is_admin} = true then 1 end)`,
    })
    .from(userSchema.users);

  const [monthlyUsers] = await db
    .select({ count: count() })
    .from(userSchema.users)
    .where(gte(userSchema.users.created_at, firstOfMonth));

  const [previousMonthUsers] = await db
    .select({ count: count() })
    .from(userSchema.users)
    .where(and(
      gte(userSchema.users.created_at, new Date(now.getFullYear(), now.getMonth() - 1, 1)),
      lte(userSchema.users.created_at, new Date(now.getFullYear(), now.getMonth(), 0))
    ));

  const userMonthlyGrowth = previousMonthUsers.count > 0 
    ? ((monthlyUsers.count - previousMonthUsers.count) / previousMonthUsers.count) * 100 
    : 0;

  // Category distribution
  const categoryDistribution = await calculateCategoryDistribution();

  return {
    businesses: {
      total: businessStats.total,
      active: businessStats.active,
      inactive: businessStats.inactive,
      featured: businessStats.featured,
      monthlyGrowth: Math.round(monthlyGrowth * 100) / 100,
    },
    applications: {
      pending: applicationStats.pending,
      approved: applicationStats.approved,
      rejected: applicationStats.rejected,
      totalThisMonth: monthlyApplications.count,
      averageProcessingTime: Math.round((avgProcessingTime.avgHours || 0) * 100) / 100,
    },
    reviews: {
      total: reviewStats.total,
      averageRating: Math.round((Number(reviewStats.averageRating) || 0) * 100) / 100,
      pending: reviewStats.pending,
      approved: reviewStats.approved,
      rejected: 0, // We don't have rejected reviews in current schema
      flagged: 0, // We don't have flagged reviews in current schema
    },
    users: {
      total: userStats.total,
      admins: userStats.admins,
      monthlyGrowth: Math.round(userMonthlyGrowth * 100) / 100,
      activeThisMonth: monthlyUsers.count,
    },
    categories: {
      total: categoryDistribution.length,
      businessDistribution: categoryDistribution,
    },
  };
}

/**
 * Calculate category distribution of businesses
 */
export async function calculateCategoryDistribution(): Promise<CategoryDistribution[]> {
  const db = await getDatabase();

  const distribution = await db
    .select({
      categoryId: businessSchema.categories.id,
      categoryName: businessSchema.categories.name,
      businessCount: count(businessSchema.businesses.id),
    })
    .from(businessSchema.categories)
    .leftJoin(businessSchema.businesses, eq(businessSchema.categories.id, businessSchema.businesses.category_id))
    .groupBy(businessSchema.categories.id, businessSchema.categories.name)
    .orderBy(desc(count(businessSchema.businesses.id)));

  const totalBusinesses = distribution.reduce((sum, cat) => sum + cat.businessCount, 0);

  return distribution.map(cat => ({
    categoryId: cat.categoryId,
    categoryName: cat.categoryName,
    businessCount: cat.businessCount,
    percentage: totalBusinesses > 0 ? Math.round((cat.businessCount / totalBusinesses) * 10000) / 100 : 0,
  }));
}

/**
 * Calculate business performance trends over time
 */
export async function calculateBusinessTrends(months: number = 12): Promise<BusinessPerformanceMetrics> {
  const db = await getDatabase();
  const trends: BusinessPerformanceMetrics = {
    totalBusinesses: [],
    newRegistrations: [],
    averageRating: [],
    totalReviews: [],
  };

  for (let i = months - 1; i >= 0; i--) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const period = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    // Total businesses up to this month
    const [totalBusinesses] = await db
      .select({ count: count() })
      .from(businessSchema.businesses)
      .where(lte(businessSchema.businesses.created_at, monthEnd));

    // New registrations this month
    const [newRegistrations] = await db
      .select({ count: count() })
      .from(businessSchema.businesses)
      .where(and(
        gte(businessSchema.businesses.created_at, monthStart),
        lte(businessSchema.businesses.created_at, monthEnd)
      ));

    // Average rating this month
    const [avgRating] = await db
      .select({ avg: avg(businessSchema.reviews.rating) })
      .from(businessSchema.reviews)
      .where(and(
        gte(businessSchema.reviews.created_at, monthStart),
        lte(businessSchema.reviews.created_at, monthEnd)
      ));

    // Total reviews this month
    const [totalReviews] = await db
      .select({ count: count() })
      .from(businessSchema.reviews)
      .where(and(
        gte(businessSchema.reviews.created_at, monthStart),
        lte(businessSchema.reviews.created_at, monthEnd)
      ));

    trends.totalBusinesses.push({ period, value: totalBusinesses.count });
    trends.newRegistrations.push({ period, value: newRegistrations.count });
    trends.averageRating.push({ period, value: Math.round((Number(avgRating.avg) || 0) * 100) / 100 });
    trends.totalReviews.push({ period, value: totalReviews.count });
  }

  // Calculate percentage changes
  const addChanges = (data: TrendData[]) => {
    for (let i = 1; i < data.length; i++) {
      const current = data[i].value;
      const previous = data[i - 1].value;
      data[i].change = previous > 0 ? Math.round(((current - previous) / previous) * 10000) / 100 : 0;
    }
  };

  addChanges(trends.totalBusinesses);
  addChanges(trends.newRegistrations);
  addChanges(trends.averageRating);
  addChanges(trends.totalReviews);

  return trends;
}

/**
 * Calculate category performance metrics
 */
export async function calculateCategoryPerformance(): Promise<CategoryPerformanceMetrics[]> {
  const db = await getDatabase();

  const performance = await db
    .select({
      categoryId: businessSchema.categories.id,
      categoryName: businessSchema.categories.name,
      businessCount: count(businessSchema.businesses.id),
      averageRating: avg(businessSchema.businesses.average_rating),
      totalReviews: sql<number>`sum(${businessSchema.businesses.total_reviews})`,
    })
    .from(businessSchema.categories)
    .leftJoin(businessSchema.businesses, eq(businessSchema.categories.id, businessSchema.businesses.category_id))
    .groupBy(businessSchema.categories.id, businessSchema.categories.name)
    .orderBy(desc(count(businessSchema.businesses.id)));

  // Calculate growth rates (simplified - could be enhanced with historical data)
  return performance.map(cat => ({
    categoryId: cat.categoryId,
    categoryName: cat.categoryName,
    businessCount: cat.businessCount,
    averageRating: Math.round((Number(cat.averageRating) || 0) * 100) / 100,
    totalReviews: cat.totalReviews || 0,
    growthRate: 0, // Would need historical data to calculate actual growth
  }));
}

/**
 * Calculate application analytics
 */
export async function calculateApplicationAnalytics(): Promise<ApplicationAnalytics> {
  const db = await getDatabase();

  // Average processing time
  const [avgProcessingTime] = await db
    .select({
      avgHours: sql<number>`avg(extract(epoch from (${businessSchema.businessApplications.reviewed_at} - ${businessSchema.businessApplications.submitted_at})) / 3600)`
    })
    .from(businessSchema.businessApplications)
    .where(sql`${businessSchema.businessApplications.reviewed_at} is not null`);

  // Approval rate
  const [approvalStats] = await db
    .select({
      total: count(),
      approved: sql<number>`count(case when ${businessSchema.businessApplications.status} = 'approved' then 1 end)`,
    })
    .from(businessSchema.businessApplications)
    .where(sql`${businessSchema.businessApplications.status} != 'pending'`);

  const approvalRate = approvalStats.total > 0 
    ? (approvalStats.approved / approvalStats.total) * 100 
    : 0;

  // Monthly submissions (last 12 months)
  const monthlySubmissions: TrendData[] = [];
  for (let i = 11; i >= 0; i--) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const period = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    const [submissions] = await db
      .select({ count: count() })
      .from(businessSchema.businessApplications)
      .where(and(
        gte(businessSchema.businessApplications.submitted_at, monthStart),
        lte(businessSchema.businessApplications.submitted_at, monthEnd)
      ));

    monthlySubmissions.push({ period, value: submissions.count });
  }

  // Category breakdown
  const categoryBreakdown = await db
    .select({
      categoryId: businessSchema.categories.id,
      categoryName: businessSchema.categories.name,
      applicationCount: count(businessSchema.businessApplications.id),
    })
    .from(businessSchema.categories)
    .leftJoin(businessSchema.businessApplications, eq(businessSchema.categories.id, businessSchema.businessApplications.category_id))
    .groupBy(businessSchema.categories.id, businessSchema.categories.name)
    .orderBy(desc(count(businessSchema.businessApplications.id)));

  const totalApplications = categoryBreakdown.reduce((sum, cat) => sum + cat.applicationCount, 0);

  return {
    averageProcessingTime: Math.round((avgProcessingTime.avgHours || 0) * 100) / 100,
    approvalRate: Math.round(approvalRate * 100) / 100,
    monthlySubmissions,
    categoryBreakdown: categoryBreakdown.map(cat => ({
      categoryId: cat.categoryId,
      categoryName: cat.categoryName,
      businessCount: cat.applicationCount,
      percentage: totalApplications > 0 ? Math.round((cat.applicationCount / totalApplications) * 10000) / 100 : 0,
    })),
  };
}
