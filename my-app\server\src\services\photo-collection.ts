/**
 * Photo Collection Service
 * Handles automated photo collection from external sources (Google Places, Yelp)
 */

import { getEnv } from '../lib/env';
import { getDatabase } from '../lib/db';
import { eq } from 'drizzle-orm';
import * as businessSchema from '../schema/business';

export interface BusinessLocation {
  name: string;
  address: string;
  latitude?: string | null;
  longitude?: string | null;
}

export interface PhotoCollectionResult {
  success: boolean;
  photosCollected: number;
  errors: string[];
}

export interface GooglePlacePhoto {
  photo_reference: string;
  height: number;
  width: number;
  html_attributions: string[];
}

export interface GooglePlaceDetails {
  place_id: string;
  name: string;
  photos?: GooglePlacePhoto[];
}

export class PhotoCollectionService {
  private googleApiKey: string | undefined;

  constructor() {
    this.googleApiKey = getEnv('GOOGLE_PLACES_API_KEY');
  }

  /**
   * Find Google Place ID for a business
   */
  async findGooglePlaceId(business: BusinessLocation): Promise<string | null> {
    if (!this.googleApiKey) {
      console.warn('Google Places API key not configured');
      return null;
    }

    try {
      const query = `${business.name} ${business.address}`;
      const url = new URL('https://maps.googleapis.com/maps/api/place/findplacefromtext/json');
      url.searchParams.set('input', query);
      url.searchParams.set('inputtype', 'textquery');
      url.searchParams.set('fields', 'place_id,name');
      url.searchParams.set('key', this.googleApiKey);

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.status === 'OK' && data.candidates && data.candidates.length > 0) {
        return data.candidates[0].place_id;
      }

      return null;
    } catch (error) {
      console.error('Error finding Google Place ID:', error);
      return null;
    }
  }

  /**
   * Fetch photos from Google Places API
   */
  async fetchGooglePlacesPhotos(placeId: string): Promise<GooglePlacePhoto[]> {
    if (!this.googleApiKey) {
      throw new Error('Google Places API key not configured');
    }

    try {
      const url = new URL('https://maps.googleapis.com/maps/api/place/details/json');
      url.searchParams.set('place_id', placeId);
      url.searchParams.set('fields', 'photos');
      url.searchParams.set('key', this.googleApiKey);

      const response = await fetch(url.toString());
      const data = await response.json();

      if (data.status === 'OK' && data.result && data.result.photos) {
        return data.result.photos;
      }

      return [];
    } catch (error) {
      console.error('Error fetching Google Places photos:', error);
      return [];
    }
  }

  /**
   * Download and save a photo from Google Places
   */
  async downloadAndSavePhoto(
    businessId: number,
    photoRef: string,
    source: string = 'google_places'
  ): Promise<boolean> {
    if (!this.googleApiKey) {
      throw new Error('Google Places API key not configured');
    }

    try {
      // Get photo URL from Google Places
      const photoUrl = new URL('https://maps.googleapis.com/maps/api/place/photo');
      photoUrl.searchParams.set('maxwidth', '800');
      photoUrl.searchParams.set('photo_reference', photoRef);
      photoUrl.searchParams.set('key', this.googleApiKey);

      // For now, we'll store the Google Places photo URL directly
      // In production, you might want to download and store locally
      const db = await getDatabase();
      
      await db.insert(businessSchema.businessImages).values({
        business_id: businessId,
        image_url: photoUrl.toString(),
        photo_source: source,
        source_photo_id: photoRef,
        is_approved: false, // Requires admin approval
        collected_at: new Date(),
        display_order: 999, // Put at end initially
      });

      return true;
    } catch (error) {
      console.error('Error downloading and saving photo:', error);
      return false;
    }
  }

  /**
   * Collect photos for a business from Google Places
   */
  async collectBusinessPhotos(business: BusinessLocation & { id: number }): Promise<PhotoCollectionResult> {
    const result: PhotoCollectionResult = {
      success: false,
      photosCollected: 0,
      errors: []
    };

    try {
      // 1. Find Google Place ID
      const placeId = await this.findGooglePlaceId(business);
      if (!placeId) {
        result.errors.push('Could not find Google Place ID for business');
        return result;
      }

      // 2. Fetch photos from Google Places
      const photos = await this.fetchGooglePlacesPhotos(placeId);
      if (photos.length === 0) {
        result.errors.push('No photos found for business on Google Places');
        return result;
      }

      // 3. Download and save photos (limit to 6 photos)
      const photosToSave = photos.slice(0, 6);
      let savedCount = 0;

      for (const photo of photosToSave) {
        const saved = await this.downloadAndSavePhoto(
          business.id,
          photo.photo_reference,
          'google_places'
        );
        if (saved) {
          savedCount++;
        } else {
          result.errors.push(`Failed to save photo: ${photo.photo_reference}`);
        }
      }

      result.success = savedCount > 0;
      result.photosCollected = savedCount;

      console.log(`Collected ${savedCount} photos for business: ${business.name}`);
      
    } catch (error) {
      console.error('Error collecting business photos:', error);
      result.errors.push(`Photo collection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Get pending photos for admin review
   */
  async getPendingPhotos(businessId?: number) {
    const db = await getDatabase();
    
    let query = db
      .select({
        id: businessSchema.businessImages.id,
        business_id: businessSchema.businessImages.business_id,
        image_url: businessSchema.businessImages.image_url,
        photo_source: businessSchema.businessImages.photo_source,
        source_photo_id: businessSchema.businessImages.source_photo_id,
        collected_at: businessSchema.businessImages.collected_at,
        business_name: businessSchema.businesses.name,
      })
      .from(businessSchema.businessImages)
      .leftJoin(
        businessSchema.businesses,
        businessSchema.eq(businessSchema.businessImages.business_id, businessSchema.businesses.id)
      )
      .where(eq(businessSchema.businessImages.is_approved, false));

    if (businessId) {
      query = query.where(eq(businessSchema.businessImages.business_id, businessId));
    }

    return await query;
  }

  /**
   * Approve a photo
   */
  async approvePhoto(photoId: number): Promise<boolean> {
    try {
      const db = await getDatabase();
      
      await db
        .update(businessSchema.businessImages)
        .set({ is_approved: true })
        .where(eq(businessSchema.businessImages.id, photoId));

      return true;
    } catch (error) {
      console.error('Error approving photo:', error);
      return false;
    }
  }

  /**
   * Reject (delete) a photo
   */
  async rejectPhoto(photoId: number): Promise<boolean> {
    try {
      const db = await getDatabase();
      
      await db
        .delete(businessSchema.businessImages)
        .where(eq(businessSchema.businessImages.id, photoId));

      return true;
    } catch (error) {
      console.error('Error rejecting photo:', error);
      return false;
    }
  }
}

// Export singleton instance
export const photoCollectionService = new PhotoCollectionService();
