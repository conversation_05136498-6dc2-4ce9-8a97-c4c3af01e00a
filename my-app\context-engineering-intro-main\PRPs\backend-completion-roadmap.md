# **PRP: Business Directory Backend Completion**

## **Context & Objective**

Complete the business directory backend by implementing missing public APIs, business services, and enhanced functionality while maintaining consistency with the existing Hono.js + Drizzle ORM architecture.

### **Current State Analysis**
- ✅ **Foundation**: Hono.js + TypeScript + Drizzle ORM + PostgreSQL established
- ✅ **Database Schema**: Complete business tables with all relationships
- ✅ **Admin APIs**: Comprehensive admin management system (`/api/v1/admin/*`)
- ✅ **Authentication**: Firebase Auth middleware with admin/user roles
- ✅ **Infrastructure**: Database connection, migrations, environment handling
- ❌ **Public APIs**: No public business discovery endpoints
- ❌ **Search System**: No search/filtering functionality for public use
- ❌ **Business Services**: No business logic layer
- ❌ **Image Services**: No image upload/processing system
- ❌ **Email Services**: No notification system
- ❌ **Geocoding**: No address-to-coordinates conversion

## **Implementation Blueprint**

### **Phase 1: Public Business APIs**
```typescript
// 1. Create public business routes
// File: server/src/routes/public-businesses.ts
const publicBusinessRoutes = new Hono();

// GET /api/v1/businesses - Public business listing
publicBusinessRoutes.get('/', async (c) => {
  // Pagination, filtering, sorting
  // No authentication required
});

// GET /api/v1/businesses/:slug - Single business profile
publicBusinessRoutes.get('/:slug', async (c) => {
  // Include all business data, hours, images, reviews
});

// Pattern follows existing admin routes in:
// server/src/routes/admin-businesses.ts (lines 12-50)
```

### **Phase 2: Search & Discovery APIs**
```typescript
// 2. Create search functionality
// File: server/src/routes/public-search.ts
const searchRoutes = new Hono();

// GET /api/v1/search - Business search
searchRoutes.get('/', async (c) => {
  // Keyword, location, category, radius filtering
  // Full-text search implementation
});

// GET /api/v1/categories - Public categories
searchRoutes.get('/categories', async (c) => {
  // Categories with business counts
});
```

### **Phase 3: Business Services Layer**
```typescript
// 3. Create business services
// File: server/src/services/business-service.ts
export class BusinessService {
  static async getBusinesses(params: BusinessListParams): Promise<BusinessListResponse>
  static async getBusiness(slug: string): Promise<BusinessResponse>
  static async searchBusinesses(query: SearchParams): Promise<SearchResponse>
  static async getBusinessReviews(businessId: number): Promise<ReviewsResponse>
}

// Pattern follows existing database queries in:
// server/src/routes/admin-businesses.ts (lines 25-50)
```

## **Critical Context & Patterns**

### **Existing Hono.js Route Patterns**
```typescript
// From server/src/routes/admin-businesses.ts (lines 12-23):
businessRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search');
    const category = c.req.query('category');
    const offset = (page - 1) * limit;
    const db = await getDatabase();
    
    // Build where conditions
    const whereConditions = [];
    // ... filtering logic
  } catch (error) {
    console.error('Error:', error);
    return c.json({ error: 'Failed to fetch data' }, 500);
  }
});
```

### **Existing Database Query Patterns**
```typescript
// From server/src/routes/admin-businesses.ts (lines 60-85):
const businesses = await db
  .select({
    id: businessSchema.businesses.id,
    name: businessSchema.businesses.name,
    slug: businessSchema.businesses.slug,
    // ... other fields
    category: {
      id: businessSchema.categories.id,
      name: businessSchema.categories.name,
      slug: businessSchema.categories.slug,
    }
  })
  .from(businessSchema.businesses)
  .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
  .where(and(...whereConditions))
  .orderBy(sortOrder === 'desc' ? desc(sortField) : asc(sortField))
  .limit(limit)
  .offset(offset);
```

### **Existing Schema References**
```typescript
// From server/src/schema/business.ts:
// ✅ Available tables:
// - businesses (lines 26-47)
// - categories (lines 14-23)
// - businessHours (lines 50-58)
// - businessImages (lines 61-69)
// - reviews (lines 72-83)
// - businessApplications (lines 86-100)

// ✅ Available relationships:
// - business.category_id → categories.id
// - businessHours.business_id → businesses.id
// - businessImages.business_id → businesses.id
// - reviews.business_id → businesses.id
```

### **Existing API Mount Pattern**
```typescript
// From server/src/api.ts (lines 410-416):
mainAdminRoutes.route('/businesses', businessRoutes);
mainAdminRoutes.route('/categories', categoryRoutes);
mainAdminRoutes.route('/reviews', reviewRoutes);

// Mount admin routes:
api.route('/v1/admin', mainAdminRoutes);

// Pattern to follow for public routes:
api.route('/v1', publicRoutes);
```

## **External Dependencies & Documentation**

### **Geocoding Service**
- **Google Geocoding API**: https://developers.google.com/maps/documentation/geocoding/overview
- **Alternative**: Mapbox Geocoding API: https://docs.mapbox.com/api/search/geocoding/
- **Implementation**: Create `server/src/services/geocoding-service.ts`

### **Image Upload & Processing**
- **Cloudflare Images**: https://developers.cloudflare.com/images/
- **Alternative**: AWS S3 + CloudFront
- **Local Development**: File system storage with validation

### **Email Service**
- **Resend**: https://resend.com/docs (recommended for Cloudflare Workers)
- **Alternative**: SendGrid, Mailgun
- **Templates**: HTML email templates for notifications

### **Full-Text Search**
- **PostgreSQL Full-Text Search**: https://www.postgresql.org/docs/current/textsearch.html
- **Implementation**: Use `to_tsvector` and `to_tsquery` for search functionality

## **Implementation Tasks (Ordered)**

### **Week 1: Public Business APIs**
1. **Create Public Business Routes** (`server/src/routes/public-businesses.ts`)
   - GET `/api/v1/businesses` - Business listing with pagination/filtering
   - GET `/api/v1/businesses/:slug` - Single business profile
   - Include business hours, images, category, and basic review stats
   - Follow existing admin route patterns for consistency

2. **Create Public Category Routes** (`server/src/routes/public-categories.ts`)
   - GET `/api/v1/categories` - All categories with business counts
   - GET `/api/v1/categories/:slug` - Businesses in specific category
   - Include category icons and descriptions

3. **Mount Public Routes** (Update `server/src/api.ts`)
   - Add public routes to main API without authentication
   - Ensure proper CORS and rate limiting
   - Add API versioning structure

### **Week 2: Search & Discovery System**
4. **Implement Search Functionality** (`server/src/routes/public-search.ts`)
   - GET `/api/v1/search` - Full-text search with filters
   - Support keyword, location, category, radius filtering
   - Implement PostgreSQL full-text search
   - Add search result ranking and relevance

5. **Create Location-Based Search** (`server/src/services/location-service.ts`)
   - Distance calculation using coordinates
   - Radius-based business filtering
   - Location autocomplete suggestions
   - Integration with geocoding service

6. **Add Advanced Filtering** (Enhance search routes)
   - Price range, ratings, features filtering
   - Open now / hours-based filtering
   - Featured businesses prioritization
   - Sort by distance, rating, newest

### **Week 3: Business Services & Data Enhancement**
7. **Create Business Service Layer** (`server/src/services/business-service.ts`)
   - Centralize business logic and data access
   - Implement caching for frequently accessed data
   - Add business data validation and sanitization
   - Create reusable query builders

8. **Implement Review System** (`server/src/routes/public-reviews.ts`)
   - GET `/api/v1/businesses/:id/reviews` - Business reviews
   - POST `/api/v1/reviews` - Submit new review
   - Review moderation and approval workflow
   - Rating calculation and aggregation

9. **Add Business Application System** (`server/src/routes/public-applications.ts`)
   - POST `/api/v1/applications` - Submit business application
   - Email notifications for new applications
   - Application status tracking
   - Integration with admin approval workflow

### **Week 4: Enhanced Features & Services**
10. **Implement Image Service** (`server/src/services/image-service.ts`)
    - Image upload handling and validation
    - Image resizing and optimization
    - Multiple image support per business
    - CDN integration for image delivery

11. **Create Email Service** (`server/src/services/email-service.ts`)
    - Application submission notifications
    - Application approval/rejection emails
    - Review notification emails
    - HTML email templates

12. **Add Analytics & Reporting** (`server/src/services/analytics-service.ts`)
    - Business view tracking
    - Search analytics
    - Popular categories and businesses
    - Admin dashboard statistics

### **Week 5: Performance & Production Features**
13. **Implement Caching Layer** (`server/src/services/cache-service.ts`)
    - Redis integration for caching
    - Business data caching
    - Search result caching
    - Cache invalidation strategies

14. **Add Rate Limiting & Security** (`server/src/middleware/`)
    - API rate limiting per IP
    - Request validation and sanitization
    - SQL injection prevention
    - CORS configuration

15. **Create Bulk Operations** (`server/src/services/bulk-service.ts`)
    - Bulk business import/export
    - CSV data processing
    - Batch operations for admin
    - Data migration utilities

## **Error Handling Strategy**

### **Consistent Error Response Format**
```typescript
// Pattern for all API responses:
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: PaginationInfo;
}

// Error handling pattern:
try {
  const result = await businessService.getBusinesses(params);
  return c.json({ success: true, data: result });
} catch (error) {
  console.error('Business API Error:', error);
  return c.json({ 
    success: false, 
    error: 'Failed to fetch businesses' 
  }, 500);
}
```

### **Database Error Handling**
```typescript
// Pattern for database operations:
try {
  const db = await getDatabase();
  const result = await db.select().from(table);
  return result;
} catch (error) {
  if (error.code === '23505') { // Unique constraint violation
    throw new Error('Duplicate entry');
  } else if (error.code === '23503') { // Foreign key violation
    throw new Error('Referenced record not found');
  }
  throw new Error('Database operation failed');
}
```

## **Validation Gates**

### **Development Validation**
```bash
# Type checking
npm run type-check

# Database migrations
npm run db:migrate

# API testing
npm run test:api

# Build verification
npm run build
```

### **API Endpoint Testing**
```bash
# Start development server
npm run dev

# Test public endpoints:
curl "http://localhost:5500/api/v1/businesses"
curl "http://localhost:5500/api/v1/businesses/test-business"
curl "http://localhost:5500/api/v1/categories"
curl "http://localhost:5500/api/v1/search?q=restaurant&location=New+York"

# Test admin endpoints (with auth):
curl -H "Authorization: Bearer <token>" "http://localhost:5500/api/v1/admin/businesses"
```

### **Database Validation**
```sql
-- Verify data integrity:
SELECT COUNT(*) FROM app.businesses WHERE is_active = true;
SELECT COUNT(*) FROM app.categories WHERE is_active = true;
SELECT COUNT(*) FROM app.reviews WHERE is_approved = true;

-- Test search functionality:
SELECT * FROM app.businesses 
WHERE to_tsvector('english', name || ' ' || description) 
@@ to_tsquery('english', 'restaurant');
```

## **Success Criteria**

### **Functional Requirements**
- [ ] Public business listing API with pagination and filtering
- [ ] Single business profile API with complete data
- [ ] Category listing and filtering APIs
- [ ] Full-text search with location and category filters
- [ ] Review submission and display system
- [ ] Business application submission system
- [ ] Image upload and management system
- [ ] Email notification system

### **Performance Requirements**
- [ ] API response times < 200ms for cached data
- [ ] API response times < 500ms for database queries
- [ ] Support for 1000+ concurrent requests
- [ ] Efficient database queries with proper indexing
- [ ] Caching for frequently accessed data

### **Security Requirements**
- [ ] Input validation and sanitization
- [ ] SQL injection prevention
- [ ] Rate limiting implementation
- [ ] Proper CORS configuration
- [ ] Authentication for protected endpoints
- [ ] Admin authorization for admin endpoints

## **Quality Score: 9/10**

**Confidence Level**: Very High for one-pass implementation success

**Strengths**:
- Comprehensive analysis of existing codebase patterns
- Clear implementation path following established Hono.js + Drizzle patterns
- Complete database schema already exists
- Admin system provides excellent reference patterns
- Detailed task breakdown with specific file references
- Proper error handling and validation strategies

**Risk Mitigation**:
- All patterns follow existing admin route conventions
- Database schema is complete and tested
- TypeScript interfaces ensure type safety
- Validation gates prevent regression
- Incremental implementation reduces complexity
- Existing admin APIs provide proven patterns to follow
