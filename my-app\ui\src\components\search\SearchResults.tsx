import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BusinessCard } from '@/components/business/BusinessCard';
import { Search, AlertCircle } from 'lucide-react';

interface Business {
  id: number;
  name: string;
  slug: string;
  short_description: string;
  address: string;
  phone: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
  };
}

interface SearchResponse {
  businesses: Business[];
  query: string;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

interface SearchResultsProps {
  results: SearchResponse | null;
  loading: boolean;
  error: string | null;
  onPageChange: (page: number) => void;
  currentPage: number;
}

export function SearchResults({ 
  results, 
  loading, 
  error, 
  onPageChange, 
  currentPage 
}: SearchResultsProps) {
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="h-48 bg-muted"></div>
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded mb-2"></div>
                <div className="h-3 bg-muted rounded mb-4"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-xl font-semibold mb-2">Search Error</h3>
        <p className="text-muted-foreground">{error}</p>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="text-center py-12">
        <Search className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-xl font-semibold mb-2">Start Your Search</h3>
        <p className="text-muted-foreground">
          Enter a search term to find local businesses and services.
        </p>
      </div>
    );
  }

  if (results.businesses.length === 0) {
    return (
      <div className="text-center py-12">
        <Search className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-xl font-semibold mb-2">No Results Found</h3>
        <p className="text-muted-foreground mb-4">
          We couldn't find any businesses matching "{results.query}".
        </p>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>Try:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>Checking your spelling</li>
            <li>Using different keywords</li>
            <li>Removing filters</li>
            <li>Searching for a broader category</li>
          </ul>
        </div>
      </div>
    );
  }

  const { businesses, pagination } = results;

  return (
    <div className="space-y-6">
      {/* Results Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {businesses.map((business) => (
          <BusinessCard 
            key={business.id} 
            business={business} 
            showCategory={true}
            category={business.category}
          />
        ))}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex justify-center items-center gap-4 pt-8">
          <Button
            variant="outline"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          
          <div className="flex items-center gap-2">
            {/* Show page numbers */}
            {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
              let pageNum;
              if (pagination.pages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= pagination.pages - 2) {
                pageNum = pagination.pages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <Button
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(pageNum)}
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === pagination.pages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Results Summary */}
      <div className="text-center text-sm text-muted-foreground pt-4 border-t">
        Showing {((currentPage - 1) * pagination.limit) + 1} to{' '}
        {Math.min(currentPage * pagination.limit, pagination.total)} of{' '}
        {pagination.total} results
      </div>
    </div>
  );
}
