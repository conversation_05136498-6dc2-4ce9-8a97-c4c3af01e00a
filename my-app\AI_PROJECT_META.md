# AI Project Meta - Volo App Template

**Project Name:** Volo App Template  
**Project Type:** Full-stack web application  
**Primary Languages:** TypeScript, JavaScript  
**Framework/Stack:** React + Hono + PostgreSQL + Firebase Auth  
**Deployment Target:** Cloudflare Pages + Workers  

---

## 1. Project Overview

This is a **full-stack web application template** with:
- **Frontend**: React 19 + TypeScript + Vite + Tailwind CSS + ShadCN/UI
- **Backend**: Hono API framework (Node.js/Cloudflare Workers)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Firebase Auth with Google Sign-In
- **Architecture**: Monorepo with separate UI and server packages

### High-Level Architecture
- **Vertical slice architecture** with feature-based organization
- **API-first design** with protected/public route separation
- **Component-driven UI** with co-located styles and logic
- **Environment-agnostic backend** (Node.js local, Cloudflare Workers production)

---

## 2. Folder and File Organization

```
my-app/
├── ui/                     # React frontend (Vite + TypeScript)
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   │   ├── ui/        # ShadCN base components
│   │   │   └── *.tsx      # App-specific components
│   │   ├── pages/         # Route components
│   │   ├── lib/           # Utilities, auth, API communication
│   │   └── hooks/         # Custom React hooks
│   └── package.json
├── server/                 # Hono API backend
│   ├── src/
│   │   ├── api.ts         # Main API routes and middleware
│   │   ├── lib/           # Database, auth, utilities
│   │   ├── middleware/    # Auth and other middleware
│   │   └── schema/        # Drizzle database schemas
│   └── package.json
├── design/                 # Design system and UI specifications
│   └── desgn-system.md    # Color tokens, typography, spacing
├── data/                   # Local development data
│   ├── postgres/          # Embedded PostgreSQL data
│   └── firebase-emulator/ # Firebase emulator data
├── docs/                   # Project documentation
└── scripts/               # Development and setup scripts
```

### File Size Limits
- **Maximum 500 lines per file** (recommended)
- **Components should be single-responsibility**
- **Break large files into smaller, focused modules**

### Co-location Rules
- **UI components**: Keep component, styles, and tests together
- **API routes**: Group related endpoints in the same file
- **Database schemas**: One schema per domain/feature
- **Design system**: Centralized in `/design` folder with CSS custom properties

---

## 3. Language and Coding Standards

### TypeScript Configuration
- **Strict mode enabled** across all packages
- **Path aliases**: Use `@/` for src imports in UI package
- **Type safety**: All API responses and database models must be typed

### Code Style Tools
- **ESLint**: Configured for React + TypeScript
- **Prettier**: Auto-formatting (if configured)
- **Import organization**: Relative imports preferred for local modules

### Naming Conventions
- **Files**: kebab-case (`user-profile.tsx`, `auth-middleware.ts`)
- **Components**: PascalCase (`UserProfile`, `LoginForm`)
- **Functions/variables**: camelCase (`getCurrentUser`, `isAuthenticated`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`, `DATABASE_URL`)
- **Database tables**: snake_case (`users`, `user_sessions`)

---

## 4. Design System and UI Standards

### Design Token Architecture
- **CSS Custom Properties**: All design tokens defined as CSS variables
- **Theme Support**: Light and dark theme variants with automatic switching
- **Color System**: Semantic color tokens (primary, secondary, muted, destructive)
- **Typography**: DM Serif Display and Roboto Mono with 8pt rhythm system
- **Spacing**: 8px base unit scaling system (8px, 16px, 24px, 32px...)

### Design System Location
- **Central file**: `/design/desgn-system.md` contains all design specifications
- **Implementation**: CSS custom properties in UI package
- **Components**: ShadCN/UI components follow design system tokens

### Color Token Usage
```css
/* Light theme primary colors */
--primary: #6e56cf;           /* Main brand color */
--primary-foreground: #ffffff; /* Text on primary */
--secondary: #e4dfff;         /* Secondary actions */
--muted: #f0f0fa;            /* Subtle backgrounds */
--destructive: #ff5470;       /* Error/danger states */
```

### Typography Scale
- **Font sizes**: 12px to 48px following 8pt rhythm
- **Line heights**: Consistent vertical rhythm
- **Font families**: DM Serif Display (headings), Roboto Mono (code)
- **Usage**: Semantic sizing tokens (xs, sm, base, lg, xl, 2xl, 3xl, 4xl)

### Spacing System
- **Base unit**: 8px for consistent spacing
- **Scale**: 0px, 8px, 16px, 24px, 32px, 40px, 48px, 56px, 64px, 72px, 80px
- **Usage**: Apply to margins, padding, gaps, and positioning

### Shadow System
- **Elevation levels**: 2xs through 2xl for depth hierarchy
- **Consistent shadows**: HSL-based with opacity for theme compatibility
- **Usage**: Cards, modals, dropdowns, and elevated components

### Design System Rules
- ✅ **Always use design tokens** instead of hardcoded values
- ✅ **Follow spacing scale** for consistent layouts
- ✅ **Use semantic colors** (primary, secondary, muted) not specific colors
- ✅ **Maintain theme compatibility** for light/dark mode support
- 🚫 **Don't hardcode colors** or spacing values in components
- 🚫 **Don't break typography rhythm** with arbitrary font sizes

---

## 5. Security and Secrets Management

### Environment Variables
- **Local development**: `.env` files in server package
- **Production**: Cloudflare Workers environment variables
- **Firebase config**: Public config in UI, private keys in server

### Critical Security Rules
- 🚫 **Never commit secrets** to version control
- ✅ **Always validate inputs** using Drizzle schemas or Zod
- ✅ **Use Firebase Admin SDK** for server-side auth verification
- ✅ **Implement CORS** properly for cross-origin requests
- ✅ **Sanitize database queries** (Drizzle ORM handles this)

### Authentication Flow
- **Frontend**: Firebase Auth SDK for user authentication
- **Backend**: Firebase Admin SDK for token verification
- **Protected routes**: Use `authMiddleware` for server endpoints
- **Token handling**: Automatic token refresh in `serverComm.ts`

---

## 6. Testing Requirements

### Current State
- ⚠️ **No tests currently implemented**
- 📋 **Testing framework**: Vitest configured in server package

### Testing Structure (To Be Implemented)
```
ui/src/
├── components/
│   └── __tests__/         # Component tests
server/src/
├── __tests__/             # API and integration tests
└── lib/
    └── __tests__/         # Unit tests for utilities
```

### Testing Requirements
- **Unit tests**: All utility functions and business logic
- **Component tests**: Critical UI components
- **API tests**: All endpoints (public and protected)
- **Integration tests**: Database operations and auth flows

---

## 7. Development Workflow

### Local Development Setup
```bash
# Install dependencies
pnpm install

# Start development servers
pnpm dev  # Runs UI, server, database, and Firebase emulator
```

### Common Commands
- **Frontend dev**: `cd ui && pnpm dev`
- **Backend dev**: `cd server && pnpm dev`
- **Database push**: `cd server && pnpm db:push`
- **Linting**: `cd ui && pnpm lint`
- **Build**: `pnpm build` (from root)

### Required Tools
- **Node.js**: >=20.0.0
- **pnpm**: >=8.0.0
- **Firebase CLI**: For auth emulator
- **PostgreSQL**: Embedded for local, external for production

---

## 8. Git and Version Control

### Branching Model
- **Main branch**: `main` (production-ready code)
- **Feature branches**: `feature/description` or `fix/description`
- **No direct commits** to main branch

### Commit Message Style
```
type(scope): description

feat(auth): add Google sign-in integration
fix(api): resolve CORS issue for protected routes
docs(readme): update setup instructions
design(ui): update color tokens for better contrast
```

### Pre-commit Checks
- ✅ **Linting passes** (ESLint)
- ✅ **TypeScript compilation** succeeds
- ✅ **No console.log** statements in production code
- ✅ **Environment variables** properly configured
- ✅ **Design tokens** used instead of hardcoded values

---

## 9. Documentation Standards

### Code Documentation
- **Public functions**: JSDoc comments required
- **Complex logic**: Inline comments explaining why, not what
- **API endpoints**: Document parameters and responses
- **Component props**: TypeScript interfaces with descriptions

### Project Documentation
- **README files**: Each package has its own README
- **API documentation**: Document all endpoints in server README
- **Setup guides**: Clear instructions for new developers
- **Architecture decisions**: Document major design choices
- **Design system**: Maintained in `/design/desgn-system.md`

---

## 10. Performance Guidelines

### Frontend Performance
- **React 19 compiler**: Let React optimize performance automatically
- **Code splitting**: Use dynamic imports for large components
- **Image optimization**: Proper sizing and formats
- **Bundle analysis**: Monitor bundle size with Vite

### Backend Performance
- **Database queries**: Use Drizzle ORM efficiently
- **Caching**: Implement where appropriate (Redis, etc.)
- **Connection pooling**: Configure for production databases
- **Response compression**: Enable in production

### General Rules
- 🎯 **Clean, readable code first** - optimize only when needed
- 📊 **Measure before optimizing** - use profiling tools
- ⚡ **Avoid premature optimization** - follow YAGNI principle

---

## 11. Deployment and Release Process

### Backend Deployment (Cloudflare Workers)
```bash
cd server
pnpm run deploy
```

### Frontend Deployment (Cloudflare Pages)
- **Automatic**: Connect Git repository to Cloudflare Pages
- **Manual**: Build locally and upload dist folder

### Pre-deployment Checklist
- ✅ **All tests pass** (when implemented)
- ✅ **Environment variables** configured
- ✅ **Database migrations** applied
- ✅ **Firebase project** configured for production
- ✅ **CORS settings** updated for production domains
- ✅ **Design tokens** properly implemented

### Release Process
1. **Feature development** in feature branch
2. **Code review** and testing
3. **Merge to main** branch
4. **Deploy backend** to Cloudflare Workers
5. **Deploy frontend** via Cloudflare Pages
6. **Verify deployment** and monitor for issues

---

## 12. Code Review Checklist

### Functionality
- ✅ **Code works** as intended
- ✅ **Edge cases** handled appropriately
- ✅ **Error handling** implemented
- ✅ **No breaking changes** without migration plan

### Code Quality
- ✅ **Follows existing patterns** and conventions
- ✅ **Reuses existing utilities** and components
- ✅ **No code duplication** without justification
- ✅ **Proper TypeScript types** used throughout

### Security
- ✅ **Input validation** implemented
- ✅ **Authentication** properly enforced
- ✅ **No sensitive data** exposed
- ✅ **SQL injection** prevention (Drizzle ORM)

### Performance
- ✅ **No unnecessary re-renders** (React)
- ✅ **Efficient database queries**
- ✅ **Proper error boundaries**
- ✅ **Bundle size** impact considered

### Design System Compliance
- ✅ **Uses design tokens** instead of hardcoded values
- ✅ **Follows spacing scale** for consistent layouts
- ✅ **Maintains theme compatibility** (light/dark)
- ✅ **Typography scale** properly applied

---

## 13. Debugging and Monitoring

### Logging Conventions
- **Frontend**: Use `console.error` for errors, avoid `console.log` in production
- **Backend**: Use Hono's logger middleware
- **Database**: Enable query logging in development
- **Authentication**: Log auth failures and token issues

### Development Debugging
- **React DevTools**: For component debugging
- **Network tab**: For API request/response inspection
- **Firebase emulator**: For auth debugging
- **Database tools**: Direct PostgreSQL access for data inspection

### Production Monitoring
- **Cloudflare Analytics**: For performance metrics
- **Error tracking**: Implement Sentry or similar (not yet configured)
- **Database monitoring**: Connection pool and query performance
- **Auth monitoring**: Failed login attempts and token issues

---

## 14. Critical Violations Summary

### 🚫 Absolute Don'ts
- **Don't delete existing code** unless explicitly required for the task
- **Don't ignore TypeScript errors** - fix them or add proper types
- **Don't commit secrets** or API keys to version control
- **Don't bypass authentication** on protected routes
- **Don't modify database schema** without proper migrations
- **Don't introduce breaking changes** without migration plan
- **Don't hardcode URLs** or configuration values
- **Don't catch-all exceptions** - be specific about error handling
- **Don't hardcode colors or spacing** - use design tokens

### ⚠️ Warning Triggers
- **Large file sizes** (>500 lines) - consider breaking up
- **Duplicate code** - extract to shared utilities
- **Missing error handling** - all API calls should handle failures
- **Untyped data** - all external data should be validated
- **Direct DOM manipulation** - use React patterns instead
- **Inline styles** - use Tailwind classes or design tokens
- **Hardcoded design values** - use CSS custom properties from design system

---

## 15. Pattern Awareness

### 🔍 Always Check First
Before implementing new functionality:
1. **Search existing codebase** for similar patterns
2. **Check if utilities exist** for common operations
3. **Review existing components** for reusable parts
4. **Validate against established patterns** in the project

### Established Patterns
- **API communication**: Use `serverComm.ts` patterns
- **Authentication**: Follow `auth-context.tsx` patterns
- **Database operations**: Use Drizzle ORM patterns in `lib/db.ts`
- **Component structure**: Follow ShadCN/UI component patterns
- **Routing**: Use React Router patterns in `App.tsx`
- **State management**: Use React Context for global state
- **Design system**: Use CSS custom properties from `/design/desgn-system.md`

### Integration Requirements
- **New API routes**: Add to `api.ts` with proper middleware
- **New components**: Follow ShadCN/UI patterns and design system tokens
- **New database tables**: Create in `schema/` with proper types
- **New pages**: Add to routing in `App.tsx`
- **New utilities**: Add to appropriate `lib/` directory
- **Design changes**: Update design system file and implement via CSS custom properties

---

**Last Updated**: 2025-01-08  
**Maintainer**: Volo App Template Team  
**Review Schedule**: Monthly or after major feature additions

---

> 💡 **For AI Assistants**: This file serves as the source of truth for project standards. Always reference these patterns and rules before making changes. When in doubt, ask for clarification rather than assuming implementation details.
