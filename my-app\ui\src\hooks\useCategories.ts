import { useState, useEffect } from 'react';
import { api } from '@/lib/serverComm';

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
  business_count?: number;
}

interface UseCategoriesReturn {
  categories: Category[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useCategories(): UseCategoriesReturn {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.getCategories();
      
      if (response && response.categories && Array.isArray(response.categories)) {
        setCategories(response.categories);
      } else if (response && Array.isArray(response)) {
        // Handle case where response is directly an array
        setCategories(response);
      } else {
        setCategories([]);
        console.warn('Unexpected categories response format:', response);
      }
    } catch (err) {
      setError('Failed to load categories');
      console.error('Error fetching categories:', err);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
  };
}