const { writeFileSync } = require('fs');
const { join } = require('path');

// Create accounts.json with admin privileges
const accountsData = {
  "kind": "identitytoolkit#DownloadAccountResponse",
  "users": [
    {
      "localId": "kUpCde9F1EC48ygdPct0YJktFQdC",
      "lastLoginAt": "**********971",
      "emailVerified": false,
      "email": "<EMAIL>",
      "salt": "fakeSaltxg4q2pJvLFqwhoaLb3vk",
      "passwordHash": "fakeHash:salt=fakeSaltxg4q2pJvLFqwhoaLb3vk:password= cbcbcbcbc",
      "passwordUpdatedAt": **********971,
      "validSince": "**********",
      "createdAt": "**********971",
      "providerUserInfo": [
        {
          "providerId": "password",
          "email": "<EMAIL>",
          "federatedId": "<EMAIL>",
          "rawId": "<EMAIL>"
        }
      ],
      "lastRefreshAt": "2025-08-02T15:45:17.972Z",
      "customClaims": {
        "admin": true,
        "roles": ["admin"],
        "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]
      }
    },
    {
      "localId": "adminTestUser123",
      "lastLoginAt": "**********971",
      "emailVerified": false,
      "email": "<EMAIL>",
      "salt": "fakeSaltAdmin123",
      "passwordHash": "fakeHash:salt=fakeSaltAdmin123:password=admin123",
      "passwordUpdatedAt": **********971,
      "validSince": "**********",
      "createdAt": "**********971",
      "providerUserInfo": [
        {
          "providerId": "password",
          "email": "<EMAIL>",
          "federatedId": "<EMAIL>",
          "rawId": "<EMAIL>"
        }
      ],
      "lastRefreshAt": "2025-08-02T15:45:17.972Z",
      "customClaims": {
        "admin": true,
        "roles": ["admin"],
        "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]
      }
    }
  ]
};

const accountsPath = join(__dirname, '..', 'data', 'firebase-emulator', 'auth_export', 'accounts.json');

try {
  writeFileSync(accountsPath, JSON.stringify(accountsData));
  console.log('✅ Successfully created admin accounts!');
  console.log('📧 Admin accounts available:');
  console.log('   1. <EMAIL> (existing account with admin privileges)');
  console.log('   2. <EMAIL> / admin123 (new admin account)');
  console.log('');
  console.log('🔄 Please restart the Firebase emulator to pick up the changes.');
} catch (error) {
  console.error('❌ Error creating accounts:', error.message);
}
