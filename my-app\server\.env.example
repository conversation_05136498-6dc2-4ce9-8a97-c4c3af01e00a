# Node.js Development Environment
PORT=8787
NODE_ENV=development

# Database Configuration
# For local development, embedded-postgres is used by default
# For production deployment, you need a standalone PostgreSQL database
DATABASE_URL={{DATABASE_URL}}

# Firebase Configuration (<PERSON><PERSON><PERSON> approach)
FIREBASE_PROJECT_ID={{FIREBASE_PROJECT_ID}}

# Google Places API Configuration (for automated photo collection)
GOOGLE_PLACES_API_KEY={{GOOGLE_PLACES_API_KEY}}

# Cloudflare Configuration
WORKER_NAME={{WORKER_NAME}}