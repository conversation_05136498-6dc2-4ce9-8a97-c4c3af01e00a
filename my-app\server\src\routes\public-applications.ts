// Public application routes
import { Hono } from 'hono';
import { eq } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';

const publicApplicationRoutes = new Hono();

// Submit business application (public)
publicApplicationRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Validate required fields
    const requiredFields = ['business_name', 'category_id', 'description', 'address', 'email', 'contact_person'];
    const missingFields = requiredFields.filter(field => !body[field]);
    
    if (missingFields.length > 0) {
      return c.json({ 
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}` 
      }, 400);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return c.json({ 
        success: false,
        error: 'Invalid email format' 
      }, 400);
    }

    // Validate category exists
    const [category] = await db
      .select({ id: businessSchema.categories.id })
      .from(businessSchema.categories)
      .where(eq(businessSchema.categories.id, body.category_id))
      .limit(1);

    if (!category) {
      return c.json({ 
        success: false,
        error: 'Invalid category ID' 
      }, 400);
    }

    const [application] = await db
      .insert(businessSchema.businessApplications)
      .values({
        business_name: body.business_name,
        category_id: body.category_id,
        description: body.description,
        address: body.address,
        latitude: body.latitude || null,
        longitude: body.longitude || null,
        phone: body.phone || null,
        email: body.email,
        website: body.website || null,
        contact_person: body.contact_person,
        additional_info: body.additional_info || null,
        status: 'pending',
      })
      .returning();

    return c.json({
      success: true,
      data: {
        message: 'Application submitted successfully',
        application_id: application.id
      }
    }, 201);
  } catch (error) {
    console.error('Error submitting application:', error);
    return c.json({ 
      success: false,
      error: 'Failed to submit application' 
    }, 500);
  }
});

// Get application status (public - requires application ID)
publicApplicationRoutes.get('/:id/status', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    const [application] = await db
      .select({
        id: businessSchema.businessApplications.id,
        business_name: businessSchema.businessApplications.business_name,
        status: businessSchema.businessApplications.status,
        submitted_at: businessSchema.businessApplications.submitted_at,
        reviewed_at: businessSchema.businessApplications.reviewed_at,
        admin_notes: businessSchema.businessApplications.admin_notes,
        approved_business_id: businessSchema.businessApplications.approved_business_id,
      })
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.id, id))
      .limit(1);

    if (!application) {
      return c.json({ 
        success: false,
        error: 'Application not found' 
      }, 404);
    }

    return c.json({
      success: true,
      data: { application }
    });
  } catch (error) {
    console.error('Error fetching application status:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch application status' 
    }, 500);
  }
});

export default publicApplicationRoutes;