import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Sheet, Sheet<PERSON>ontent, SheetTrigger, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { ThemeSelector } from '@/components/theme/ThemeSelector';
import { Menu, Home, Search, Grid3X3, Building, Phone, Mail } from 'lucide-react';

export function MobileNav() {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/search', label: 'Search', icon: Search },
    { href: '/categories', label: 'Categories', icon: Grid3X3 },
    { href: '/apply', label: 'List Business', icon: Building },
  ];

  const closeNav = () => setIsOpen(false);

  return (
    <div className="md:hidden">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="h-10 w-10">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Open menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80">
          <SheetHeader>
            <SheetTitle className="text-left">Business Directory</SheetTitle>
          </SheetHeader>
          
          <div className="mt-8 space-y-4">
            {/* Navigation Links */}
            <nav className="space-y-2">
              {navItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    to={item.href}
                    onClick={closeNav}
                    className="flex items-center gap-3 px-3 py-3 rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
                  >
                    <Icon className="h-5 w-5" />
                    <span className="font-medium">{item.label}</span>
                  </Link>
                );
              })}
            </nav>

            {/* Divider */}
            <div className="border-t pt-4 mt-6">
              <div className="space-y-4">
                <h3 className="font-semibold text-sm text-muted-foreground uppercase tracking-wider">
                  Quick Actions
                </h3>
                
                <div className="space-y-2">
                  <Link
                    to="/apply"
                    onClick={closeNav}
                    className="block w-full"
                  >
                    <Button className="w-full justify-start" size="lg">
                      <Building className="h-4 w-4 mr-2" />
                      List Your Business
                    </Button>
                  </Link>
                  
                  <Link
                    to="/search"
                    onClick={closeNav}
                    className="block w-full"
                  >
                    <Button variant="outline" className="w-full justify-start" size="lg">
                      <Search className="h-4 w-4 mr-2" />
                      Find Businesses
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            {/* Theme Selection */}
            <div className="border-t pt-4 mt-6">
              <h3 className="font-semibold text-sm text-muted-foreground uppercase tracking-wider mb-3">
                Appearance
              </h3>
              <ThemeSelector
                variant="buttons"
                orientation="vertical"
                size="sm"
                className="w-full"
              />
            </div>

            {/* Contact Info */}
            <div className="border-t pt-4 mt-6">
              <h3 className="font-semibold text-sm text-muted-foreground uppercase tracking-wider mb-3">
                Contact
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>(*************</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
