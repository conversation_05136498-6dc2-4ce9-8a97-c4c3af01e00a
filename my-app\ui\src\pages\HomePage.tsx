import { HeroSection } from '@/components/homepage/HeroSection';
import { CategoryHighlights } from '@/components/homepage/CategoryHighlights';
import { FeaturedBusinesses } from '@/components/homepage/FeaturedBusinesses';
import { FeaturedProducts } from '@/components/homepage/FeaturedProducts';
import { NewBusinesses } from '@/components/homepage/NewBusinesses';
import { MarqueeBanner } from '@/components/homepage/MarqueeBanner';
import { InteractiveMapSection } from '@/components/homepage/InteractiveMapSection';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { useNavigate } from 'react-router-dom';

export function HomePage() {
  const navigate = useNavigate();

  const handleSearch = (query: string, location?: string) => {
    const params = new URLSearchParams();
    params.set('q', query);
    if (location) params.set('location', location);
    navigate(`/search?${params.toString()}`);
  };

  return (
    <div className="min-h-screen">
      {/* Marquee Banner */}
      <ErrorBoundary>
        <MarqueeBanner />
      </ErrorBoundary>

      <div className="px-40 flex flex-1 justify-center py-5">
        <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
          {/* Hero Section */}
          <ErrorBoundary>
            <HeroSection onSearch={handleSearch} />
          </ErrorBoundary>

          {/* Interactive Map Section */}
          <ErrorBoundary>
            <InteractiveMapSection />
          </ErrorBoundary>

          {/* Featured Businesses */}
          <ErrorBoundary>
            <FeaturedBusinesses />
          </ErrorBoundary>

          {/* Category Highlights */}
          <ErrorBoundary>
            <CategoryHighlights />
          </ErrorBoundary>
        </div>
      </div>

      {/* Featured Products - Full Width Carousel */}
      <ErrorBoundary>
        <FeaturedProducts />
      </ErrorBoundary>
    </div>
  );
}
