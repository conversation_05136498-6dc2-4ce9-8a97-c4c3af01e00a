/**
 * Theme Showcase Component
 * 
 * Demonstrates all available theme components and their variants
 * Useful for testing and documentation purposes
 */

import * as React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  ModeToggle,
  ModeToggleButton, 
  ModeToggleCompact, 
  ModeToggleText, 
  ModeToggleAnimated,
  ModeToggleSwitch,
  ModeToggleSwitchMinimal,
  ModeToggleSwitchCustom,
  ModeToggleDropdown,
  ModeToggleDropdownCompact,
  ModeToggleDropdownDetailed,
  ThemeSelector,
  ThemeSelectorCompact,
  ThemeSelectorDetailed
} from './index';

export function ThemeShowcase() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="h1">Theme Components Showcase</h1>
        <p className="body-text text-muted-foreground">
          Explore all available theme toggle components and their variants
        </p>
      </div>

      {/* Mode Toggle Variants */}
      <Card>
        <CardHeader>
          <CardTitle className="h3">Mode Toggle Variants</CardTitle>
          <CardDescription>
            Different styles of theme toggle buttons
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="h5">Original Toggle</h4>
              <div className="flex items-center gap-2">
                <ModeToggle />
                <span className="caption">Icon only</span>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="h5">Button Variants</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <ModeToggleButton variant="outline" />
                  <span className="caption">Outline</span>
                </div>
                <div className="flex items-center gap-2">
                  <ModeToggleButton variant="ghost" />
                  <span className="caption">Ghost</span>
                </div>
                <div className="flex items-center gap-2">
                  <ModeToggleButton variant="secondary" />
                  <span className="caption">Secondary</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="h5">Compact & Text</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <ModeToggleCompact />
                  <span className="caption">Compact</span>
                </div>
                <div className="flex items-center gap-2">
                  <ModeToggleText />
                  <span className="caption">Text only</span>
                </div>
                <div className="flex items-center gap-2">
                  <ModeToggleAnimated />
                  <span className="caption">Animated</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Switch Variants */}
      <Card>
        <CardHeader>
          <CardTitle className="h3">Switch Variants</CardTitle>
          <CardDescription>
            Switch-style theme toggles with different configurations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="h5">Horizontal Switches</h4>
              <div className="space-y-3">
                <ModeToggleSwitch orientation="horizontal" />
                <ModeToggleSwitchMinimal />
                <ModeToggleSwitchCustom lightLabel="Day" darkLabel="Night" />
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="h5">Vertical Switch</h4>
              <div className="flex justify-center">
                <ModeToggleSwitch orientation="vertical" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dropdown Variants */}
      <Card>
        <CardHeader>
          <CardTitle className="h3">Dropdown Variants</CardTitle>
          <CardDescription>
            Dropdown menus for theme selection with system option
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="h5">Standard Dropdown</h4>
              <ModeToggleDropdown />
            </div>

            <div className="space-y-3">
              <h4 className="h5">Compact Dropdown</h4>
              <ModeToggleDropdownCompact />
            </div>

            <div className="space-y-3">
              <h4 className="h5">Detailed Dropdown</h4>
              <ModeToggleDropdownDetailed />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Selectors */}
      <Card>
        <CardHeader>
          <CardTitle className="h3">Theme Selectors</CardTitle>
          <CardDescription>
            Multi-option theme selectors with different layouts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-6">
            <div className="space-y-3">
              <h4 className="h5">Button Selector (Horizontal)</h4>
              <ThemeSelector variant="buttons" orientation="horizontal" />
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="h5">Button Selector (Vertical)</h4>
              <ThemeSelector variant="buttons" orientation="vertical" />
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="h5">Radio Selector</h4>
              <ThemeSelector variant="radio" orientation="horizontal" />
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="h5">Card Selector</h4>
              <ThemeSelectorDetailed />
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="h5">Compact Selector</h4>
              <ThemeSelectorCompact />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Size Variants */}
      <Card>
        <CardHeader>
          <CardTitle className="h3">Size Variants</CardTitle>
          <CardDescription>
            Different sizes for various use cases
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h4 className="h5">Small</h4>
              <div className="space-y-2">
                <ModeToggleButton size="sm" />
                <ThemeSelector size="sm" />
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="h5">Medium (Default)</h4>
              <div className="space-y-2">
                <ModeToggleButton size="md" />
                <ThemeSelector size="md" />
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="h5">Large</h4>
              <div className="space-y-2">
                <ModeToggleButton size="lg" />
                <ThemeSelector size="lg" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="h3">Usage Examples</CardTitle>
          <CardDescription>
            Real-world usage scenarios and combinations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="p-4 border rounded-lg">
              <h4 className="h5 mb-3">Header Navigation</h4>
              <div className="flex items-center justify-between">
                <span className="label">Business Directory</span>
                <div className="flex items-center gap-3">
                  <span className="caption">Theme:</span>
                  <ModeToggleDropdown />
                </div>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <h4 className="h5 mb-3">Settings Panel</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="label">Appearance</span>
                    <p className="caption">Choose your preferred theme</p>
                  </div>
                  <ThemeSelector variant="buttons" size="sm" />
                </div>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <h4 className="h5 mb-3">Mobile Menu</h4>
              <div className="space-y-3">
                <span className="label">Appearance</span>
                <ThemeSelector variant="buttons" orientation="vertical" size="sm" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
