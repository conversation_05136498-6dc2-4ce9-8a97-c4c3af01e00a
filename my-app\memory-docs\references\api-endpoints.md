# API Endpoints Reference

## 🌐 Base URLs

- **Local Development**: `http://localhost:8787`
- **Production**: `https://your-domain.workers.dev`

## 🔐 Authentication

All admin endpoints require Firebase JWT token in Authorization header:
```
Authorization: Bearer <firebase-jwt-token>
```

## 📋 Public Endpoints

### Business Directory

#### GET /api/businesses
Get list of businesses with optional filtering.

**Query Parameters:**
- `featured` (boolean): Filter featured businesses
- `category` (string): Filter by category slug
- `limit` (number): Limit results (default: 20)
- `offset` (number): Pagination offset

**Response:**
```json
{
  "businesses": [
    {
      "id": 1,
      "name": "Business Name",
      "slug": "business-name",
      "category": "restaurants",
      "description": "Business description",
      "address": "123 Main St",
      "phone": "+1234567890",
      "website": "https://example.com",
      "average_rating": 4.5,
      "total_reviews": 25,
      "is_featured": true
    }
  ],
  "total": 100
}
```

#### GET /api/businesses/:slug
Get detailed business information.

**Response:**
```json
{
  "id": 1,
  "name": "Business Name",
  "slug": "business-name",
  "category": {
    "id": 1,
    "name": "Restaurants",
    "slug": "restaurants"
  },
  "description": "Detailed business description",
  "short_description": "Brief description",
  "address": "123 Main St",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "phone": "+1234567890",
  "email": "<EMAIL>",
  "website": "https://example.com",
  "logo_url": "https://example.com/logo.jpg",
  "hero_image_url": "https://example.com/hero.jpg",
  "average_rating": 4.5,
  "total_reviews": 25,
  "is_featured": true,
  "photos": [
    {
      "id": 1,
      "url": "https://example.com/photo1.jpg",
      "caption": "Photo caption"
    }
  ],
  "reviews": [
    {
      "id": 1,
      "rating": 5,
      "comment": "Great service!",
      "author_name": "John Doe",
      "created_at": "2025-01-08T10:00:00Z"
    }
  ]
}
```

### Categories

#### GET /api/categories
Get list of all active categories.

**Response:**
```json
{
  "categories": [
    {
      "id": 1,
      "name": "Restaurants",
      "slug": "restaurants",
      "icon": "utensils",
      "description": "Dining and food services",
      "business_count": 45
    }
  ]
}
```

### Search

#### GET /api/search
Search businesses by name, description, or category.

**Query Parameters:**
- `q` (string, required): Search query
- `category` (string): Filter by category slug
- `limit` (number): Limit results (default: 20)

**Response:**
```json
{
  "results": [
    {
      "id": 1,
      "name": "Business Name",
      "slug": "business-name",
      "category": "restaurants",
      "description": "Business description",
      "address": "123 Main St",
      "average_rating": 4.5,
      "total_reviews": 25
    }
  ],
  "total": 10,
  "query": "pizza"
}
```

### Applications

#### POST /api/applications
Submit a new business application.

**Request Body:**
```json
{
  "business_name": "New Business",
  "category_id": 1,
  "description": "Business description",
  "address": "123 Main St",
  "phone": "+1234567890",
  "email": "<EMAIL>",
  "website": "https://business.com",
  "owner_name": "John Doe",
  "owner_email": "<EMAIL>"
}
```

**Response:**
```json
{
  "id": 123,
  "status": "pending",
  "message": "Application submitted successfully"
}
```

### Reviews

#### POST /api/reviews
Submit a new review for a business.

**Request Body:**
```json
{
  "business_id": 1,
  "rating": 5,
  "comment": "Great service!",
  "author_name": "John Doe",
  "author_email": "<EMAIL>"
}
```

## 🔒 Admin Endpoints

### Business Management

#### GET /api/admin/businesses
Get all businesses for admin management.

#### PUT /api/admin/businesses/:id
Update business information.

#### DELETE /api/admin/businesses/:id
Delete a business (soft delete).

### Application Management

#### GET /api/admin/applications
Get all pending applications.

#### PUT /api/admin/applications/:id/approve
Approve a business application.

#### PUT /api/admin/applications/:id/reject
Reject a business application.

### Review Management

#### GET /api/admin/reviews
Get all reviews for moderation.

#### PUT /api/admin/reviews/:id/approve
Approve a review.

#### PUT /api/admin/reviews/:id/reject
Reject a review.

### Photo Management

#### GET /api/admin/photos
Get all uploaded photos for review.

#### PUT /api/admin/photos/:id/approve
Approve a photo.

#### DELETE /api/admin/photos/:id
Delete a photo.

## 🚨 Error Responses

All endpoints return consistent error format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Invalid input data
- `UNAUTHORIZED`: Missing or invalid authentication
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMITED`: Too many requests
- `INTERNAL_ERROR`: Server error

---

**Created**: 2025-01-08  
**Last Updated**: 2025-01-08  
**Maintainer**: Documentation Specialist Agent
