<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Calculator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="calculator_theme_1.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: var(--background);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: var(--font-sans);
        }
        .calculator {
            background-color: var(--card);
            border-radius: var(--radius);
            padding: calc(var(--spacing) * 4);
            box-shadow: var(--shadow-xl);
            width: 100%;
            max-width: 360px;
        }
        .display {
            background-color: var(--muted);
            color: var(--foreground);
            font-family: var(--font-mono);
            font-size: 3rem;
            text-align: right;
            padding: calc(var(--spacing) * 4);
            border-radius: calc(var(--radius) / 2);
            margin-bottom: calc(var(--spacing) * 4);
            overflow-x: auto;
            transition: all 0.3s ease-in-out;
        }
        .display-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0.5; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: calc(var(--spacing) * 3);
        }
        .btn {
            background-color: var(--secondary);
            color: var(--secondary-foreground);
            border: none;
            border-radius: calc(var(--radius) / 1.5);
            font-size: 1.5rem;
            font-weight: 500;
            padding: calc(var(--spacing) * 4);
            cursor: pointer;
            transition: all 0.15s ease-out;
            box-shadow: var(--shadow-sm);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        .btn:active {
            transform: translateY(0px) scale(0.98);
            box-shadow: var(--shadow-xs);
        }
        .btn-operator {
            background-color: var(--primary);
            color: var(--primary-foreground);
        }
        .btn-operator.active {
            background-color: var(--accent);
            box-shadow: var(--shadow-lg), 0 0 15px 0 var(--accent);
        }
        .btn-special {
            background-color: var(--muted);
            color: var(--muted-foreground);
        }
        .btn-zero {
            grid-column: span 2;
        }
    </style>
</head>
<body>
    <div class="calculator">
        <div id="display" class="display">0</div>
        <div class="buttons">
            <button class="btn btn-special" data-action="clear">AC</button>
            <button class="btn btn-special" data-action="negate">+/-</button>
            <button class="btn btn-special" data-action="percent">%</button>
            <button class="btn btn-operator" data-action="divide">÷</button>

            <button class="btn" data-number="7">7</button>
            <button class="btn" data-number="8">8</button>
            <button class="btn" data-number="9">9</button>
            <button class="btn btn-operator" data-action="multiply">x</button>

            <button class="btn" data-number="4">4</button>
            <button class="btn" data-number="5">5</button>
            <button class="btn" data-number="6">6</button>
            <button class="btn btn-operator" data-action="subtract">-</button>

            <button class="btn" data-number="1">1</button>
            <button class="btn" data-number="2">2</button>
            <button class="btn" data-number="3">3</button>
            <button class="btn btn-operator" data-action="add">+</button>

            <button class="btn btn-zero" data-number="0">0</button>
            <button class="btn" data-action="decimal">.</button>
            <button class="btn btn-operator" data-action="equals">=</button>
        </div>
    </div>

    <script>
        const display = document.getElementById('display');
        const buttons = document.querySelector('.buttons');

        let displayValue = '0';
        let firstOperand = null;
        let operator = null;
        let waitingForSecondOperand = false;

        function updateDisplay() {
            display.textContent = displayValue;
            display.classList.add('display-fade-in');
            setTimeout(() => display.classList.remove('display-fade-in'), 300);
        }

        updateDisplay();

        buttons.addEventListener('click', (event) => {
            const { target } = event;
            if (!target.matches('button')) {
                return;
            }

            if (target.dataset.number) {
                inputDigit(target.dataset.number);
                return;
            }

            if (target.dataset.action === 'decimal') {
                inputDecimal();
                return;
            }

            if (target.dataset.action === 'clear') {
                resetCalculator();
                return;
            }
            
            if (target.dataset.action === 'negate') {
                negateValue();
                return;
            }

            if (target.dataset.action === 'percent') {
                calculatePercent();
                return;
            }

            if (target.classList.contains('btn-operator')) {
                handleOperator(target.dataset.action);
            }
        });

        function inputDigit(digit) {
            if (waitingForSecondOperand) {
                displayValue = digit;
                waitingForSecondOperand = false;
            } else {
                displayValue = displayValue === '0' ? digit : displayValue + digit;
            }
            updateDisplay();
        }

        function inputDecimal() {
            if (!displayValue.includes('.')) {
                displayValue += '.';
            }
            updateDisplay();
        }

        function resetCalculator() {
            displayValue = '0';
            firstOperand = null;
            operator = null;
            waitingForSecondOperand = false;
            clearActiveOperator();
            updateDisplay();
        }
        
        function negateValue() {
            displayValue = (parseFloat(displayValue) * -1).toString();
            updateDisplay();
        }

        function calculatePercent() {
            displayValue = (parseFloat(displayValue) / 100).toString();
            updateDisplay();
        }

        function handleOperator(nextOperator) {
            const inputValue = parseFloat(displayValue);

            if (operator && waitingForSecondOperand) {
                operator = nextOperator;
                setActiveOperator(nextOperator);
                return;
            }

            if (firstOperand === null) {
                firstOperand = inputValue;
            } else if (operator) {
                const result = performCalculation[operator](firstOperand, inputValue);
                displayValue = String(result);
                firstOperand = result;
            }

            waitingForSecondOperand = true;
            operator = nextOperator;
            
            setActiveOperator(nextOperator);
            updateDisplay();
        }
        
        const performCalculation = {
            'divide': (first, second) => first / second,
            'multiply': (first, second) => first * second,
            'subtract': (first, second) => first - second,
            'add': (first, second) => first + second,
            'equals': (first, second) => second
        };
        
        function setActiveOperator(op) {
            clearActiveOperator();
            const operatorButton = document.querySelector(`[data-action=${op}]`);
            if (operatorButton) {
                operatorButton.classList.add('active');
            }
        }

        function clearActiveOperator() {
            const activeOp = document.querySelector('.btn-operator.active');
            if (activeOp) {
                activeOp.classList.remove('active');
            }
        }

    </script>
</body>
</html>
