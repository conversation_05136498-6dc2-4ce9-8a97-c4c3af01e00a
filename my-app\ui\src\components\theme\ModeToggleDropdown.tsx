/**
 * Mode Toggle Dropdown Component
 * 
 * A dropdown menu for selecting between Light/Dark/System themes
 */

import * as React from 'react';
import { Sun, Moon, Monitor, Check, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useThemePreference } from '@/hooks/useThemePreference';
import { getTransitionClasses, themeConfig, type ThemeOption } from '@/lib/theme-utils';

interface ModeToggleDropdownProps {
  showSystemOption?: boolean;
  placement?: 'bottom' | 'top' | 'left' | 'right';
  trigger?: 'click' | 'hover';
  showTriggerText?: boolean;
  showTriggerIcon?: boolean;
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ModeToggleDropdown({
  showSystemOption = true,
  placement = 'bottom',
  trigger = 'click',
  showTriggerText = true,
  showTriggerIcon = true,
  variant = 'outline',
  size = 'sm',
  className,
  ...props
}: ModeToggleDropdownProps) {
  const { 
    theme, 
    setTheme, 
    resolvedTheme, 
    mounted,
    displayName,
    isActiveTheme,
    availableThemes 
  } = useThemePreference();

  // Show loading state during hydration
  if (!mounted) {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn('gap-2', className)}
        disabled
        {...props}
      >
        {showTriggerIcon && <Sun className="h-4 w-4" />}
        {showTriggerText && <span className="label">Theme</span>}
        <ChevronDown className="h-3 w-3" />
      </Button>
    );
  }

  const getThemeIcon = (themeName: string) => {
    switch (themeName) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const getCurrentThemeIcon = () => {
    if (theme === 'system') {
      return <Monitor className="h-4 w-4" />;
    }
    return resolvedTheme === 'dark' 
      ? <Moon className="h-4 w-4" /> 
      : <Sun className="h-4 w-4" />;
  };

  const themesToShow = showSystemOption 
    ? availableThemes 
    : availableThemes.filter(t => t !== 'system');

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn(
            'gap-2',
            getTransitionClasses('button'),
            className
          )}
          aria-label="Select theme"
          {...props}
        >
          {showTriggerIcon && (
            <span className={getTransitionClasses('icon')}>
              {getCurrentThemeIcon()}
            </span>
          )}
          {showTriggerText && (
            <span className="label">
              {displayName}
            </span>
          )}
          <ChevronDown className="h-3 w-3 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end"
        className={cn(
          'min-w-[140px]',
          getTransitionClasses('normal')
        )}
      >
        {themesToShow.map((themeName) => {
          const isActive = isActiveTheme(themeName);
          const config = themeConfig[themeName];
          
          return (
            <DropdownMenuItem
              key={themeName}
              onClick={() => setTheme(themeName)}
              className={cn(
                'flex items-center gap-2 cursor-pointer',
                getTransitionClasses('fast'),
                isActive && 'bg-accent text-accent-foreground'
              )}
            >
              <span className="flex items-center gap-2 flex-1">
                {getThemeIcon(themeName)}
                <span className="label">{config.label}</span>
              </span>
              {isActive && (
                <Check className="h-3 w-3 text-primary" />
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

/**
 * Compact Mode Toggle Dropdown
 * 
 * Icon-only dropdown for space-constrained layouts
 */
export function ModeToggleDropdownCompact({
  className,
  ...props
}: Omit<ModeToggleDropdownProps, 'showTriggerText' | 'size'>) {
  return (
    <ModeToggleDropdown
      showTriggerText={false}
      size="sm"
      className={cn('h-8 w-8 p-0', className)}
      {...props}
    />
  );
}

/**
 * Mode Toggle Dropdown with Descriptions
 * 
 * Dropdown with detailed descriptions for each theme option
 */
export function ModeToggleDropdownDetailed({
  className,
  ...props
}: ModeToggleDropdownProps) {
  const { 
    theme, 
    setTheme, 
    resolvedTheme, 
    mounted,
    displayName,
    isActiveTheme,
    availableThemes,
    systemTheme
  } = useThemePreference();

  if (!mounted) {
    return <ModeToggleDropdown className={className} {...props} />;
  }

  const getThemeDescription = (themeName: string) => {
    switch (themeName) {
      case 'light':
        return 'Light theme with bright colors';
      case 'dark':
        return 'Dark theme with muted colors';
      case 'system':
        return `Follow system preference (${systemTheme})`;
      default:
        return 'Unknown theme';
    }
  };

  const getCurrentThemeIcon = () => {
    if (theme === 'system') {
      return <Monitor className="h-4 w-4" />;
    }
    return resolvedTheme === 'dark' 
      ? <Moon className="h-4 w-4" /> 
      : <Sun className="h-4 w-4" />;
  };

  const getThemeIcon = (themeName: string) => {
    switch (themeName) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            'gap-2',
            getTransitionClasses('button'),
            className
          )}
          aria-label="Select theme"
          {...props}
        >
          <span className={getTransitionClasses('icon')}>
            {getCurrentThemeIcon()}
          </span>
          <span className="label">{displayName}</span>
          <ChevronDown className="h-3 w-3 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end"
        className={cn(
          'min-w-[200px]',
          getTransitionClasses('normal')
        )}
      >
        {availableThemes.map((themeName) => {
          const isActive = isActiveTheme(themeName);
          const config = themeConfig[themeName];
          
          return (
            <DropdownMenuItem
              key={themeName}
              onClick={() => setTheme(themeName)}
              className={cn(
                'flex flex-col items-start gap-1 cursor-pointer p-3',
                getTransitionClasses('fast'),
                isActive && 'bg-accent text-accent-foreground'
              )}
            >
              <div className="flex items-center gap-2 w-full">
                <span className="flex items-center gap-2 flex-1">
                  {getThemeIcon(themeName)}
                  <span className="label font-medium">{config.label}</span>
                </span>
                {isActive && (
                  <Check className="h-3 w-3 text-primary" />
                )}
              </div>
              <span className="caption text-left">
                {getThemeDescription(themeName)}
              </span>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
