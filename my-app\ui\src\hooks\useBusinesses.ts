import { useState, useEffect } from 'react';
import { api } from '@/lib/serverComm';

interface BusinessListParams {
  page?: number;
  limit?: number;
  category?: string;
  featured?: boolean;
}

interface Business {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  address: string;
  latitude: string;
  longitude: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: string;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
    description: string;
  };
}

interface BusinessListResponse {
  businesses: Business[];
  total: number;
  page: number;
  totalPages: number;
}

interface UseBusinessesReturn {
  businesses: Business[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  totalPages: number;
  refetch: () => void;
}

export function useBusinesses(params: BusinessListParams = {}): UseBusinessesReturn {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const fetchBusinesses = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.getBusinesses(params);
      
      if (response && response.businesses && Array.isArray(response.businesses)) {
        setBusinesses(response.businesses);
        setTotal(response.total || response.businesses.length);
        setPage(response.page || 1);
        setTotalPages(response.totalPages || 1);
      } else if (response && Array.isArray(response)) {
        // Handle case where response is directly an array
        setBusinesses(response);
        setTotal(response.length);
        setPage(1);
        setTotalPages(1);
      } else {
        setBusinesses([]);
        setTotal(0);
        setPage(1);
        setTotalPages(0);
        console.warn('Unexpected businesses response format:', response);
      }
    } catch (err) {
      setError('Failed to load businesses');
      console.error('Error fetching businesses:', err);
      setBusinesses([]);
      setTotal(0);
      setPage(1);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBusinesses();
  }, [params.page, params.limit, params.category, params.featured]);

  return {
    businesses,
    loading,
    error,
    total,
    page,
    totalPages,
    refetch: fetchBusinesses,
  };
}