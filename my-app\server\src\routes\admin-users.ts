// Admin user management routes
import { Hono } from 'hono';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as userSchema from '../schema/users';
import * as businessSchema from '../schema/business';
import type { AdminResponse, UserActivityMetrics } from '../types/admin';

const userRoutes = new Hono();

// ===== USER MANAGEMENT SYSTEM =====
// Get All Users with Activity Metrics
userRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search');
    const adminOnly = c.req.query('adminOnly') === 'true';
    const sortBy = c.req.query('sortBy') || 'created_at';
    const sortOrder = c.req.query('sortOrder') || 'desc';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Build where conditions
    const whereConditions = [];
    
    if (adminOnly) {
      whereConditions.push(eq(userSchema.users.is_admin, true));
    }
    
    if (search) {
      whereConditions.push(
        sql`(${userSchema.users.email} ILIKE ${`%${search}%`} OR 
            ${userSchema.users.display_name} ILIKE ${`%${search}%`})`
      );
    }

    let query = db
      .select({
        id: userSchema.users.id,
        email: userSchema.users.email,
        display_name: userSchema.users.display_name,
        photo_url: userSchema.users.photo_url,
        is_admin: userSchema.users.is_admin,
        created_at: userSchema.users.created_at,
        updated_at: userSchema.users.updated_at,
      })
      .from(userSchema.users);

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Apply sorting
    const orderColumn = sortBy === 'email' ? userSchema.users.email :
                       sortBy === 'display_name' ? userSchema.users.display_name :
                       userSchema.users.created_at;
    
    const orderDirection = sortOrder === 'asc' ? asc : desc;
    query = query.orderBy(orderDirection(orderColumn));

    const users = await query.limit(limit).offset(offset);

    // Get activity metrics for each user
    const usersWithActivity = await Promise.all(
      users.map(async (user) => {
        const activity = await getUserActivityMetrics(user.id);
        return {
          ...user,
          activity,
        };
      })
    );

    // Get total count with same filters
    let countQuery = db.select({ total: count() }).from(userSchema.users);
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    const response: AdminResponse<typeof usersWithActivity> = {
      data: usersWithActivity,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        filters: { search, adminOnly, sortBy, sortOrder },
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching users:', error);
    return c.json({ 
      error: 'Failed to fetch users',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get User by ID with Activity
userRoutes.get('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const db = await getDatabase();

    const [user] = await db
      .select()
      .from(userSchema.users)
      .where(eq(userSchema.users.id, id))
      .limit(1);

    if (!user) {
      return c.json({ 
        error: 'User not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    const activity = await getUserActivityMetrics(id);

    const response: AdminResponse<typeof user & { activity: UserActivityMetrics }> = {
      data: {
        ...user,
        activity,
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching user:', error);
    return c.json({ 
      error: 'Failed to fetch user',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Toggle Admin Privileges
userRoutes.patch('/:id/toggle-admin', async (c) => {
  try {
    const id = c.req.param('id');
    const db = await getDatabase();

    const [user] = await db
      .select({ is_admin: userSchema.users.is_admin })
      .from(userSchema.users)
      .where(eq(userSchema.users.id, id))
      .limit(1);

    if (!user) {
      return c.json({ 
        error: 'User not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    const [updatedUser] = await db
      .update(userSchema.users)
      .set({
        is_admin: !user.is_admin,
        updated_at: new Date(),
      })
      .where(eq(userSchema.users.id, id))
      .returning();

    return c.json({
      message: `User ${updatedUser.is_admin ? 'granted' : 'revoked'} admin privileges`,
      user: updatedUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error toggling admin privileges:', error);
    return c.json({ 
      error: 'Failed to toggle admin privileges',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get User Activity Logs
userRoutes.get('/:id/activity', async (c) => {
  try {
    const id = c.req.param('id');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Check if user exists
    const [user] = await db
      .select({ email: userSchema.users.email })
      .from(userSchema.users)
      .where(eq(userSchema.users.id, id))
      .limit(1);

    if (!user) {
      return c.json({ 
        error: 'User not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Get user's reviews
    const reviews = await db
      .select({
        id: businessSchema.reviews.id,
        business_id: businessSchema.reviews.business_id,
        business_name: businessSchema.businesses.name,
        rating: businessSchema.reviews.rating,
        comment: businessSchema.reviews.comment,
        is_approved: businessSchema.reviews.is_approved,
        created_at: businessSchema.reviews.created_at,
      })
      .from(businessSchema.reviews)
      .leftJoin(businessSchema.businesses, eq(businessSchema.reviews.business_id, businessSchema.businesses.id))
      .where(eq(businessSchema.reviews.author_email, user.email))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.reviews)
      .where(eq(businessSchema.reviews.author_email, user.email));

    const response: AdminResponse<typeof reviews> = {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        userEmail: user.email,
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching user activity:', error);
    return c.json({ 
      error: 'Failed to fetch user activity',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get User Statistics
userRoutes.get('/stats/overview', async (c) => {
  try {
    const db = await getDatabase();
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Total users
    const [totalUsers] = await db
      .select({ count: count() })
      .from(userSchema.users);

    // Admin users
    const [adminUsers] = await db
      .select({ count: count() })
      .from(userSchema.users)
      .where(eq(userSchema.users.is_admin, true));

    // New users in last 30 days
    const [newUsers] = await db
      .select({ count: count() })
      .from(userSchema.users)
      .where(sql`${userSchema.users.created_at} >= ${thirtyDaysAgo}`);

    // Active users (users who have submitted reviews)
    const [activeUsers] = await db
      .selectDistinct({ count: sql<number>`count(distinct ${businessSchema.reviews.author_email})` })
      .from(businessSchema.reviews);

    const stats = {
      totalUsers: totalUsers.count,
      adminUsers: adminUsers.count,
      newUsersLast30Days: newUsers.count,
      activeUsers: activeUsers.count,
      inactiveUsers: totalUsers.count - activeUsers.count,
    };

    const response: AdminResponse<typeof stats> = {
      data: stats,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching user statistics:', error);
    return c.json({ 
      error: 'Failed to fetch user statistics',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

/**
 * Calculate user activity metrics
 */
async function getUserActivityMetrics(userId: string): Promise<UserActivityMetrics> {
  const db = await getDatabase();

  // Get user details
  const [user] = await db
    .select({ email: userSchema.users.email, created_at: userSchema.users.created_at })
    .from(userSchema.users)
    .where(eq(userSchema.users.id, userId))
    .limit(1);

  if (!user) {
    return {
      reviewsSubmitted: 0,
      businessesViewed: 0, // We don't track this in current schema
      lastLoginDate: null, // We don't track this in current schema
      accountAge: 0,
      engagementScore: 0,
    };
  }

  // Count reviews submitted by this user (using email as identifier)
  const [reviewCount] = await db
    .select({ count: count() })
    .from(businessSchema.reviews)
    .where(eq(businessSchema.reviews.author_email, user.email));

  // Calculate account age in days
  const accountAge = Math.floor((Date.now() - user.created_at.getTime()) / (1000 * 60 * 60 * 24));

  // Calculate engagement score (simple formula based on reviews and account age)
  const engagementScore = accountAge > 0 ? Math.min(100, (reviewCount.count / accountAge) * 365 * 10) : 0;

  return {
    reviewsSubmitted: reviewCount.count,
    businessesViewed: 0, // Would need view tracking to implement
    lastLoginDate: null, // Would need login tracking to implement
    accountAge,
    engagementScore: Math.round(engagementScore * 100) / 100,
  };
}

export default userRoutes;
