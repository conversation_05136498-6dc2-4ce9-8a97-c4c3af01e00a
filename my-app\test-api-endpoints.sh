#!/bin/bash

# API Endpoint Testing Script
# Tests all public API endpoints implemented in the backend completion

BASE_URL="http://localhost:8787/api/v1"

echo "=== Business Directory API Endpoint Tests ==="
echo

# Test 1: Health check
echo "1. Testing health check..."
curl -s "${BASE_URL}/hello" | jq '.' || echo "Failed"
echo

# Test 2: Get all businesses
echo "2. Testing GET /businesses..."
curl -s "${BASE_URL}/businesses?limit=5" | jq '.success' || echo "Failed"
echo

# Test 3: Get all categories
echo "3. Testing GET /categories..."
curl -s "${BASE_URL}/categories" | jq '.success' || echo "Failed"
echo

# Test 4: Search businesses
echo "4. Testing GET /search..."
curl -s "${BASE_URL}/search?q=restaurant&limit=3" | jq '.success' || echo "Failed"
echo

# Test 5: Search suggestions
echo "5. Testing GET /search/suggestions..."
curl -s "${BASE_URL}/search/suggestions?q=re&limit=3" | jq '.success' || echo "Failed"
echo

# Test 6: Location-based search (with dummy coordinates)
echo "6. Testing GET /search/location..."
curl -s "${BASE_URL}/search/location?latitude=40.7128&longitude=-74.0060&radius=50&limit=3" | jq '.success' || echo "Failed"
echo

# Test 7: Get location suggestions
echo "7. Testing GET /search/locations..."
curl -s "${BASE_URL}/search/locations?q=New&limit=3" | jq '.success' || echo "Failed"
echo

# Test 8: Business density (with dummy bounds)
echo "8. Testing GET /search/density..."
curl -s "${BASE_URL}/search/density?neLat=40.8&neLon=-73.9&swLat=40.6&swLon=-74.1&gridSize=5" | jq '.success' || echo "Failed"
echo

# Test 9: Submit business application
echo "9. Testing POST /applications..."
curl -s -X POST "${BASE_URL}/applications" \
  -H "Content-Type: application/json" \
  -d '{
    "business_name": "Test Restaurant",
    "category_id": 1,
    "description": "A test restaurant for API testing",
    "address": "123 Test Street, Test City",
    "email": "<EMAIL>",
    "contact_person": "John Doe",
    "phone": "555-0123"
  }' | jq '.success' || echo "Failed"
echo

# Test 10: Submit review
echo "10. Testing POST /reviews..."
curl -s -X POST "${BASE_URL}/reviews" \
  -H "Content-Type: application/json" \
  -d '{
    "business_id": 1,
    "rating": 5,
    "comment": "Great test business!",
    "author_name": "Jane Doe",
    "author_email": "<EMAIL>"
  }' | jq '.success' || echo "Failed"
echo

echo "=== API Endpoint Tests Complete ==="
echo
echo "Note: Some tests may fail if database is empty or server is not running."
echo "Expected behavior: All endpoints should return JSON with 'success' field."