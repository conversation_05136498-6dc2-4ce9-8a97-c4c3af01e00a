// Public category routes
import { <PERSON>o } from 'hono';
import { eq, and, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';

const publicCategoryRoutes = new Hono();

// Get all categories (public)
publicCategoryRoutes.get('/', async (c) => {
  try {
    const db = await getDatabase();
    const categories = await db
      .select({
        id: businessSchema.categories.id,
        name: businessSchema.categories.name,
        slug: businessSchema.categories.slug,
        icon: businessSchema.categories.icon,
        description: businessSchema.categories.description,
        business_count: sql<number>`count(${businessSchema.businesses.id})`,
      })
      .from(businessSchema.categories)
      .leftJoin(businessSchema.businesses, and(
        eq(businessSchema.categories.id, businessSchema.businesses.category_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .where(eq(businessSchema.categories.is_active, true))
      .groupBy(
        businessSchema.categories.id,
        businessSchema.categories.name,
        businessSchema.categories.slug,
        businessSchema.categories.icon,
        businessSchema.categories.description
      )
      .orderBy(businessSchema.categories.name);

    return c.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch categories' 
    }, 500);
  }
});

// Get businesses in specific category
publicCategoryRoutes.get('/:slug', async (c) => {
  try {
    const slug = c.req.param('slug');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // First, get the category
    const [category] = await db
      .select()
      .from(businessSchema.categories)
      .where(and(
        eq(businessSchema.categories.slug, slug),
        eq(businessSchema.categories.is_active, true)
      ))
      .limit(1);

    if (!category) {
      return c.json({ 
        success: false,
        error: 'Category not found' 
      }, 404);
    }

    // Get businesses in this category
    const businesses = await db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
      })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.category_id, category.id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .orderBy(businessSchema.businesses.is_featured, businessSchema.businesses.created_at)
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.category_id, category.id),
        eq(businessSchema.businesses.is_active, true)
      ));

    return c.json({
      success: true,
      data: {
        category,
        businesses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching category businesses:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch category businesses' 
    }, 500);
  }
});

export default publicCategoryRoutes;