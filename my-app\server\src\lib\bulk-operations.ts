// Bulk operations utilities for admin dashboard

import { eq, inArray, and, count, sql } from 'drizzle-orm';
import { getDatabase } from './db';
import * as businessSchema from '../schema/business';
import * as userSchema from '../schema/users';
import type { BulkOperationRequest, BulkOperationResult } from '../types/admin';

/**
 * Perform bulk operations on businesses
 */
export async function bulkBusinessOperations(
  request: BulkOperationRequest,
  adminId: string
): Promise<BulkOperationResult> {
  const db = await getDatabase();
  const result: BulkOperationResult = {
    success: 0,
    failed: 0,
    errors: [],
  };

  try {
    switch (request.operation) {
      case 'activate':
        await db
          .update(businessSchema.businesses)
          .set({ 
            is_active: true,
            updated_at: new Date(),
          })
          .where(inArray(businessSchema.businesses.id, request.ids));
        result.success = request.ids.length;
        break;

      case 'deactivate':
        await db
          .update(businessSchema.businesses)
          .set({ 
            is_active: false,
            updated_at: new Date(),
          })
          .where(inArray(businessSchema.businesses.id, request.ids));
        result.success = request.ids.length;
        break;

      case 'feature':
        await db
          .update(businessSchema.businesses)
          .set({ 
            is_featured: true,
            updated_at: new Date(),
          })
          .where(inArray(businessSchema.businesses.id, request.ids));
        result.success = request.ids.length;
        break;

      case 'delete':
        // Soft delete by deactivating
        await db
          .update(businessSchema.businesses)
          .set({ 
            is_active: false,
            updated_at: new Date(),
          })
          .where(inArray(businessSchema.businesses.id, request.ids));
        result.success = request.ids.length;
        break;

      default:
        throw new Error(`Unsupported operation: ${request.operation}`);
    }

    // Log the bulk operation
    await logBulkOperation(adminId, 'business', request.operation, request.ids, request.notes);

  } catch (error) {
    console.error('Bulk business operation failed:', error);
    result.failed = request.ids.length;
    result.errors = request.ids.map(id => ({
      id,
      error: error instanceof Error ? error.message : 'Unknown error',
    }));
  }

  return result;
}

/**
 * Perform bulk operations on reviews
 */
export async function bulkReviewOperations(
  request: BulkOperationRequest,
  adminId: string
): Promise<BulkOperationResult> {
  const db = await getDatabase();
  const result: BulkOperationResult = {
    success: 0,
    failed: 0,
    errors: [],
  };

  try {
    switch (request.operation) {
      case 'approve':
        await db
          .update(businessSchema.reviews)
          .set({ 
            is_approved: true,
            updated_at: new Date(),
          })
          .where(inArray(businessSchema.reviews.id, request.ids));
        
        // Update business average ratings for approved reviews
        await updateBusinessRatingsForReviews(request.ids);
        result.success = request.ids.length;
        break;

      case 'reject':
        await db
          .update(businessSchema.reviews)
          .set({ 
            is_approved: false,
            updated_at: new Date(),
          })
          .where(inArray(businessSchema.reviews.id, request.ids));
        result.success = request.ids.length;
        break;

      case 'delete':
        // Get review details before deletion for rating recalculation
        const reviewsToDelete = await db
          .select({
            id: businessSchema.reviews.id,
            business_id: businessSchema.reviews.business_id,
            is_approved: businessSchema.reviews.is_approved,
          })
          .from(businessSchema.reviews)
          .where(inArray(businessSchema.reviews.id, request.ids));

        // Delete reviews
        await db
          .delete(businessSchema.reviews)
          .where(inArray(businessSchema.reviews.id, request.ids));

        // Update business ratings for affected businesses
        const affectedBusinessIds = [...new Set(reviewsToDelete.map(r => r.business_id))];
        await updateBusinessRatings(affectedBusinessIds);
        
        result.success = request.ids.length;
        break;

      default:
        throw new Error(`Unsupported operation: ${request.operation}`);
    }

    // Log the bulk operation
    await logBulkOperation(adminId, 'review', request.operation, request.ids, request.notes);

  } catch (error) {
    console.error('Bulk review operation failed:', error);
    result.failed = request.ids.length;
    result.errors = request.ids.map(id => ({
      id,
      error: error instanceof Error ? error.message : 'Unknown error',
    }));
  }

  return result;
}

/**
 * Perform bulk operations on applications
 */
export async function bulkApplicationOperations(
  request: BulkOperationRequest,
  adminId: string
): Promise<BulkOperationResult> {
  const db = await getDatabase();
  const result: BulkOperationResult = {
    success: 0,
    failed: 0,
    errors: [],
  };

  for (const id of request.ids) {
    try {
      const [application] = await db
        .select()
        .from(businessSchema.businessApplications)
        .where(eq(businessSchema.businessApplications.id, id))
        .limit(1);

      if (!application) {
        result.failed++;
        result.errors.push({ id, error: 'Application not found' });
        continue;
      }

      if (application.status !== 'pending') {
        result.failed++;
        result.errors.push({ id, error: 'Application already processed' });
        continue;
      }

      switch (request.operation) {
        case 'approve':
          // Create business from application
          const [newBusiness] = await db
            .insert(businessSchema.businesses)
            .values({
              name: application.business_name,
              slug: application.business_name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
              category_id: application.category_id,
              description: application.description,
              short_description: application.description.substring(0, 500),
              address: application.address,
              latitude: application.latitude,
              longitude: application.longitude,
              phone: application.phone,
              email: application.email,
              website: application.website,
              is_featured: false,
              is_active: true,
            })
            .returning();

          // Update application status
          await db
            .update(businessSchema.businessApplications)
            .set({
              status: 'approved',
              admin_notes: request.notes || null,
              reviewed_at: new Date(),
              approved_business_id: newBusiness.id,
            })
            .where(eq(businessSchema.businessApplications.id, id));

          result.success++;
          break;

        case 'reject':
          await db
            .update(businessSchema.businessApplications)
            .set({
              status: 'rejected',
              admin_notes: request.notes || null,
              reviewed_at: new Date(),
            })
            .where(eq(businessSchema.businessApplications.id, id));

          result.success++;
          break;

        default:
          result.failed++;
          result.errors.push({ id, error: `Unsupported operation: ${request.operation}` });
      }
    } catch (error) {
      console.error(`Failed to process application ${id}:`, error);
      result.failed++;
      result.errors.push({
        id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Log the bulk operation
  await logBulkOperation(adminId, 'application', request.operation, request.ids, request.notes);

  return result;
}

/**
 * Update business ratings after review changes
 */
async function updateBusinessRatingsForReviews(reviewIds: number[]): Promise<void> {
  const db = await getDatabase();

  // Get unique business IDs from the reviews
  const businessIds = await db
    .selectDistinct({ business_id: businessSchema.reviews.business_id })
    .from(businessSchema.reviews)
    .where(inArray(businessSchema.reviews.id, reviewIds));

  await updateBusinessRatings(businessIds.map(b => b.business_id));
}

/**
 * Update business ratings and review counts
 */
async function updateBusinessRatings(businessIds: number[]): Promise<void> {
  const db = await getDatabase();

  for (const businessId of businessIds) {
    // Calculate new average rating and total reviews for approved reviews only
    const [stats] = await db
      .select({
        averageRating: sql<number>`avg(${businessSchema.reviews.rating})`,
        totalReviews: count(businessSchema.reviews.id),
      })
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, businessId),
        eq(businessSchema.reviews.is_approved, true)
      ));

    await db
      .update(businessSchema.businesses)
      .set({
        average_rating: stats.averageRating ? stats.averageRating.toFixed(2) : '0.00',
        total_reviews: stats.totalReviews,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.businesses.id, businessId));
  }
}

/**
 * Log bulk operations for audit trail
 */
async function logBulkOperation(
  adminId: string,
  resourceType: string,
  operation: string,
  resourceIds: number[],
  notes?: string
): Promise<void> {
  // This would typically log to an audit table
  // For now, we'll just console log
  console.log('Bulk operation logged:', {
    adminId,
    resourceType,
    operation,
    resourceIds,
    notes,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Export businesses to CSV format
 */
export async function exportBusinessesToCSV(filters?: any): Promise<string> {
  const db = await getDatabase();

  let query = db
    .select({
      id: businessSchema.businesses.id,
      name: businessSchema.businesses.name,
      category: businessSchema.categories.name,
      address: businessSchema.businesses.address,
      phone: businessSchema.businesses.phone,
      email: businessSchema.businesses.email,
      website: businessSchema.businesses.website,
      is_featured: businessSchema.businesses.is_featured,
      is_active: businessSchema.businesses.is_active,
      average_rating: businessSchema.businesses.average_rating,
      total_reviews: businessSchema.businesses.total_reviews,
      created_at: businessSchema.businesses.created_at,
    })
    .from(businessSchema.businesses)
    .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id));

  // Apply filters if provided
  if (filters?.status === 'active') {
    query = query.where(eq(businessSchema.businesses.is_active, true));
  } else if (filters?.status === 'inactive') {
    query = query.where(eq(businessSchema.businesses.is_active, false));
  }

  const businesses = await query;

  // Convert to CSV
  const headers = [
    'ID', 'Name', 'Category', 'Address', 'Phone', 'Email', 'Website',
    'Featured', 'Active', 'Average Rating', 'Total Reviews', 'Created At'
  ];

  const csvRows = [
    headers.join(','),
    ...businesses.map(business => [
      business.id,
      `"${business.name}"`,
      `"${business.category || ''}"`,
      `"${business.address}"`,
      business.phone || '',
      business.email || '',
      business.website || '',
      business.is_featured,
      business.is_active,
      business.average_rating,
      business.total_reviews,
      business.created_at.toISOString(),
    ].join(','))
  ];

  return csvRows.join('\n');
}

/**
 * Import businesses from CSV data
 */
export async function importBusinessesFromCSV(
  csvData: string,
  adminId: string
): Promise<BulkOperationResult> {
  const db = await getDatabase();
  const result: BulkOperationResult = {
    success: 0,
    failed: 0,
    errors: [],
  };

  try {
    const lines = csvData.split('\n');
    const headers = lines[0].split(',');
    
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue;

      try {
        const values = lines[i].split(',');
        const businessData: any = {};

        headers.forEach((header, index) => {
          businessData[header.trim().toLowerCase().replace(' ', '_')] = values[index]?.replace(/"/g, '').trim();
        });

        // Validate required fields
        if (!businessData.name || !businessData.address) {
          result.failed++;
          result.errors.push({ id: i, error: 'Missing required fields: name or address' });
          continue;
        }

        // Insert business
        await db.insert(businessSchema.businesses).values({
          name: businessData.name,
          slug: businessData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
          category_id: parseInt(businessData.category_id) || 1, // Default category
          description: businessData.description || '',
          short_description: businessData.short_description || businessData.description?.substring(0, 500) || '',
          address: businessData.address,
          phone: businessData.phone || null,
          email: businessData.email || null,
          website: businessData.website || null,
          is_featured: businessData.is_featured === 'true',
          is_active: businessData.is_active !== 'false',
        });

        result.success++;
      } catch (error) {
        result.failed++;
        result.errors.push({
          id: i,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Log the import operation
    await logBulkOperation(adminId, 'business', 'import', [], `Imported ${result.success} businesses`);

  } catch (error) {
    console.error('CSV import failed:', error);
    throw new Error('Failed to process CSV data');
  }

  return result;
}
