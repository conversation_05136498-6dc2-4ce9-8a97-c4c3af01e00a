import 'dotenv/config';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { authMiddleware, adminMiddleware } from './middleware/auth';
import { getDatabase, testDatabaseConnection } from './lib/db';
import { setEnvContext, clearEnvContext, getDatabaseUrl } from './lib/env';
import * as schema from './schema/users';
import * as businessSchema from './schema/business';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import adminRoutes from './routes/admin';
import businessRoutes from './routes/admin-businesses';
import categoryRoutes from './routes/admin-categories';
import reviewRoutes from './routes/admin-reviews';
import userRoutes from './routes/admin-users';
import applicationRoutes from './routes/admin-applications';
import adminPhotoRoutes from './routes/admin-photo-collection';

// Public routes
import publicBusinessRoutes from './routes/public-businesses';
import publicCategoryRoutes from './routes/public-categories';
import publicSearchRoutes from './routes/public-search';
import publicApplicationRoutes from './routes/public-applications';
import publicReviewRoutes from './routes/public-reviews';

type Env = {
  RUNTIME?: string;
  [key: string]: any;
};

const app = new Hono<{ Bindings: Env }>();

// In Node.js environment, set environment context from process.env
if (typeof process !== 'undefined' && process.env) {
  setEnvContext(process.env);
}

// Environment context middleware - detect runtime using RUNTIME env var
app.use('*', async (c, next) => {
  if (c.env?.RUNTIME === 'cloudflare') {
    setEnvContext(c.env);
  }
  
  await next();
  // No need to clear context - env vars are the same for all requests
  // In fact, clearing the context would cause the env vars to potentially be unset for parallel requests
});

// Middleware
app.use('*', logger());
app.use('*', cors());

// Health check route - public
app.get('/', (c) => c.json({ status: 'ok', message: 'API is running' }));

// API routes
const api = new Hono();

// Public routes go here (if any)
api.get('/hello', (c) => {
  return c.json({
    message: 'Hello from Hono!',
  });
});

// ===== PUBLIC API ROUTES =====
// Mount organized public routes
api.route('/businesses', publicBusinessRoutes);
api.route('/categories', publicCategoryRoutes);
api.route('/search', publicSearchRoutes);
api.route('/applications', publicApplicationRoutes);
api.route('/reviews', publicReviewRoutes);

// Database test route
api.get('/test-db', async (c) => {
  try {
    const isHealthy = await testDatabaseConnection();
    const db = await getDatabase();

    const result = await db.select().from(schema.users).limit(5);

    return c.json({
      message: 'Database connection successful!',
      users: result,
      connectionHealthy: isHealthy,
      usingLocalDatabase: !getDatabaseUrl(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Database test error:', error);
    return c.json({
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Protected routes - require authentication
const protectedRoutes = new Hono();

protectedRoutes.use('*', authMiddleware);

protectedRoutes.get('/me', (c) => {
  const user = c.get('user');
  return c.json({
    user,
    message: 'You are authenticated!',
  });
});

protectedRoutes.get('/admin-status', (c) => {
  const user = c.get('user');
  return c.json({
    isAdmin: user.is_admin || false,
    permissions: user.is_admin ? ['admin.access', 'admin.businesses', 'admin.applications', 'admin.reviews'] : [],
    message: user.is_admin ? 'Admin access granted' : 'Regular user access',
  });
});

// ===== ADMIN ROUTES =====
// Create main admin routes with admin middleware
const mainAdminRoutes = new Hono();
mainAdminRoutes.use('*', adminMiddleware);

// Mount modular admin routes
mainAdminRoutes.route('/', adminRoutes);
mainAdminRoutes.route('/businesses', businessRoutes);
mainAdminRoutes.route('/categories', categoryRoutes);
mainAdminRoutes.route('/reviews', reviewRoutes);
mainAdminRoutes.route('/users', userRoutes);
mainAdminRoutes.route('/applications', applicationRoutes);
mainAdminRoutes.route('/photos', adminPhotoRoutes);

// Existing admin applications route (keeping for backward compatibility)
mainAdminRoutes.get('/applications', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status') || 'pending';
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    const applications = await db
      .select({
        id: businessSchema.businessApplications.id,
        business_name: businessSchema.businessApplications.business_name,
        description: businessSchema.businessApplications.description,
        address: businessSchema.businessApplications.address,
        phone: businessSchema.businessApplications.phone,
        email: businessSchema.businessApplications.email,
        website: businessSchema.businessApplications.website,
        contact_person: businessSchema.businessApplications.contact_person,
        status: businessSchema.businessApplications.status,
        admin_notes: businessSchema.businessApplications.admin_notes,
        submitted_at: businessSchema.businessApplications.submitted_at,
        reviewed_at: businessSchema.businessApplications.reviewed_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businessApplications)
      .leftJoin(businessSchema.categories, eq(businessSchema.businessApplications.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businessApplications.status, status))
      .orderBy(desc(businessSchema.businessApplications.submitted_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.status, status));

    return c.json({
      applications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching applications:', error);
    return c.json({ error: 'Failed to fetch applications' }, 500);
  }
});

// Admin Application Approval/Rejection
mainAdminRoutes.put('/applications/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    const db = await getDatabase();

    if (!body.action || !['approve', 'reject'].includes(body.action)) {
      return c.json({ error: 'Action must be "approve" or "reject"' }, 400);
    }

    // Get the application
    const [application] = await db
      .select()
      .from(businessSchema.businessApplications)
      .where(eq(businessSchema.businessApplications.id, id))
      .limit(1);

    if (!application) {
      return c.json({ error: 'Application not found' }, 404);
    }

    if (application.status !== 'pending') {
      return c.json({ error: 'Application has already been processed' }, 400);
    }

    if (body.action === 'approve') {
      // Create business from application
      const [newBusiness] = await db
        .insert(businessSchema.businesses)
        .values({
          name: application.business_name,
          slug: application.business_name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
          category_id: application.category_id,
          description: application.description,
          short_description: application.description.substring(0, 500),
          address: application.address,
          latitude: application.latitude,
          longitude: application.longitude,
          phone: application.phone,
          email: application.email,
          website: application.website,
          is_featured: false,
          is_active: true,
        })
        .returning();

      // Update application status
      await db
        .update(businessSchema.businessApplications)
        .set({
          status: 'approved',
          admin_notes: body.notes || null,
          reviewed_at: new Date(),
          approved_business_id: newBusiness.id,
        })
        .where(eq(businessSchema.businessApplications.id, id));

      return c.json({
        message: 'Application approved and business created',
        business: newBusiness
      });
    } else {
      // Reject application
      await db
        .update(businessSchema.businessApplications)
        .set({
          status: 'rejected',
          admin_notes: body.notes || null,
          reviewed_at: new Date(),
        })
        .where(eq(businessSchema.businessApplications.id, id));

      return c.json({
        message: 'Application rejected'
      });
    }
  } catch (error) {
    console.error('Error processing application:', error);
    return c.json({ error: 'Failed to process application' }, 500);
  }
});

// Mount the protected routes under /protected
api.route('/protected', protectedRoutes);

// Mount the admin routes under /admin
api.route('/admin', mainAdminRoutes);

// Mount the API router
app.route('/api/v1', api);

export default app;
