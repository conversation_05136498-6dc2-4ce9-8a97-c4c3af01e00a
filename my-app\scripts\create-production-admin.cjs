const { writeFileSync, readFileSync, existsSync } = require('fs');
const { join } = require('path');

/**
 * Production Admin Creation Script
 * 
 * This script creates admin users for both Firebase emulator (development)
 * and provides instructions for production admin creation.
 */

function createDevelopmentAdmin(email, password) {
  const accountsPath = join(__dirname, '..', 'data', 'firebase-emulator', 'auth_export', 'accounts.json');
  
  try {
    let accountsData;
    
    // Read existing accounts or create new structure
    if (existsSync(accountsPath)) {
      accountsData = JSON.parse(readFileSync(accountsPath, 'utf8'));
    } else {
      accountsData = {
        "kind": "identitytoolkit#DownloadAccountResponse",
        "users": []
      };
    }
    
    // Check if user already exists
    const existingUser = accountsData.users.find(u => u.email === email);
    
    if (existingUser) {
      // Update existing user with admin privileges
      existingUser.customClaims = {
        "admin": true,
        "roles": ["admin"],
        "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]
      };
      console.log(`✅ Updated existing user ${email} with admin privileges`);
    } else {
      // Create new admin user
      const newUser = {
        "localId": `admin_${Date.now()}`,
        "lastLoginAt": Date.now().toString(),
        "emailVerified": true,
        "email": email,
        "salt": `fakeSalt_${Date.now()}`,
        "passwordHash": `fakeHash:salt=fakeSalt_${Date.now()}:password=${password}`,
        "passwordUpdatedAt": Date.now(),
        "validSince": Math.floor(Date.now() / 1000).toString(),
        "createdAt": Date.now().toString(),
        "providerUserInfo": [
          {
            "providerId": "password",
            "email": email,
            "federatedId": email,
            "rawId": email
          }
        ],
        "lastRefreshAt": new Date().toISOString(),
        "customClaims": {
          "admin": true,
          "roles": ["admin"],
          "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]
        }
      };
      
      accountsData.users.push(newUser);
      console.log(`✅ Created new admin user: ${email}`);
    }
    
    // Write back to file
    writeFileSync(accountsPath, JSON.stringify(accountsData));
    
    return true;
  } catch (error) {
    console.error('❌ Error creating development admin:', error.message);
    return false;
  }
}

function displayProductionInstructions(email) {
  console.log('\n📋 PRODUCTION ADMIN CREATION INSTRUCTIONS:');
  console.log('==========================================');
  console.log('');
  console.log('1. **Firebase Console Setup:**');
  console.log('   - Go to Firebase Console → Authentication → Users');
  console.log(`   - Create user with email: ${email}`);
  console.log('   - Set custom claims via Firebase Admin SDK or console');
  console.log('');
  console.log('2. **Custom Claims (Firebase Admin SDK):**');
  console.log('   ```javascript');
  console.log('   const admin = require("firebase-admin");');
  console.log(`   const userRecord = await admin.auth().getUserByEmail("${email}");`);
  console.log('   await admin.auth().setCustomUserClaims(userRecord.uid, {');
  console.log('     admin: true,');
  console.log('     roles: ["admin"],');
  console.log('     permissions: ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]');
  console.log('   });');
  console.log('   ```');
  console.log('');
  console.log('3. **Database Update:**');
  console.log('   - The backend will automatically create/update the user record');
  console.log('   - The `is_admin` field will be set based on Firebase custom claims');
  console.log('');
  console.log('4. **Verification:**');
  console.log(`   - User ${email} should be able to sign in`);
  console.log('   - Admin panel should be accessible at /admin');
  console.log('   - Backend API calls should work with admin privileges');
}

function main() {
  const email = process.argv[2] || '<EMAIL>';
  const password = process.argv[3] || 'admin123';
  const environment = process.argv[4] || 'development';
  
  console.log('🔐 Admin User Creation Script');
  console.log('============================');
  console.log(`Email: ${email}`);
  console.log(`Environment: ${environment}`);
  console.log('');
  
  if (environment === 'development') {
    const success = createDevelopmentAdmin(email, password);
    
    if (success) {
      console.log('🎉 Development admin user created successfully!');
      console.log('');
      console.log('📝 Next Steps:');
      console.log('1. Restart the development server (pnpm run dev)');
      console.log('2. Go to http://localhost:5501/admin');
      console.log(`3. Sign in with: ${email} / ${password}`);
      console.log('4. You should have full admin access!');
    }
  } else {
    displayProductionInstructions(email);
  }
}

// Show usage if no arguments
if (process.argv.length < 3) {
  console.log('Usage:');
  console.log('  Development: node scripts/create-production-admin.cjs <email> <password> development');
  console.log('  Production:  node scripts/create-production-admin.cjs <email> <password> production');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/create-production-admin.cjs <EMAIL> securepass123 development');
  console.log('  node scripts/create-production-admin.cjs <EMAIL> securepass123 production');
  console.log('');
}

main();
