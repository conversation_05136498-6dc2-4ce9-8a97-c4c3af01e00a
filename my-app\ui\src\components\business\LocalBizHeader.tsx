import { <PERSON>, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Link } from 'react-router-dom';

export function LocalBizHeader() {
  return (
    <header className="bg-background border-b border-border">
      <div className="px-10 py-4">
        <div className="flex items-center justify-between">
          {/* Logo Section */}
          <div className="flex items-center">
            <Link to="/" className="text-xl font-bold text-foreground">
              LocalBiz
            </Link>
          </div>
          
          {/* Navigation Section */}
          <div className="hidden md:flex items-center gap-8">
            <Link to="/" className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors">
              Home
            </Link>
            <Link to="/categories" className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors">
              Categories
            </Link>
            <Link to="/businesses" className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors">
              Businesses
            </Link>
            <Link to="/apply" className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors">
              List Your Business
            </Link>
          </div>
          
          {/* Actions Section */}
          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search businesses..."
                className="pl-10 w-64 bg-muted border-border"
              />
            </div>
            
            {/* Notification Bell */}
            <Button variant="ghost" size="icon">
              <Bell className="w-4 h-4" />
            </Button>
            
            {/* User Avatar Placeholder */}
            <div className="w-8 h-8 rounded-full bg-muted border border-border"></div>
          </div>
        </div>
      </div>
    </header>
  );
}
