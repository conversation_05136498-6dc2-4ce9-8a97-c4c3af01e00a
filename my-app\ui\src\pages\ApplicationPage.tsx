import { useState } from 'react';
import { BusinessApplicationForm } from '@/components/application/BusinessApplicationForm';
import { ApplicationSuccess } from '@/components/application/ApplicationSuccess';

export function ApplicationPage() {
  const [applicationSubmitted, setApplicationSubmitted] = useState(false);
  const [applicationId, setApplicationId] = useState<string | null>(null);

  const handleApplicationSuccess = (id: string) => {
    setApplicationId(id);
    setApplicationSubmitted(true);
  };

  if (applicationSubmitted) {
    return <ApplicationSuccess applicationId={applicationId} />;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-primary text-primary-foreground py-12">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            List Your Business
          </h1>
          <p className="text-lg opacity-90 max-w-2xl mx-auto">
            Join our directory and connect with customers in your area. 
            It's free and easy to get started.
          </p>
        </div>
      </div>

      {/* Application Form */}
      <div className="container mx-auto px-6 py-12">
        <div className="max-w-2xl mx-auto">
          <BusinessApplicationForm onSuccess={handleApplicationSuccess} />
        </div>
      </div>
    </div>
  );
}
