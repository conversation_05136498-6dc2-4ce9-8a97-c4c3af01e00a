#!/usr/bin/env node

/**
 * Script to make a user an admin by setting Firebase custom claims
 * Usage: node scripts/make-admin.js <email>
 * Example: node scripts/make-admin.js <EMAIL>
 */

const { readFileSync, writeFileSync, existsSync } = require('fs');
const { join, dirname } = require('path');

const scriptDir = dirname(require.main.filename);
const projectRoot = join(scriptDir, '..');

/**
 * Find the latest Firebase emulator export directory
 */
function findLatestFirebaseExport() {
  const { readdirSync, statSync } = require('fs');

  try {
    const files = readdirSync(projectRoot);
    const exportDirs = files
      .filter(file => file.startsWith('firebase-export-'))
      .map(dir => ({
        name: dir,
        path: join(projectRoot, dir),
        mtime: statSync(join(projectRoot, dir)).mtime
      }))
      .sort((a, b) => b.mtime - a.mtime);

    if (exportDirs.length === 0) {
      throw new Error('No Firebase export directories found');
    }

    return exportDirs[0].path;
  } catch (error) {
    console.error('Error finding Firebase export directory:', error.message);
    process.exit(1);
  }
}

/**
 * Update Firebase emulator accounts to add admin custom claims
 */
function makeUserAdmin(email) {
  try {
    const exportDir = findLatestFirebaseExport();
    const accountsPath = join(exportDir, 'auth_export', 'accounts.json');
    
    if (!existsSync(accountsPath)) {
      throw new Error(`Firebase accounts file not found at: ${accountsPath}`);
    }
    
    // Read current accounts
    const accountsData = JSON.parse(readFileSync(accountsPath, 'utf8'));
    
    // Find user by email
    const user = accountsData.users.find(u => u.email === email);
    
    if (!user) {
      throw new Error(`User with email ${email} not found in Firebase emulator`);
    }
    
    console.log(`Found user: ${user.email} (ID: ${user.localId})`);
    
    // Add custom claims for admin
    if (!user.customClaims) {
      user.customClaims = {};
    }
    
    user.customClaims.admin = true;
    user.customClaims.roles = ['admin'];
    user.customClaims.permissions = ['admin.access', 'admin.businesses', 'admin.applications', 'admin.reviews'];
    
    // Write back to file
    writeFileSync(accountsPath, JSON.stringify(accountsData, null, 2));
    
    console.log(`✅ Successfully made ${email} an admin!`);
    console.log('Custom claims added:');
    console.log('  - admin: true');
    console.log('  - roles: ["admin"]');
    console.log('  - permissions: ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]');
    console.log('');
    console.log('⚠️  Note: You may need to restart the Firebase emulator for changes to take effect.');
    console.log('   The user will also need to sign out and sign back in to get the new token.');
    
  } catch (error) {
    console.error('❌ Error making user admin:', error.message);
    process.exit(1);
  }
}

/**
 * Main function
 */
function main() {
  const email = process.argv[2];
  
  if (!email) {
    console.error('Usage: node scripts/make-admin.js <email>');
    console.error('Example: node scripts/make-admin.js <EMAIL>');
    process.exit(1);
  }
  
  console.log(`Making ${email} an admin...`);
  makeUserAdmin(email);
}

// Run if called directly
if (require.main === module) {
  main();
}
