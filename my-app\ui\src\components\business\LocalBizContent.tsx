import { useState } from 'react';
import { Star, MapPin, Phone, Mail, Globe, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';

interface BusinessData {
  id: number;
  name: string;
  description?: string;
  address: string;
  phone?: string;
  email?: string;
  website?: string;
  latitude?: string;
  longitude?: string;
  images?: Array<{
    id: number;
    image_url: string;
    alt_text?: string;
    is_primary: boolean;
  }>;
  reviews?: Array<{
    id: number;
    rating: number;
    comment: string;
    author_name: string;
    created_at: string;
    is_verified: boolean;
  }>;
  hours?: Array<{
    day_of_week: string;
    open_time: string;
    close_time: string;
    is_closed: boolean;
  }>;
}

interface LocalBizContentProps {
  business: BusinessData;
}

const RatingStars = ({ rating }: { rating: number }) => {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? 'fill-current text-yellow-400' : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );
};

const formatBusinessHours = (hours?: BusinessData['hours']) => {
  if (!hours || hours.length === 0) return [];
  
  const daysOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const dayNames = {
    monday: 'Monday',
    tuesday: 'Tuesday', 
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday'
  };
  
  return daysOrder.map(day => {
    const dayHours = hours.find(h => h.day_of_week.toLowerCase() === day);
    return {
      day: dayNames[day as keyof typeof dayNames],
      hours: dayHours?.is_closed ? 'Closed' : `${dayHours?.open_time || 'N/A'} - ${dayHours?.close_time || 'N/A'}`
    };
  });
};

export function LocalBizContent({ business }: LocalBizContentProps) {
  const [activeTab, setActiveTab] = useState('about');
  const businessHours = formatBusinessHours(business.hours);
  
  // Filter approved images only (assuming they're already filtered from the API)
  const approvedImages = business.images || [];

  return (
    <div className="px-10 pb-10">
      <div className="max-w-6xl mx-auto">
        {/* Tab Navigation */}
        <div className="flex gap-8 mb-8 border-b border-border">
          {[
            { id: 'about', label: 'About' },
            { id: 'contact', label: 'Contact' },
            { id: 'location', label: 'Location' },
            { id: 'photos', label: 'Photos' },
            { id: 'reviews', label: 'Reviews' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`pb-4 px-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="space-y-8">
          {/* About Section */}
          {activeTab === 'about' && (
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">About {business.name}</h2>
              {business.description ? (
                <p className="text-muted-foreground leading-relaxed">
                  {business.description}
                </p>
              ) : (
                <p className="text-muted-foreground italic">
                  No description available for this business.
                </p>
              )}
            </section>
          )}

          {/* Contact Section */}
          {activeTab === 'contact' && (
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">Contact Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {business.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <p className="font-medium">{business.phone}</p>
                    </div>
                  </div>
                )}
                {business.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p className="font-medium">{business.email}</p>
                    </div>
                  </div>
                )}
                {business.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Website</p>
                      <p className="font-medium">{business.website}</p>
                    </div>
                  </div>
                )}
                <div className="flex items-center gap-3">
                  <MapPin className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Address</p>
                    <p className="font-medium">{business.address}</p>
                  </div>
                </div>
              </div>

              {/* Business Hours */}
              {businessHours.length > 0 && (
                <div className="mt-8">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Business Hours
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {businessHours.map((day, index) => (
                      <div key={index} className="flex justify-between py-2 border-b border-border last:border-b-0">
                        <span className="font-medium">{day.day}</span>
                        <span className="text-muted-foreground">{day.hours}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </section>
          )}

          {/* Location Section */}
          {activeTab === 'location' && (
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">Location</h2>
              <p className="text-muted-foreground mb-4">{business.address}</p>
              <div className="w-full h-64 bg-muted rounded-xl overflow-hidden">
                {business.latitude && business.longitude ? (
                  <iframe
                    src={`https://www.google.com/maps/embed/v1/place?key=YOUR_GOOGLE_MAPS_API_KEY&q=${business.latitude},${business.longitude}`}
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <MapPin className="w-12 h-12 mx-auto mb-2" />
                      <p>Map not available</p>
                    </div>
                  </div>
                )}
              </div>
            </section>
          )}

          {/* Photos Section */}
          {activeTab === 'photos' && (
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">Photos</h2>
              {approvedImages.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {approvedImages.map((photo, index) => (
                    <div key={photo.id} className="aspect-square rounded-xl overflow-hidden relative group">
                      <img
                        src={photo.image_url}
                        alt={photo.alt_text || `${business.name} photo ${index + 1}`}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                      />
                      {photo.is_primary && (
                        <div className="absolute top-2 left-2">
                          <Badge variant="default" className="text-xs">
                            Main
                          </Badge>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <p>No photos available for this business.</p>
                </div>
              )}
            </section>
          )}

          {/* Reviews Section */}
          {activeTab === 'reviews' && (
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">Customer Reviews</h2>
              {business.reviews && business.reviews.length > 0 ? (
                <div className="space-y-6">
                  {business.reviews.map((review) => (
                    <Card key={review.id}>
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          <Avatar>
                            <AvatarFallback>
                              {review.author_name.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <div>
                                <h4 className="font-semibold">{review.author_name}</h4>
                                <div className="flex items-center gap-2">
                                  <RatingStars rating={review.rating} />
                                  {review.is_verified && (
                                    <Badge variant="secondary" className="text-xs">
                                      Verified
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              <span className="text-sm text-muted-foreground">
                                {formatDistanceToNow(new Date(review.created_at), { addSuffix: true })}
                              </span>
                            </div>
                            <p className="text-muted-foreground">{review.comment}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <p>No reviews yet for this business.</p>
                </div>
              )}
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
