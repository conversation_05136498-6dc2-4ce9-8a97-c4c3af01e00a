import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, MapPin, Building2, Tag } from 'lucide-react';
import { useSearch } from '@/hooks/useSearch';
import { useCategories } from '@/hooks/useCategories';
import { useDebounce } from '@/hooks/useDebounce';

interface SearchBarProps {
  initialQuery?: string;
  initialLocation?: string;
  onSearch: (query: string, location?: string) => void;
  placeholder?: string;
  showAutocomplete?: boolean;
}

interface AutocompleteItem {
  type: 'business' | 'category';
  id: number;
  name: string;
  slug: string;
  description?: string;
}

export function SearchBar({ 
  initialQuery = '', 
  initialLocation = '', 
  onSearch, 
  placeholder = "Search for businesses, services...",
  showAutocomplete = true
}: SearchBarProps) {
  const [query, setQuery] = useState(initialQuery);
  const [location, setLocation] = useState(initialLocation);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const debouncedQuery = useDebounce(query, 300);
  const { businesses, loading: searchLoading, search } = useSearch(0); // No debounce here since we're using debouncedQuery
  const { categories } = useCategories();
  
  // Create autocomplete suggestions
  const suggestions: AutocompleteItem[] = [];
  
  if (debouncedQuery && debouncedQuery.length >= 2) {
    // Add matching businesses
    businesses.slice(0, 5).forEach(business => {
      suggestions.push({
        type: 'business',
        id: business.id,
        name: business.name,
        slug: business.slug,
        description: business.short_description,
      });
    });
    
    // Add matching categories
    if (categories) {
      categories
        .filter(cat => cat.name.toLowerCase().includes(debouncedQuery.toLowerCase()))
        .slice(0, 3)
        .forEach(category => {
          suggestions.push({
            type: 'category',
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description,
          });
        });
    }
  }
  
  // Search for businesses when debounced query changes
  useEffect(() => {
    if (debouncedQuery && debouncedQuery.length >= 2) {
      search({ query: debouncedQuery, limit: 5 });
    }
  }, [debouncedQuery, search]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim(), location.trim() || undefined);
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Enter') {
        handleSubmit(e);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionSelect(suggestions[selectedIndex]);
        } else {
          handleSubmit(e);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSuggestionSelect = (suggestion: AutocompleteItem) => {
    if (suggestion.type === 'business') {
      window.location.href = `/business/${suggestion.slug}`;
    } else {
      setQuery(suggestion.name);
      onSearch(suggestion.name, location.trim() || undefined);
    }
    setShowSuggestions(false);
    setSelectedIndex(-1);
  };

  const handleInputChange = (value: string) => {
    setQuery(value);
    setSelectedIndex(-1);
    if (showAutocomplete && value.length >= 2) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleInputFocus = () => {
    if (showAutocomplete && query.length >= 2 && suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => setShowSuggestions(false), 150);
  };

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={searchRef} className="w-full relative">
      <form onSubmit={handleSubmit} className="w-full">
        <div className="flex flex-col sm:flex-row gap-3 p-2 bg-background rounded-lg border shadow-sm">
          {/* Search Input */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              ref={inputRef}
              type="text"
              placeholder={placeholder}
              value={query}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              className="pl-10 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
              autoComplete="off"
            />
          </div>

          {/* Location Input */}
          <div className="sm:w-48 relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              type="text"
              placeholder="Location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-10 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* Search Button */}
          <Button type="submit" className="sm:w-auto w-full" disabled={searchLoading}>
            <Search className="w-4 h-4 mr-2" />
            {searchLoading ? 'Searching...' : 'Search'}
          </Button>
        </div>
      </form>

      {/* Autocomplete Suggestions */}
      {showAutocomplete && showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border rounded-lg shadow-lg max-h-80 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={`${suggestion.type}-${suggestion.id}`}
              type="button"
              className={`w-full px-4 py-3 text-left hover:bg-muted transition-colors flex items-start gap-3 ${
                index === selectedIndex ? 'bg-muted' : ''
              } ${index === 0 ? 'rounded-t-lg' : ''} ${
                index === suggestions.length - 1 ? 'rounded-b-lg' : ''
              }`}
              onClick={() => handleSuggestionSelect(suggestion)}
            >
              <div className="mt-0.5">
                {suggestion.type === 'business' ? (
                  <Building2 className="w-4 h-4 text-muted-foreground" />
                ) : (
                  <Tag className="w-4 h-4 text-muted-foreground" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm line-clamp-1">
                  {suggestion.name}
                </div>
                {suggestion.description && (
                  <div className="text-xs text-muted-foreground line-clamp-1 mt-0.5">
                    {suggestion.description}
                  </div>
                )}
                <div className="text-xs text-muted-foreground mt-0.5">
                  {suggestion.type === 'business' ? 'Business' : 'Category'}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
