// Simple script to seed database with sample Belize businesses via API
const API_BASE = 'http://localhost:8787/api/v1';

const sampleCategories = [
  { name: 'Hotels & Accommodations', slug: 'hotels-accommodations', icon: 'hotel', description: 'Hotels, resorts, inns, and other lodging facilities' },
  { name: 'Restaurants & Dining', slug: 'restaurants-dining', icon: 'utensils', description: 'Restaurants, cafes, bars, and dining establishments' },
  { name: 'Shopping & Retail', slug: 'shopping-retail', icon: 'store', description: 'Supermarkets, department stores, specialty shops, and retail outlets' },
  { name: 'Tours & Activities', slug: 'tours-activities', icon: 'compass', description: 'Tour operators, activity providers, and adventure services' },
  { name: 'Financial Services', slug: 'financial-services', icon: 'briefcase', description: 'Banks, insurance companies, and financial institutions' },
  { name: 'Healthcare & Medical', slug: 'healthcare-medical', icon: 'heart', description: 'Hospitals, clinics, pharmacies, and medical services' },
  { name: 'Transportation', slug: 'transportation', icon: 'car', description: 'Airlines, taxi services, car rentals, and transportation providers' },
  { name: 'Real Estate', slug: 'real-estate', icon: 'home', description: 'Real estate agencies, property development, and housing services' },
];

const belizeBusinesses = [
  {
    name: 'Best Western Plus Belize Biltmore Plaza',
    category: 'Hotels & Accommodations',
    description: 'Premier hotel located on Northern Highway offering luxury accommodations with modern amenities, spa services, and fine dining.',
    shortDescription: 'Premier hotel with luxury accommodations, spa, and fine dining',
    address: 'Mile 3, Northern Highway, Belize City, Belize',
    phone: '+************',
    email: '<EMAIL>',
    website: 'https://belizebiltmore.com',
    latitude: '17.5162',
    longitude: '-88.1808',
    isFeatured: true
  },
  {
    name: 'Fort George Hotel & Spa',
    category: 'Hotels & Accommodations',
    description: 'Sophisticated hotel located in the heart of Belize City with a focus on sustainability and elegant accommodations.',
    shortDescription: 'Sophisticated hotel in historic Fort George area',
    address: 'Fort George, Belize City, Belize',
    phone: '+************',
    website: 'https://www.fortgeorgebelize.com',
    latitude: '17.4956',
    longitude: '-88.1870',
    isFeatured: true
  },
  {
    name: '501 Restaurant',
    category: 'Restaurants & Dining',
    description: 'Located on the ground floor of The Great House hotel, serving innovative dishes including vegan options.',
    shortDescription: 'Innovative restaurant with vegan options',
    address: 'The Great House, Fort George, Belize City, Belize',
    phone: '+************',
    latitude: '17.4961',
    longitude: '-88.1875',
    isFeatured: true
  },
  {
    name: 'Vino Tintos',
    category: 'Restaurants & Dining',
    description: 'Highly rated restaurant in Belize City known for excellent service and quality cuisine.',
    shortDescription: 'Highly rated restaurant with excellent service',
    address: 'Belize City, Belize',
    latitude: '17.4955',
    longitude: '-88.1865',
    isFeatured: true
  },
  {
    name: 'Save-U Supermarket',
    category: 'Shopping & Retail',
    description: 'Modern supermarket offering groceries, liquor, sundries, and household items.',
    shortDescription: 'Modern supermarket with groceries and household items',
    address: 'Sancas Plaza, Belize City, Belize',
    phone: '+************',
    latitude: '17.4980',
    longitude: '-88.1820',
    isFeatured: true
  },
  {
    name: 'Brodies Department Store',
    category: 'Shopping & Retail',
    description: 'Modern supermarket, mini-department store and pharmacy. James Brodie & Co. has been serving Belize since 1887.',
    shortDescription: 'Historic department store and supermarket with pharmacy',
    address: 'Goldson Highway, Belize City, Belize',
    phone: '+************',
    website: 'https://www.brodies.bz',
    latitude: '17.5100',
    longitude: '-88.1900',
    isFeatured: true
  },
  {
    name: 'Belize Bank Limited',
    category: 'Financial Services',
    description: 'The largest full-service commercial banking operation in Belize.',
    shortDescription: 'Largest commercial bank in Belize',
    address: '60 Market Square, Belize City, Belize',
    phone: '+************',
    website: 'https://www.belizebank.com',
    latitude: '17.4945',
    longitude: '-88.1875',
    isFeatured: true
  },
  {
    name: 'Tourism Village',
    category: 'Tours & Activities',
    description: 'Shopping complex with around 50 gift shops, tour kiosks, and restaurants.',
    shortDescription: 'Shopping complex with 50+ gift shops and tours',
    address: 'Fort Street Tourism Village, Belize City, Belize',
    phone: '+************',
    latitude: '17.4950',
    longitude: '-88.1885',
    isFeatured: true
  }
];

async function seedData() {
  console.log('🌱 Starting to seed database with Belize businesses...');
  
  try {
    // First seed categories (these should be created via admin API)
    console.log('📝 Creating categories...');
    for (const category of sampleCategories) {
      try {
        const response = await fetch(`${API_BASE}/admin/categories`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(category)
        });
        
        if (response.ok) {
          console.log(`✅ Created category: ${category.name}`);
        } else {
          console.log(`⚠️ Category may already exist: ${category.name}`);
        }
      } catch (error) {
        console.log(`⚠️ Error creating category ${category.name}:`, error.message);
      }
    }
    
    // Then seed businesses
    console.log('🏢 Creating businesses...');
    for (const business of belizeBusinesses) {
      try {
        const response = await fetch(`${API_BASE}/admin/businesses`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: business.name,
            category_name: business.category,
            description: business.description,
            short_description: business.shortDescription,
            address: business.address,
            phone: business.phone,
            email: business.email,
            website: business.website,
            latitude: business.latitude,
            longitude: business.longitude,
            is_featured: business.isFeatured
          })
        });
        
        if (response.ok) {
          console.log(`✅ Created business: ${business.name}`);
        } else {
          const errorText = await response.text();
          console.log(`⚠️ Error creating business ${business.name}: ${errorText}`);
        }
      } catch (error) {
        console.log(`⚠️ Error creating business ${business.name}:`, error.message);
      }
    }
    
    console.log('🎉 Database seeding completed!');
    console.log('📊 Summary:');
    console.log(`   - ${sampleCategories.length} categories`);
    console.log(`   - ${belizeBusinesses.length} businesses`);
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${API_BASE}/health`);
    return response.ok;
  } catch {
    return false;
  }
}

async function main() {
  console.log('🔍 Checking if server is running...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running. Please start the development server first:');
    console.log('   pnpm run dev');
    process.exit(1);
  }
  
  console.log('✅ Server is running');
  await seedData();
}

main().catch(console.error);