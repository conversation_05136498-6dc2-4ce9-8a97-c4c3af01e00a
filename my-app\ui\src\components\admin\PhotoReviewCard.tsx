import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, X, Star, ExternalLink } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export interface PendingPhoto {
  id: number;
  business_id: number;
  image_url: string;
  photo_source: string;
  source_photo_id: string | null;
  collected_at: string | null;
  business_name: string;
}

interface PhotoReviewCardProps {
  photo: PendingPhoto;
  onApprove: (photoId: number) => Promise<void>;
  onReject: (photoId: number) => Promise<void>;
  onSetPrimary: (photoId: number) => Promise<void>;
  isLoading?: boolean;
}

export function PhotoReviewCard({ 
  photo, 
  onApprove, 
  onReject, 
  onSetPrimary, 
  isLoading = false 
}: PhotoReviewCardProps) {
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleApprove = async () => {
    setActionLoading('approve');
    try {
      await onApprove(photo.id);
    } finally {
      setActionLoading(null);
    }
  };

  const handleReject = async () => {
    setActionLoading('reject');
    try {
      await onReject(photo.id);
    } finally {
      setActionLoading(null);
    }
  };

  const handleSetPrimary = async () => {
    setActionLoading('primary');
    try {
      await onSetPrimary(photo.id);
    } finally {
      setActionLoading(null);
    }
  };

  const getSourceBadgeColor = (source: string) => {
    switch (source) {
      case 'google_places':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'yelp':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">
            {photo.business_name}
          </CardTitle>
          <Badge className={getSourceBadgeColor(photo.photo_source)}>
            {photo.photo_source.replace('_', ' ')}
          </Badge>
        </div>
        {photo.collected_at && (
          <p className="text-xs text-muted-foreground">
            Collected {formatDistanceToNow(new Date(photo.collected_at), { addSuffix: true })}
          </p>
        )}
      </CardHeader>
      
      <CardContent className="p-0">
        {/* Photo Preview */}
        <div className="aspect-square relative overflow-hidden">
          <img
            src={photo.image_url}
            alt={`Photo for ${photo.business_name}`}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/api/placeholder/300/300';
            }}
          />
          
          {/* Overlay with photo info */}
          <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-end">
            <div className="p-3 w-full bg-gradient-to-t from-black/60 to-transparent">
              <p className="text-white text-xs">
                Source ID: {photo.source_photo_id || 'N/A'}
              </p>
            </div>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="p-4 space-y-2">
          <div className="flex gap-2">
            <Button
              onClick={handleApprove}
              disabled={isLoading || actionLoading !== null}
              className="flex-1 bg-green-600 hover:bg-green-700"
              size="sm"
            >
              {actionLoading === 'approve' ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Check className="w-4 h-4" />
              )}
              Approve
            </Button>
            
            <Button
              onClick={handleReject}
              disabled={isLoading || actionLoading !== null}
              variant="destructive"
              className="flex-1"
              size="sm"
            >
              {actionLoading === 'reject' ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <X className="w-4 h-4" />
              )}
              Reject
            </Button>
          </div>
          
          <Button
            onClick={handleSetPrimary}
            disabled={isLoading || actionLoading !== null}
            variant="outline"
            className="w-full"
            size="sm"
          >
            {actionLoading === 'primary' ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              <Star className="w-4 h-4" />
            )}
            Set as Primary & Approve
          </Button>
          
          {/* View Business Link */}
          <Button
            variant="ghost"
            size="sm"
            className="w-full text-xs"
            onClick={() => window.open(`/business/${photo.business_id}`, '_blank')}
          >
            <ExternalLink className="w-3 h-3 mr-1" />
            View Business
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
