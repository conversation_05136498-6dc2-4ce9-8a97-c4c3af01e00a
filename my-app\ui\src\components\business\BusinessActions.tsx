import { Phone, Globe, Navigation, Share2, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useState } from 'react';

interface BusinessActionsProps {
  business: {
    name: string;
    phone?: string;
    website?: string;
    address: string;
    latitude: string;
    longitude: string;
    slug: string;
  };
}

export function BusinessActions({ business }: BusinessActionsProps) {
  const [isSharing, setIsSharing] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);

  const handleCall = () => {
    if (business.phone) {
      window.location.href = `tel:${business.phone}`;
    }
  };

  const handleWebsite = () => {
    if (business.website) {
      let url = business.website;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`;
      }
      window.open(url, '_blank');
    }
  };

  const handleDirections = () => {
    const lat = parseFloat(business.latitude);
    const lng = parseFloat(business.longitude);
    
    if (!isNaN(lat) && !isNaN(lng)) {
      const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
      window.open(url, '_blank');
    } else {
      // Fallback to address search
      const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
        business.address
      )}`;
      window.open(url, '_blank');
    }
  };

  const handleShare = async () => {
    setIsSharing(true);
    const shareData = {
      title: business.name,
      text: `Check out ${business.name} - ${business.address}`,
      url: window.location.href,
    };

    try {
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(window.location.href);
        // You could show a toast notification here
        alert('Link copied to clipboard!');
      }
    } catch (err) {
      console.error('Error sharing:', err);
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      } catch (clipboardErr) {
        console.error('Clipboard failed:', clipboardErr);
      }
    } finally {
      setIsSharing(false);
    }
  };

  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
    // In a real app, this would save to user preferences or local storage
    // For now, just toggle the state
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Primary Actions */}
          <div className="grid grid-cols-1 gap-2">
            {business.phone && (
              <Button
                onClick={handleCall}
                className="w-full justify-start gap-3"
                size="lg"
              >
                <Phone className="w-5 h-5" />
                Call Now
              </Button>
            )}
            
            {business.website && (
              <Button
                onClick={handleWebsite}
                variant="outline"
                className="w-full justify-start gap-3"
                size="lg"
              >
                <Globe className="w-5 h-5" />
                Visit Website
              </Button>
            )}
            
            <Button
              onClick={handleDirections}
              variant="outline"
              className="w-full justify-start gap-3"
              size="lg"
            >
              <Navigation className="w-5 h-5" />
              Get Directions
            </Button>
          </div>

          {/* Secondary Actions */}
          <div className="grid grid-cols-2 gap-2 pt-2 border-t">
            <Button
              onClick={handleShare}
              variant="ghost"
              size="sm"
              disabled={isSharing}
              className="justify-start gap-2"
            >
              <Share2 className="w-4 h-4" />
              Share
            </Button>
            
            <Button
              onClick={handleFavorite}
              variant="ghost"
              size="sm"
              className={`justify-start gap-2 ${
                isFavorited ? 'text-red-600 hover:text-red-700' : ''
              }`}
            >
              <Heart 
                className={`w-4 h-4 ${
                  isFavorited ? 'fill-current' : ''
                }`} 
              />
              {isFavorited ? 'Saved' : 'Save'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}