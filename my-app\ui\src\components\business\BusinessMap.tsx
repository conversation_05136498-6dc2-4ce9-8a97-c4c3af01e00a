import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { MapPin, Navigation, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BusinessMapProps {
  business: {
    name: string;
    address: string;
    latitude: string;
    longitude: string;
    phone?: string;
    website?: string;
  };
}

export function BusinessMap({ business }: BusinessMapProps) {
  const [error] = useState<string | null>(null);

  const lat = parseFloat(business.latitude);
  const lng = parseFloat(business.longitude);

  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
      business.address
    )}`;
    window.open(url, '_blank');
  };

  const getDirections = () => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
    window.open(url, '_blank');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="w-5 h-5" />
          Location
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Address */}
          <div className="flex items-start gap-3">
            <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
            <div>
              <p className="text-sm">{business.address}</p>
            </div>
          </div>

          {/* Static Map Placeholder */}
          {!isNaN(lat) && !isNaN(lng) && lat && lng ? (
            <div className="relative">
              <div className="w-full h-48 rounded-lg border overflow-hidden bg-muted/30 flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <MapPin className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm font-medium">{business.name}</p>
                  <p className="text-xs">{business.address}</p>
                  <p className="text-xs mt-2">Interactive map coming soon</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="w-full h-48 rounded-lg border bg-muted/20 flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <MapPin className="w-8 h-8 mx-auto mb-2" />
                <p className="text-sm">Location coordinates not available</p>
              </div>
            </div>
          )}

          {error && (
            <p className="text-sm text-muted-foreground">{error}</p>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={getDirections}
              className="flex items-center gap-2"
            >
              <Navigation className="w-4 h-4" />
              Directions
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={openInGoogleMaps}
              className="flex items-center gap-2"
            >
              <ExternalLink className="w-4 h-4" />
              View on Maps
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}