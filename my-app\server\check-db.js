import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { businesses, categories } from './src/schema/business.js';

const sql = postgres('postgresql://postgres:password@localhost:5702/postgres');
const db = drizzle(sql);

async function checkData() {
  try {
    console.log('🔍 Checking database...');
    
    const categoryList = await db.select().from(categories);
    console.log('📂 Categories found:', categoryList.length);
    
    const businessList = await db.select().from(businesses);
    console.log('🏢 Businesses found:', businessList.length);
    
    if (businessList.length > 0) {
      console.log('📋 First few businesses:');
      businessList.slice(0, 5).forEach(b => {
        console.log('  -', b.name, '(Category ID:', b.category_id + ')');
      });
    }
    
    if (categoryList.length > 0) {
      console.log('📂 Categories:');
      categoryList.forEach(c => {
        console.log('  -', c.name, '(ID:', c.id + ')');
      });
    }
    
    await sql.end();
    console.log('✅ Database check complete');
  } catch (error) {
    console.error('❌ Database error:', error.message);
    await sql.end();
  }
}

checkData();
