-- Migration: Add photo collection fields to business_images table
-- Date: 2025-01-08
-- Description: Enhance business_images table to support automated photo collection from external sources

-- Add new columns for photo collection
ALTER TABLE business_images 
ADD COLUMN IF NOT EXISTS photo_source VARCHAR(50) DEFAULT 'manual' NOT NULL,
ADD COLUMN IF NOT EXISTS source_photo_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT TRUE NOT NULL,
ADD COLUMN IF NOT EXISTS collected_at TIMESTAMP;

-- Create index for efficient querying of pending photos
CREATE INDEX IF NOT EXISTS idx_business_images_approval ON business_images(is_approved, photo_source);

-- Create index for efficient querying by source
CREATE INDEX IF NOT EXISTS idx_business_images_source ON business_images(photo_source, source_photo_id);

-- Update existing records to be approved (they were manually added)
UPDATE business_images 
SET is_approved = TRUE, photo_source = 'manual' 
WHERE photo_source IS NULL OR is_approved IS NULL;

-- Add comment to table
COMMENT ON TABLE business_images IS 'Business photos with support for automated collection from external sources';
COMMENT ON COLUMN business_images.photo_source IS 'Source of the photo: manual, google_places, yelp';
COMMENT ON COLUMN business_images.source_photo_id IS 'Original photo ID from external source';
COMMENT ON COLUMN business_images.is_approved IS 'Whether photo has been approved by admin';
COMMENT ON COLUMN business_images.collected_at IS 'Timestamp when photo was collected from external source';
