// Admin category management routes
import { Hono } from 'hono';
import { eq, and, like, desc, asc, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import type { AdminResponse } from '../types/admin';

const categoryRoutes = new Hono();

// ===== CATEGORY MANAGEMENT =====
// Get All Categories
categoryRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '50');
    const search = c.req.query('search');
    const status = c.req.query('status'); // active, inactive, all
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Build where conditions
    const whereConditions = [];
    
    if (status === 'active') {
      whereConditions.push(eq(businessSchema.categories.is_active, true));
    } else if (status === 'inactive') {
      whereConditions.push(eq(businessSchema.categories.is_active, false));
    }
    
    if (search) {
      whereConditions.push(
        sql`(${businessSchema.categories.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.categories.description} ILIKE ${`%${search}%`})`
      );
    }

    let query = db
      .select({
        id: businessSchema.categories.id,
        name: businessSchema.categories.name,
        slug: businessSchema.categories.slug,
        icon: businessSchema.categories.icon,
        description: businessSchema.categories.description,
        is_active: businessSchema.categories.is_active,
        created_at: businessSchema.categories.created_at,
        business_count: sql<number>`count(${businessSchema.businesses.id})`,
      })
      .from(businessSchema.categories)
      .leftJoin(businessSchema.businesses, eq(businessSchema.categories.id, businessSchema.businesses.category_id))
      .groupBy(
        businessSchema.categories.id,
        businessSchema.categories.name,
        businessSchema.categories.slug,
        businessSchema.categories.icon,
        businessSchema.categories.description,
        businessSchema.categories.is_active,
        businessSchema.categories.created_at
      );

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    const categories = await query
      .orderBy(desc(businessSchema.categories.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    let countQuery = db.select({ total: count() }).from(businessSchema.categories);
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    const response: AdminResponse<typeof categories> = {
      data: categories,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        filters: { search, status },
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return c.json({ 
      error: 'Failed to fetch categories',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Create New Category
categoryRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const db = await getDatabase();

    // Validate required fields
    if (!body.name) {
      return c.json({ 
        error: 'Missing required field: name',
        timestamp: new Date().toISOString(),
      }, 400);
    }

    // Generate slug from name
    const slug = body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

    // Check if slug already exists
    const [existingCategory] = await db
      .select()
      .from(businessSchema.categories)
      .where(eq(businessSchema.categories.slug, slug))
      .limit(1);

    if (existingCategory) {
      return c.json({ 
        error: 'Category with this name already exists',
        timestamp: new Date().toISOString(),
      }, 409);
    }

    const [newCategory] = await db
      .insert(businessSchema.categories)
      .values({
        name: body.name,
        slug,
        icon: body.icon || null,
        description: body.description || null,
        is_active: body.is_active !== false,
      })
      .returning();

    const response: AdminResponse<typeof newCategory> = {
      data: newCategory,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response, 201);
  } catch (error) {
    console.error('Error creating category:', error);
    return c.json({ 
      error: 'Failed to create category',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Update Category
categoryRoutes.put('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();
    const db = await getDatabase();

    // Check if category exists
    const [existingCategory] = await db
      .select()
      .from(businessSchema.categories)
      .where(eq(businessSchema.categories.id, id))
      .limit(1);

    if (!existingCategory) {
      return c.json({ 
        error: 'Category not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Update slug if name changed
    const slug = body.name ? 
      body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') :
      existingCategory.slug;

    // Check if new slug conflicts with existing categories (excluding current)
    if (body.name && slug !== existingCategory.slug) {
      const [conflictingCategory] = await db
        .select()
        .from(businessSchema.categories)
        .where(and(
          eq(businessSchema.categories.slug, slug),
          sql`${businessSchema.categories.id} != ${id}`
        ))
        .limit(1);

      if (conflictingCategory) {
        return c.json({ 
          error: 'Category with this name already exists',
          timestamp: new Date().toISOString(),
        }, 409);
      }
    }

    const [updatedCategory] = await db
      .update(businessSchema.categories)
      .set({
        name: body.name || existingCategory.name,
        slug,
        icon: body.icon !== undefined ? body.icon : existingCategory.icon,
        description: body.description !== undefined ? body.description : existingCategory.description,
        is_active: body.is_active !== undefined ? body.is_active : existingCategory.is_active,
        updated_at: new Date(),
      })
      .where(eq(businessSchema.categories.id, id))
      .returning();

    const response: AdminResponse<typeof updatedCategory> = {
      data: updatedCategory,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

    return c.json(response);
  } catch (error) {
    console.error('Error updating category:', error);
    return c.json({ 
      error: 'Failed to update category',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Delete Category
categoryRoutes.delete('/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const db = await getDatabase();

    // Check if category exists
    const [existingCategory] = await db
      .select()
      .from(businessSchema.categories)
      .where(eq(businessSchema.categories.id, id))
      .limit(1);

    if (!existingCategory) {
      return c.json({ 
        error: 'Category not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    // Check if category has businesses
    const [businessCount] = await db
      .select({ count: count() })
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.category_id, id));

    if (businessCount.count > 0) {
      return c.json({ 
        error: `Cannot delete category with ${businessCount.count} associated businesses`,
        timestamp: new Date().toISOString(),
      }, 409);
    }

    // Delete category
    await db
      .delete(businessSchema.categories)
      .where(eq(businessSchema.categories.id, id));

    return c.json({
      message: 'Category deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    return c.json({ 
      error: 'Failed to delete category',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Get Businesses in Category
categoryRoutes.get('/:id/businesses', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Check if category exists
    const [category] = await db
      .select()
      .from(businessSchema.categories)
      .where(eq(businessSchema.categories.id, id))
      .limit(1);

    if (!category) {
      return c.json({ 
        error: 'Category not found',
        timestamp: new Date().toISOString(),
      }, 404);
    }

    const businesses = await db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        is_featured: businessSchema.businesses.is_featured,
        is_active: businessSchema.businesses.is_active,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
      })
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.category_id, id))
      .orderBy(desc(businessSchema.businesses.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.businesses)
      .where(eq(businessSchema.businesses.category_id, id));

    const response: AdminResponse<typeof businesses> = {
      data: businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      meta: {
        category: category.name,
        timestamp: new Date().toISOString(),
      }
    };

    return c.json(response);
  } catch (error) {
    console.error('Error fetching category businesses:', error);
    return c.json({ 
      error: 'Failed to fetch category businesses',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

export default categoryRoutes;
