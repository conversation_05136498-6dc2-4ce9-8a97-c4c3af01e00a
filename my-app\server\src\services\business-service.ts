// Business service layer
import { eq, and, desc, asc, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import type { Business, Category, BusinessWithDetails } from '../schema/business';

export interface BusinessListParams {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  featured?: boolean;
  sortBy?: 'name' | 'rating' | 'newest' | 'relevance';
  rating?: number;
  location?: string;
}

export interface BusinessListResponse {
  businesses: (Business & { category: Category })[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface SearchParams extends BusinessListParams {
  query?: string;
}

export interface SearchResponse extends BusinessListResponse {
  search: {
    query?: string;
    category?: string;
    location?: string;
    rating?: number;
    featured?: boolean;
    sortBy?: string;
  };
}

export class BusinessService {
  /**
   * Get businesses with pagination and filtering
   */
  static async getBusinesses(params: BusinessListParams): Promise<BusinessListResponse> {
    const {
      page = 1,
      limit = 20,
      category,
      search,
      featured,
      sortBy = 'newest'
    } = params;
    
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businesses.is_active, true));

    // Build where conditions
    const whereConditions = [eq(businessSchema.businesses.is_active, true)];

    if (category) {
      whereConditions.push(eq(businessSchema.businesses.category_id, parseInt(category)));
    }

    if (search) {
      whereConditions.push(
        sql`(${businessSchema.businesses.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.short_description} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.address} ILIKE ${`%${search}%`})`
      );
    }

    if (featured) {
      whereConditions.push(eq(businessSchema.businesses.is_featured, true));
    }

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Apply sorting
    switch (sortBy) {
      case 'name':
        query = query.orderBy(asc(businessSchema.businesses.name));
        break;
      case 'rating':
        query = query.orderBy(desc(businessSchema.businesses.average_rating), desc(businessSchema.businesses.total_reviews));
        break;
      case 'newest':
      default:
        query = query.orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at));
        break;
    }

    const businesses = await query.limit(limit).offset(offset);

    // Get total count
    let countQuery = db.select({ total: count() }).from(businessSchema.businesses);
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    return {
      businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get single business by slug with complete details
   */
  static async getBusiness(slug: string): Promise<BusinessWithDetails | null> {
    const db = await getDatabase();

    const [business] = await db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        description: businessSchema.businesses.description,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        }
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.slug, slug),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      return null;
    }

    // Get business hours
    const hours = await db
      .select()
      .from(businessSchema.businessHours)
      .where(eq(businessSchema.businessHours.business_id, business.id))
      .orderBy(businessSchema.businessHours.day_of_week);

    // Get business images
    const images = await db
      .select()
      .from(businessSchema.businessImages)
      .where(eq(businessSchema.businessImages.business_id, business.id))
      .orderBy(businessSchema.businessImages.display_order);

    // Get approved reviews
    const reviews = await db
      .select()
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, business.id),
        eq(businessSchema.reviews.is_approved, true)
      ))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(10);

    return {
      ...business,
      hours,
      images,
      reviews
    };
  }

  /**
   * Advanced search with full-text search capabilities
   */
  static async searchBusinesses(params: SearchParams): Promise<SearchResponse> {
    const {
      page = 1,
      limit = 20,
      query,
      category,
      location,
      rating,
      featured,
      sortBy = 'relevance'
    } = params;
    
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    let dbQuery = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        },
        // Add search relevance score when using full-text search
        ...(query && {
          search_rank: sql<number>`
            ts_rank_cd(
              to_tsvector('english', ${businessSchema.businesses.name} || ' ' || 
                COALESCE(${businessSchema.businesses.description}, '') || ' ' || 
                COALESCE(${businessSchema.businesses.short_description}, '')),
              to_tsquery('english', ${query.split(' ').map(term => `${term}:*`).join(' & ')})
            )
          `
        })
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businesses.is_active, true));

    // Build where conditions
    const whereConditions = [eq(businessSchema.businesses.is_active, true)];

    // Full-text search
    if (query) {
      const searchTerms = query.split(' ').map(term => `${term}:*`).join(' & ');
      whereConditions.push(
        sql`to_tsvector('english', ${businessSchema.businesses.name} || ' ' || 
            COALESCE(${businessSchema.businesses.description}, '') || ' ' || 
            COALESCE(${businessSchema.businesses.short_description}, '')) 
            @@ to_tsquery('english', ${searchTerms})`
      );
    }

    if (category) {
      whereConditions.push(eq(businessSchema.businesses.category_id, parseInt(category)));
    }

    if (location) {
      whereConditions.push(
        sql`${businessSchema.businesses.address} ILIKE ${`%${location}%`}`
      );
    }

    if (rating) {
      whereConditions.push(
        sql`${businessSchema.businesses.average_rating} >= ${rating}`
      );
    }

    if (featured) {
      whereConditions.push(eq(businessSchema.businesses.is_featured, true));
    }

    if (whereConditions.length > 0) {
      dbQuery = dbQuery.where(and(...whereConditions));
    }

    // Apply sorting
    switch (sortBy) {
      case 'rating':
        dbQuery = dbQuery.orderBy(desc(businessSchema.businesses.average_rating), desc(businessSchema.businesses.total_reviews));
        break;
      case 'newest':
        dbQuery = dbQuery.orderBy(desc(businessSchema.businesses.created_at));
        break;
      case 'name':
        dbQuery = dbQuery.orderBy(asc(businessSchema.businesses.name));
        break;
      case 'relevance':
      default:
        if (query) {
          dbQuery = dbQuery.orderBy(
            sql`ts_rank_cd(
              to_tsvector('english', ${businessSchema.businesses.name} || ' ' || 
                COALESCE(${businessSchema.businesses.description}, '') || ' ' || 
                COALESCE(${businessSchema.businesses.short_description}, '')),
              to_tsquery('english', ${query.split(' ').map(term => `${term}:*`).join(' & ')})
            ) DESC`,
            desc(businessSchema.businesses.is_featured),
            desc(businessSchema.businesses.average_rating)
          );
        } else {
          dbQuery = dbQuery.orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at));
        }
        break;
    }

    const businesses = await dbQuery.limit(limit).offset(offset);

    // Get total count
    let countQuery = db.select({ total: count() }).from(businessSchema.businesses);
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    return {
      businesses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      search: {
        query,
        category,
        location,
        rating,
        featured,
        sortBy
      }
    };
  }

  /**
   * Get business reviews with pagination
   */
  static async getBusinessReviews(businessId: number, page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Check if business exists and is active
    const [business] = await db
      .select({ id: businessSchema.businesses.id })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.id, businessId),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!business) {
      throw new Error('Business not found');
    }

    // Get approved reviews
    const reviews = await db
      .select()
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, businessId),
        eq(businessSchema.reviews.is_approved, true)
      ))
      .orderBy(desc(businessSchema.reviews.created_at))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db
      .select({ total: count() })
      .from(businessSchema.reviews)
      .where(and(
        eq(businessSchema.reviews.business_id, businessId),
        eq(businessSchema.reviews.is_approved, true)
      ));

    return {
      reviews,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get categories with business counts
   */
  static async getCategories() {
    const db = await getDatabase();
    
    return await db
      .select({
        id: businessSchema.categories.id,
        name: businessSchema.categories.name,
        slug: businessSchema.categories.slug,
        icon: businessSchema.categories.icon,
        description: businessSchema.categories.description,
        business_count: sql<number>`count(${businessSchema.businesses.id})`,
      })
      .from(businessSchema.categories)
      .leftJoin(businessSchema.businesses, and(
        eq(businessSchema.categories.id, businessSchema.businesses.category_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .where(eq(businessSchema.categories.is_active, true))
      .groupBy(
        businessSchema.categories.id,
        businessSchema.categories.name,
        businessSchema.categories.slug,
        businessSchema.categories.icon,
        businessSchema.categories.description
      )
      .orderBy(businessSchema.categories.name);
  }

  /**
   * Get search suggestions
   */
  static async getSearchSuggestions(query: string, limit = 5) {
    if (!query || query.length < 2) {
      return [];
    }

    const db = await getDatabase();

    // Get business name suggestions
    const businessSuggestions = await db
      .select({
        type: sql<string>`'business'`,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        category_name: businessSchema.categories.name
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`${businessSchema.businesses.name} ILIKE ${`%${query}%`}`
      ))
      .limit(limit);

    // Get category suggestions
    const categorySuggestions = await db
      .select({
        type: sql<string>`'category'`,
        name: businessSchema.categories.name,
        slug: businessSchema.categories.slug,
        business_count: sql<number>`count(${businessSchema.businesses.id})`
      })
      .from(businessSchema.categories)
      .leftJoin(businessSchema.businesses, and(
        eq(businessSchema.categories.id, businessSchema.businesses.category_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .where(and(
        eq(businessSchema.categories.is_active, true),
        sql`${businessSchema.categories.name} ILIKE ${`%${query}%`}`
      ))
      .groupBy(businessSchema.categories.id, businessSchema.categories.name, businessSchema.categories.slug)
      .limit(limit);

    return [
      ...businessSuggestions.map(s => ({ ...s, category_name: s.category_name })),
      ...categorySuggestions.map(s => ({ ...s, category_name: null }))
    ].slice(0, limit);
  }
}