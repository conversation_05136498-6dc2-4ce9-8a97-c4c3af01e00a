import { useState } from 'react';
import { Star, MapPin, Phone, Mail, Globe, Clock, Search, Bell } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';

// Mock data for demonstration
const businessData = {
  name: "Tech Solutions Inc.",
  rating: 4.8,
  totalReviews: 127,
  isOpen: true,
  hours: "9:00 AM - 5:00 PM",
  logo: "/api/placeholder/80/80",
  description: "Tech Solutions Inc. is a leading provider of IT services for small and medium-sized businesses. We offer a wide range of services, including network management, cybersecurity solutions, and IT consulting. Our team of experienced professionals is dedicated to providing reliable and cost-effective solutions to meet your business needs.",
  contact: {
    phone: "(*************",
    email: "<EMAIL>",
    website: "www.techsolutions.com"
  },
  address: "123 Oak Avenue, Springfield, MA",
  photos: [
    "/api/placeholder/300/300",
    "/api/placeholder/300/300", 
    "/api/placeholder/300/300",
    "/api/placeholder/300/300"
  ],
  reviews: [
    {
      id: 1,
      author: "<PERSON> <PERSON>",
      avatar: "/api/placeholder/40/40",
      rating: 5,
      timeAgo: "2 weeks ago",
      comment: "Tech Solutions Inc. has been instrumental in improving our IT infrastructure. Their team is responsive, knowledgeable, and always goes the extra mile."
    },
    {
      id: 2,
      author: "Michael Johnson", 
      avatar: "/api/placeholder/40/40",
      rating: 4,
      timeAgo: "1 month ago",
      comment: "We've been using Tech Solutions for our IT needs for over a year, and we're satisfied with their service. Prompt without any major issues."
    },
    {
      id: 3,
      author: "Olivia Reed",
      avatar: "/api/placeholder/40/40", 
      rating: 5,
      timeAgo: "2 months ago",
      comment: "Tech Solutions Inc. is a dependable IT provider. Their expertise and customer service are exceptional. Highly recommended."
    }
  ],
  ratingBreakdown: {
    5: 65,
    4: 17,
    3: 8,
    2: 5,
    1: 5
  }
};

const StarIcon = ({ filled = false }: { filled?: boolean }) => (
  <Star 
    className={`w-4 h-4 ${filled ? 'fill-current text-yellow-400' : 'text-gray-300'}`}
  />
);

const RatingStars = ({ rating, showCount = false, count = 0 }: { rating: number; showCount?: boolean; count?: number }) => {
  const stars = [];
  for (let i = 1; i <= 5; i++) {
    stars.push(<StarIcon key={i} filled={i <= rating} />);
  }
  return (
    <div className="flex items-center gap-1">
      {stars}
      {showCount && <span className="text-sm text-muted-foreground ml-1">({count})</span>}
    </div>
  );
};

export function LocalBizBusinessProfile() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-background" style={{ fontFamily: '"Space Grotesk", "Noto Sans", sans-serif' }}>
      {/* Header */}
      <header className="flex items-center justify-between whitespace-nowrap border-b border-border px-10 py-3">
        <div className="flex items-center gap-8">
          <div className="flex items-center gap-4 text-foreground">
            <div className="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M24 4H6v40h36V4H24zM8 42V6h14v36H8zm16 0V6h14v36H24z"
                  fill="currentColor"
                />
              </svg>
            </div>
            <h2 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em]">LocalBiz</h2>
          </div>
          <div className="flex items-center gap-9">
            <a className="text-foreground text-sm font-medium leading-normal" href="#">Home</a>
            <a className="text-foreground text-sm font-medium leading-normal" href="#">Categories</a>
            <a className="text-foreground text-sm font-medium leading-normal" href="#">Deals</a>
            <a className="text-foreground text-sm font-medium leading-normal" href="#">Contact</a>
          </div>
        </div>
        <div className="flex flex-1 justify-end gap-8">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search"
                className="pl-10 w-64 bg-muted border-border"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="ghost" size="icon">
              <Bell className="w-4 h-4" />
            </Button>
            <Avatar className="w-8 h-8">
              <AvatarImage src="/api/placeholder/32/32" />
              <AvatarFallback>U</AvatarFallback>
            </Avatar>
          </div>
        </div>
      </header>

      {/* Business Hero Section */}
      <div className="px-10 py-6">
        <Card className="rounded-xl border-border">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <img
                src={businessData.logo}
                alt={`${businessData.name} logo`}
                className="w-20 h-20 rounded-xl object-cover"
              />
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-foreground mb-2">{businessData.name}</h1>
                <div className="flex items-center gap-4 mb-2">
                  <RatingStars rating={Math.floor(businessData.rating)} showCount count={businessData.totalReviews} />
                  <span className="text-sm font-medium">{businessData.rating}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={businessData.isOpen ? "default" : "secondary"} className="text-xs">
                    {businessData.isOpen ? "Open" : "Closed"}
                  </Badge>
                  <span className="text-sm text-muted-foreground">{businessData.hours}</span>
                </div>
              </div>
            </div>
            
            {/* Tabs */}
            <div className="flex gap-6 mt-6 border-b border-border">
              {['Overview', 'Reviews', 'Photos', 'Services'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab.toLowerCase())}
                  className={`pb-3 px-1 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.toLowerCase()
                      ? 'border-primary text-primary'
                      : 'border-transparent text-muted-foreground hover:text-foreground'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="px-10 pb-10">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* About Section */}
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">About</h2>
              <p className="text-muted-foreground leading-relaxed">
                {businessData.description}
              </p>
            </section>

            {/* Contact Section */}
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">Contact</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3">
                  <Phone className="w-5 h-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Phone</p>
                    <p className="font-medium">{businessData.contact.phone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{businessData.contact.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Globe className="w-5 h-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Website</p>
                    <p className="font-medium">{businessData.contact.website}</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Location Section */}
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">Location</h2>
              <p className="text-muted-foreground mb-4">{businessData.address}</p>
              <div className="w-full h-64 bg-muted rounded-xl overflow-hidden">
                <img
                  src="/api/placeholder/800/256"
                  alt="Map location"
                  className="w-full h-full object-cover"
                />
              </div>
            </section>

            {/* Photos Section */}
            <section>
              <h2 className="text-xl font-bold text-foreground mb-4">Photos</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {businessData.photos.map((photo, index) => (
                  <div key={index} className="aspect-square rounded-xl overflow-hidden">
                    <img
                      src={photo}
                      alt={`Business photo ${index + 1}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                    />
                  </div>
                ))}
              </div>
            </section>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Reviews Summary */}
            <Card className="rounded-xl border-border">
              <CardContent className="p-6">
                <h3 className="text-lg font-bold text-foreground mb-4">Reviews</h3>
                <div className="flex items-center gap-4 mb-4">
                  <span className="text-3xl font-bold">{businessData.rating}</span>
                  <div>
                    <RatingStars rating={Math.floor(businessData.rating)} />
                    <p className="text-sm text-muted-foreground">{businessData.totalReviews} reviews</p>
                  </div>
                </div>
                
                {/* Rating Breakdown */}
                <div className="space-y-2">
                  {Object.entries(businessData.ratingBreakdown).reverse().map(([stars, percentage]) => (
                    <div key={stars} className="flex items-center gap-2">
                      <span className="text-sm w-2">{stars}</span>
                      <Star className="w-3 h-3 fill-current text-yellow-400" />
                      <div className="flex-1 bg-muted rounded-full h-2">
                        <div 
                          className="bg-yellow-400 h-2 rounded-full" 
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-sm text-muted-foreground w-8">{percentage}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Individual Reviews */}
            <div className="space-y-4">
              {businessData.reviews.map((review) => (
                <Card key={review.id} className="rounded-xl border-border">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={review.avatar} />
                        <AvatarFallback>{review.author[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-sm">{review.author}</h4>
                          <span className="text-xs text-muted-foreground">{review.timeAgo}</span>
                        </div>
                        <RatingStars rating={review.rating} />
                        <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
                          {review.comment}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
