/**
 * Theme Selector Component
 * 
 * A comprehensive theme selection component with multiple variants
 */

import * as React from 'react';
import { Sun, Moon, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useThemePreference } from '@/hooks/useThemePreference';
import { getTransitionClasses, themeConfig, type ThemeOption } from '@/lib/theme-utils';

interface ThemeSelectorProps {
  variant?: 'buttons' | 'radio' | 'cards';
  orientation?: 'horizontal' | 'vertical';
  showLabels?: boolean;
  showIcons?: boolean;
  showDescriptions?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ThemeSelector({
  variant = 'buttons',
  orientation = 'horizontal',
  showLabels = true,
  showIcons = true,
  showDescriptions = false,
  size = 'md',
  className,
  ...props
}: ThemeSelectorProps) {
  const { 
    theme, 
    setTheme, 
    mounted,
    isActiveTheme,
    availableThemes 
  } = useThemePreference();

  // Show loading state during hydration
  if (!mounted) {
    return (
      <div className={cn(
        'flex gap-2',
        orientation === 'vertical' && 'flex-col',
        className
      )}>
        {[1, 2, 3].map((i) => (
          <Button key={i} variant="outline" size={size} disabled>
            <Sun className="h-4 w-4" />
          </Button>
        ))}
      </div>
    );
  }

  const getThemeIcon = (themeName: string) => {
    switch (themeName) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const getThemeDescription = (themeName: string) => {
    switch (themeName) {
      case 'light':
        return 'Bright and clean interface';
      case 'dark':
        return 'Easy on the eyes in low light';
      case 'system':
        return 'Matches your device setting';
      default:
        return 'Unknown theme';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          button: 'h-8 px-3 text-xs',
          icon: 'h-3 w-3',
          gap: 'gap-1.5'
        };
      case 'lg':
        return {
          button: 'h-12 px-6 text-base',
          icon: 'h-5 w-5',
          gap: 'gap-3'
        };
      default:
        return {
          button: 'h-10 px-4 text-sm',
          icon: 'h-4 w-4',
          gap: 'gap-2'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  if (variant === 'cards') {
    return (
      <div className={cn(
        'grid gap-3',
        orientation === 'horizontal' ? 'grid-cols-3' : 'grid-cols-1',
        className
      )}>
        {availableThemes.map((themeName) => {
          const isActive = isActiveTheme(themeName);
          const config = themeConfig[themeName];
          
          return (
            <button
              key={themeName}
              onClick={() => setTheme(themeName)}
              className={cn(
                'flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-all',
                'hover:bg-accent hover:text-accent-foreground',
                'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                isActive 
                  ? 'border-primary bg-primary/5 text-primary' 
                  : 'border-border bg-card text-card-foreground',
                getTransitionClasses('normal')
              )}
              aria-label={`Select ${config.label} theme`}
            >
              {showIcons && (
                <span className={cn(
                  'transition-transform',
                  isActive && 'scale-110'
                )}>
                  {getThemeIcon(themeName)}
                </span>
              )}
              {showLabels && (
                <span className="label font-medium">
                  {config.label}
                </span>
              )}
              {showDescriptions && (
                <span className="caption text-center">
                  {getThemeDescription(themeName)}
                </span>
              )}
            </button>
          );
        })}
      </div>
    );
  }

  if (variant === 'radio') {
    return (
      <div className={cn(
        'flex',
        orientation === 'vertical' ? 'flex-col gap-3' : 'gap-4',
        className
      )}>
        {availableThemes.map((themeName) => {
          const isActive = isActiveTheme(themeName);
          const config = themeConfig[themeName];
          const id = `theme-${themeName}`;
          
          return (
            <div key={themeName} className="flex items-center gap-2">
              <input
                type="radio"
                id={id}
                name="theme"
                checked={isActive}
                onChange={() => setTheme(themeName)}
                className="sr-only"
              />
              <Label
                htmlFor={id}
                className={cn(
                  'flex items-center gap-2 cursor-pointer p-2 rounded-md transition-all',
                  'hover:bg-accent hover:text-accent-foreground',
                  isActive && 'text-primary',
                  getTransitionClasses('fast')
                )}
              >
                <span className={cn(
                  'w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all',
                  isActive 
                    ? 'border-primary bg-primary' 
                    : 'border-border bg-background'
                )}>
                  {isActive && (
                    <span className="w-2 h-2 rounded-full bg-primary-foreground" />
                  )}
                </span>
                {showIcons && getThemeIcon(themeName)}
                {showLabels && (
                  <span className="label">
                    {config.label}
                  </span>
                )}
              </Label>
            </div>
          );
        })}
      </div>
    );
  }

  // Default: buttons variant
  return (
    <div className={cn(
      'flex',
      orientation === 'vertical' ? 'flex-col' : 'flex-row',
      sizeClasses.gap,
      className
    )}>
      {availableThemes.map((themeName) => {
        const isActive = isActiveTheme(themeName);
        const config = themeConfig[themeName];
        
        return (
          <Button
            key={themeName}
            variant={isActive ? 'default' : 'outline'}
            size={size}
            onClick={() => setTheme(themeName)}
            className={cn(
              'flex items-center',
              sizeClasses.gap,
              getTransitionClasses('button'),
              !showLabels && 'aspect-square p-0'
            )}
            aria-label={`Select ${config.label} theme`}
            aria-pressed={isActive}
          >
            {showIcons && (
              <span className={getTransitionClasses('icon')}>
                {getThemeIcon(themeName)}
              </span>
            )}
            {showLabels && (
              <span className="label">
                {config.label}
              </span>
            )}
          </Button>
        );
      })}
    </div>
  );
}

/**
 * Compact Theme Selector
 * 
 * Icon-only theme selector for space-constrained layouts
 */
export function ThemeSelectorCompact({
  className,
  ...props
}: Omit<ThemeSelectorProps, 'showLabels' | 'size'>) {
  return (
    <ThemeSelector
      showLabels={false}
      size="sm"
      className={className}
      {...props}
    />
  );
}

/**
 * Theme Selector with Descriptions
 * 
 * Card-style theme selector with descriptions
 */
export function ThemeSelectorDetailed({
  className,
  ...props
}: Omit<ThemeSelectorProps, 'variant' | 'showDescriptions'>) {
  return (
    <ThemeSelector
      variant="cards"
      showDescriptions={true}
      className={className}
      {...props}
    />
  );
}
