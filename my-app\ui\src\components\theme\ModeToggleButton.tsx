/**
 * Mode Toggle Button Component
 * 
 * A button-style theme toggle with text and icon options
 */

import * as React from 'react';
import { Sun, Moon, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useThemePreference } from '@/hooks/useThemePreference';
import { getTransitionClasses, getThemeAriaLabel } from '@/lib/theme-utils';

interface ModeToggleButtonProps {
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  showIcon?: boolean;
  showSystemOption?: boolean;
  className?: string;
}

export function ModeToggleButton({
  variant = 'outline',
  size = 'sm',
  showText = true,
  showIcon = true,
  showSystemOption = false,
  className,
  ...props
}: ModeToggleButtonProps) {
  const { 
    theme, 
    setTheme, 
    resolvedTheme, 
    mounted,
    toggleTheme 
  } = useThemePreference();

  // Show loading state during hydration
  if (!mounted) {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn('gap-2', className)}
        disabled
        {...props}
      >
        {showIcon && <Sun className="h-4 w-4" />}
        {showText && <span className="label">Theme</span>}
      </Button>
    );
  }

  const handleClick = () => {
    if (showSystemOption) {
      toggleTheme(); // Cycles through light -> dark -> system
    } else {
      // Simple toggle between light and dark
      setTheme(resolvedTheme === 'light' ? 'dark' : 'light');
    }
  };

  const getButtonContent = () => {
    const isLight = resolvedTheme === 'light';
    const isDark = resolvedTheme === 'dark';
    const isSystem = theme === 'system';

    if (isSystem && showSystemOption) {
      return {
        icon: <Monitor className="h-4 w-4" />,
        text: 'Auto'
      };
    }

    if (isDark) {
      return {
        icon: <Moon className="h-4 w-4" />,
        text: 'Dark'
      };
    }

    return {
      icon: <Sun className="h-4 w-4" />,
      text: 'Light'
    };
  };

  const content = getButtonContent();
  const ariaLabel = getThemeAriaLabel(theme || 'light');

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      className={cn(
        'gap-2',
        getTransitionClasses('button'),
        className
      )}
      aria-label={ariaLabel}
      title={ariaLabel}
      {...props}
    >
      {showIcon && (
        <span className={getTransitionClasses('icon')}>
          {content.icon}
        </span>
      )}
      {showText && (
        <span className="label">
          {content.text}
        </span>
      )}
    </Button>
  );
}

/**
 * Compact Mode Toggle Button
 * 
 * A minimal version for mobile or space-constrained layouts
 */
export function ModeToggleCompact({
  className,
  ...props
}: Omit<ModeToggleButtonProps, 'showText' | 'showIcon' | 'size'>) {
  return (
    <ModeToggleButton
      size="sm"
      showText={false}
      showIcon={true}
      className={cn('h-8 w-8 p-0', className)}
      {...props}
    />
  );
}

/**
 * Mode Toggle with Text Only
 * 
 * Text-only version for minimal designs
 */
export function ModeToggleText({
  className,
  ...props
}: Omit<ModeToggleButtonProps, 'showText' | 'showIcon'>) {
  return (
    <ModeToggleButton
      showText={true}
      showIcon={false}
      className={cn('px-3', className)}
      {...props}
    />
  );
}

/**
 * Animated Mode Toggle Button
 * 
 * Version with enhanced animations for the icons
 */
export function ModeToggleAnimated({
  className,
  ...props
}: Omit<ModeToggleButtonProps, 'showIcon'>) {
  const { resolvedTheme, mounted, toggleTheme } = useThemePreference();

  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className={cn('h-8 w-8', className)}
        disabled
        {...props}
      >
        <Sun className="h-4 w-4" />
      </Button>
    );
  }

  const isLight = resolvedTheme === 'light';

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className={cn(
        'h-8 w-8 relative',
        getTransitionClasses('button'),
        className
      )}
      aria-label={getThemeAriaLabel(resolvedTheme || 'light')}
      {...props}
    >
      <Sun 
        className={cn(
          'h-4 w-4 absolute transition-all duration-300 ease-in-out',
          isLight 
            ? 'rotate-0 scale-100 opacity-100' 
            : '-rotate-90 scale-0 opacity-0'
        )} 
      />
      <Moon 
        className={cn(
          'h-4 w-4 absolute transition-all duration-300 ease-in-out',
          !isLight 
            ? 'rotate-0 scale-100 opacity-100' 
            : 'rotate-90 scale-0 opacity-0'
        )} 
      />
    </Button>
  );
}
