# Memory Docs System

This directory contains the centralized knowledge management system for the Volo App Template business directory project.

## 📁 Directory Structure

```
memory-docs/
├── architecture/          # High-level system documentation and diagrams
├── fix-guides/           # Post-mortems and resolutions for past issues
├── open-issues/          # Issues that are still broken and need work
├── audits/              # Performance, cost, and security reviews
├── references/          # APIs, schemas, and checklists
├── progress.md          # Running changelog of project evolution
├── implementation-plan.md # Current build blueprint
└── features.md          # Consolidated shipped-feature documentation
```

## 🎯 Purpose

This system serves as the project's institutional memory, capturing:
- **Architectural decisions** and their rationale
- **Issue resolution patterns** for faster debugging
- **Performance insights** and optimization history
- **Security findings** and remediation steps
- **Feature evolution** and implementation details

## 📋 Usage Guidelines

### For Developers
- Check `open-issues/` before starting work on bugs
- Review `fix-guides/` for similar problems and solutions
- Update `progress.md` with significant changes
- Document new features in `features.md`

### For Maintainers
- Create post-mortems in `fix-guides/` after critical issues
- Update `implementation-plan.md` for sprint planning
- Archive resolved issues from `open-issues/` to `fix-guides/`
- Conduct regular audits and document in `audits/`

### For Onboarding
- Start with `features.md` for product overview
- Review `architecture/` for system understanding
- Check `implementation-plan.md` for current priorities

## 🔄 Maintenance

This system is automatically maintained by the Documentation Specialist agent and updated based on:
- Git commit messages and PR descriptions
- Issue tracker synchronization
- Performance monitoring alerts
- Security scan results
- Manual updates from team members

---

**Last Updated**: 2025-01-08  
**Maintainer**: Documentation Specialist Agent  
**Version**: 1.0
