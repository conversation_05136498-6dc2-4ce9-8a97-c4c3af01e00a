import { useState, useEffect } from 'react';
import { api } from '@/lib/serverComm';

interface BusinessData {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  address: string;
  latitude: string;
  longitude: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: string;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
    description: string;
  };
  hours: Array<{
    id: number;
    day_of_week: string;
    open_time: string;
    close_time: string;
    is_closed: boolean;
  }>;
  images: Array<{
    id: number;
    image_url: string;
    alt_text: string;
    is_primary: boolean;
    display_order: number;
  }>;
  reviews: Array<{
    id: number;
    rating: number;
    comment: string;
    author_name: string;
    created_at: string;
    is_verified: boolean;
  }>;
}

interface UseBusinessReturn {
  business: BusinessData | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useBusiness(slug: string): UseBusinessReturn {
  const [business, setBusiness] = useState<BusinessData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBusiness = async () => {
    if (!slug) {
      setError('Business slug is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await api.getBusiness(slug);
      
      if (response.business) {
        setBusiness(response.business);
      } else {
        setError('Business not found');
        setBusiness(null);
      }
    } catch (err: any) {
      if (err.status === 404) {
        setError('Business not found');
      } else if (err.status >= 500) {
        setError('Server error. Please try again later.');
      } else {
        setError('Failed to load business information');
      }
      console.error('Error fetching business:', err);
      setBusiness(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBusiness();
  }, [slug]);

  return {
    business,
    loading,
    error,
    refetch: fetchBusiness,
  };
}