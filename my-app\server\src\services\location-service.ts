// Location-based search service
import { eq, and, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';

export interface LocationSearchParams {
  latitude: number;
  longitude: number;
  radius?: number; // in kilometers, default 50km
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  rating?: number;
}

export interface BusinessWithDistance {
  id: number;
  name: string;
  slug: string;
  short_description: string | null;
  address: string;
  latitude: string | null;
  longitude: string | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  logo_url: string | null;
  hero_image_url: string | null;
  is_featured: boolean;
  average_rating: string | null;
  total_reviews: number;
  created_at: Date;
  distance: number; // in kilometers
  category: {
    id: number;
    name: string;
    slug: string;
  } | null;
}

export class LocationService {
  /**
   * Calculate distance between two points using Haversine formula
   * Returns distance in kilometers
   */
  private static calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI/180);
  }

  /**
   * Search businesses within a radius using PostGIS ST_Distance_Sphere function
   * Falls back to Haversine formula if PostGIS is not available
   */
  static async searchBusinessesByLocation(params: LocationSearchParams): Promise<{
    businesses: BusinessWithDistance[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    const {
      latitude,
      longitude,
      radius = 50,
      page = 1,
      limit = 20,
      category,
      search,
      rating
    } = params;

    const offset = (page - 1) * limit;
    const db = await getDatabase();

    // Build base query with distance calculation
    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        },
        // Use PostGIS if available, otherwise use a simpler calculation
        distance: sql<number>`
          CASE 
            WHEN ${businessSchema.businesses.latitude} IS NOT NULL 
            AND ${businessSchema.businesses.longitude} IS NOT NULL
            THEN (
              6371 * acos(
                cos(radians(${latitude})) * 
                cos(radians(CAST(${businessSchema.businesses.latitude} AS DECIMAL))) * 
                cos(radians(CAST(${businessSchema.businesses.longitude} AS DECIMAL)) - radians(${longitude})) + 
                sin(radians(${latitude})) * 
                sin(radians(CAST(${businessSchema.businesses.latitude} AS DECIMAL)))
              )
            )
            ELSE 999999
          END
        `
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id));

    // Build where conditions
    const whereConditions = [
      eq(businessSchema.businesses.is_active, true),
      sql`${businessSchema.businesses.latitude} IS NOT NULL`,
      sql`${businessSchema.businesses.longitude} IS NOT NULL`,
      // Distance filter using Haversine formula
      sql`(
        6371 * acos(
          cos(radians(${latitude})) * 
          cos(radians(CAST(${businessSchema.businesses.latitude} AS DECIMAL))) * 
          cos(radians(CAST(${businessSchema.businesses.longitude} AS DECIMAL)) - radians(${longitude})) + 
          sin(radians(${latitude})) * 
          sin(radians(CAST(${businessSchema.businesses.latitude} AS DECIMAL)))
        )
      ) <= ${radius}`
    ];

    if (category) {
      whereConditions.push(eq(businessSchema.businesses.category_id, parseInt(category)));
    }

    if (search) {
      whereConditions.push(
        sql`(${businessSchema.businesses.name} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.short_description} ILIKE ${`%${search}%`} OR 
            ${businessSchema.businesses.address} ILIKE ${`%${search}%`})`
      );
    }

    if (rating) {
      whereConditions.push(
        sql`CAST(${businessSchema.businesses.average_rating} AS DECIMAL) >= ${rating}`
      );
    }

    // Apply all conditions
    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Order by distance, then by featured status
    query = query
      .orderBy(
        sql`distance ASC`,
        sql`${businessSchema.businesses.is_featured} DESC`,
        sql`${businessSchema.businesses.average_rating} DESC`
      );

    const businesses = await query.limit(limit).offset(offset);

    // Get total count with same filters
    let countQuery = db
      .select({ total: sql<number>`count(*)` })
      .from(businessSchema.businesses);

    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    return {
      businesses: businesses.map(business => ({
        ...business,
        distance: business.distance || 0
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get businesses near a specific business
   */
  static async getNearbyBusinesses(businessId: number, radius = 10, limit = 5): Promise<BusinessWithDistance[]> {
    const db = await getDatabase();

    // First get the target business coordinates
    const [targetBusiness] = await db
      .select({
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
      })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.id, businessId),
        eq(businessSchema.businesses.is_active, true)
      ))
      .limit(1);

    if (!targetBusiness || !targetBusiness.latitude || !targetBusiness.longitude) {
      return [];
    }

    const lat = parseFloat(targetBusiness.latitude);
    const lon = parseFloat(targetBusiness.longitude);

    return this.searchBusinessesByLocation({
      latitude: lat,
      longitude: lon,
      radius,
      limit
    }).then(result => 
      result.businesses.filter(business => business.id !== businessId)
    );
  }

  /**
   * Geocode an address to coordinates
   * This is a placeholder - in production you would integrate with a geocoding service
   */
  static async geocodeAddress(address: string): Promise<{ latitude: number; longitude: number } | null> {
    // This is a placeholder implementation
    // In production, integrate with Google Geocoding API, Mapbox, or similar service
    
    // For now, return null to indicate geocoding is not implemented
    // TODO: Implement actual geocoding service integration
    console.warn('Geocoding service not implemented. Address:', address);
    return null;
  }

  /**
   * Get city/area suggestions based on business locations
   */
  static async getLocationSuggestions(query: string, limit = 5): Promise<string[]> {
    if (!query || query.length < 2) {
      return [];
    }

    const db = await getDatabase();

    const locations = await db
      .select({
        address: businessSchema.businesses.address
      })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`${businessSchema.businesses.address} ILIKE ${`%${query}%`}`
      ))
      .limit(limit * 3); // Get more to filter unique cities

    // Extract unique city names from addresses
    const cities = new Set<string>();
    
    locations.forEach(location => {
      if (location.address) {
        // Simple extraction - in production you might want more sophisticated parsing
        const parts = location.address.split(',');
        if (parts.length >= 2) {
          const city = parts[parts.length - 2]?.trim();
          if (city && city.toLowerCase().includes(query.toLowerCase())) {
            cities.add(city);
          }
        }
      }
    });

    return Array.from(cities).slice(0, limit);
  }

  /**
   * Get business density by area (for heatmap visualization)
   */
  static async getBusinessDensity(bounds: {
    northEast: { latitude: number; longitude: number };
    southWest: { latitude: number; longitude: number };
  }, gridSize = 10): Promise<Array<{
    latitude: number;
    longitude: number;
    count: number;
  }>> {
    const db = await getDatabase();

    // Calculate grid cell size
    const latStep = (bounds.northEast.latitude - bounds.southWest.latitude) / gridSize;
    const lonStep = (bounds.northEast.longitude - bounds.southWest.longitude) / gridSize;

    const businesses = await db
      .select({
        latitude: businessSchema.businesses.latitude,
        longitude: businessSchema.businesses.longitude,
      })
      .from(businessSchema.businesses)
      .where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`${businessSchema.businesses.latitude} IS NOT NULL`,
        sql`${businessSchema.businesses.longitude} IS NOT NULL`,
        sql`CAST(${businessSchema.businesses.latitude} AS DECIMAL) BETWEEN ${bounds.southWest.latitude} AND ${bounds.northEast.latitude}`,
        sql`CAST(${businessSchema.businesses.longitude} AS DECIMAL) BETWEEN ${bounds.southWest.longitude} AND ${bounds.northEast.longitude}`
      ));

    // Group businesses into grid cells
    const grid = new Map<string, number>();

    businesses.forEach(business => {
      if (business.latitude && business.longitude) {
        const lat = parseFloat(business.latitude);
        const lon = parseFloat(business.longitude);
        
        const gridLat = Math.floor((lat - bounds.southWest.latitude) / latStep);
        const gridLon = Math.floor((lon - bounds.southWest.longitude) / lonStep);
        
        const key = `${gridLat},${gridLon}`;
        grid.set(key, (grid.get(key) || 0) + 1);
      }
    });

    // Convert grid to array format
    return Array.from(grid.entries()).map(([key, count]) => {
      const [gridLat, gridLon] = key.split(',').map(Number);
      return {
        latitude: bounds.southWest.latitude + (gridLat + 0.5) * latStep,
        longitude: bounds.southWest.longitude + (gridLon + 0.5) * lonStep,
        count
      };
    });
  }
}