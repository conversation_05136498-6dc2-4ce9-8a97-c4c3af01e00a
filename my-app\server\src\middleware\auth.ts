import { Middle<PERSON><PERSON>and<PERSON> } from 'hono';
import { verifyFirebaseToken } from '../lib/firebase-auth';
import { getDatabase } from '../lib/db';
import { eq } from 'drizzle-orm';
import { User, users, UserWithPermissions } from '../schema/users';
import { getFirebaseProjectId, getDatabaseUrl } from '../lib/env';

declare module 'hono' {
  interface ContextVariableMap {
    user: User;
    userWithPermissions: UserWithPermissions;
  }
}

export const authMiddleware: MiddlewareHandler = async (c, next) => {
  try {
    const authHeader = c.req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Unauthorized' }, 401);
    }

    const token = authHeader.split('Bearer ')[1];
    const firebaseProjectId = getFirebaseProjectId();
    const firebaseUser = await verifyFirebaseToken(token, firebaseProjectId);

    const databaseUrl = getDatabaseUrl();
    const db = await getDatabase(databaseUrl);

    // Determine admin status from Firebase custom claims or database
    const isAdminFromClaims = firebaseUser.customClaims?.admin || false;

    // Upsert: insert if not exists, update admin status if changed
    await db.insert(users)
      .values({
        id: firebaseUser.id,
        email: firebaseUser.email!,
        display_name: null,
        photo_url: null,
        is_admin: isAdminFromClaims,
      })
      .onConflictDoUpdate({
        target: users.id,
        set: {
          is_admin: isAdminFromClaims,
          updated_at: new Date(),
        },
      });

    // Get the user (either just created or already existing)
    const [user] = await db.select()
      .from(users)
      .where(eq(users.id, firebaseUser.id))
      .limit(1);

    if (!user) {
      throw new Error('Failed to create or retrieve user');
    }

    // Create user with permissions
    const userWithPermissions: UserWithPermissions = {
      ...user,
      permissions: user.is_admin ? ['admin.access', 'admin.businesses', 'admin.applications', 'admin.reviews'] : [],
    };

    c.set('user', user);
    c.set('userWithPermissions', userWithPermissions);
    await next();
  } catch (error) {
    console.error('Auth error:', error);
    return c.json({ error: 'Unauthorized' }, 401);
  }
};

// Admin-only middleware - requires user to be authenticated AND have admin privileges
export const adminMiddleware: MiddlewareHandler = async (c, next) => {
  try {
    // First run the regular auth middleware
    await authMiddleware(c, async () => {});

    const userWithPermissions = c.get('userWithPermissions');

    if (!userWithPermissions || !userWithPermissions.is_admin) {
      return c.json({ error: 'Admin access required' }, 403);
    }

    await next();
  } catch (error) {
    console.error('Admin auth error:', error);
    return c.json({ error: 'Admin access required' }, 403);
  }
};

// Permission-based middleware factory
export const requirePermission = (permission: string): MiddlewareHandler => {
  return async (c, next) => {
    try {
      // First run the regular auth middleware
      await authMiddleware(c, async () => {});

      const userWithPermissions = c.get('userWithPermissions');

      if (!userWithPermissions || !userWithPermissions.permissions.includes(permission)) {
        return c.json({ error: `Permission required: ${permission}` }, 403);
      }

      await next();
    } catch (error) {
      console.error('Permission auth error:', error);
      return c.json({ error: 'Insufficient permissions' }, 403);
    }
  };
};