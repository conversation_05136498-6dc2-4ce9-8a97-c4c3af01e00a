/**
 * Theme Utilities
 * 
 * Utility functions and constants for theme management
 */

import { useCallback } from 'react';
import { useTheme } from 'next-themes';

// Theme transition classes for smooth animations
export const themeTransitions = {
  fast: 'transition-colors duration-150 ease-in-out',
  normal: 'transition-colors duration-200 ease-in-out',
  slow: 'transition-colors duration-300 ease-in-out',
  
  // Specific component transitions
  button: 'transition-all duration-200 ease-in-out',
  card: 'transition-[background-color,border-color,box-shadow] duration-200 ease-in-out',
  text: 'transition-colors duration-150 ease-in-out',
  icon: 'transition-transform duration-200 ease-in-out'
} as const;

// Theme color mappings using design system tokens
export const themeColors = {
  light: 'text-foreground bg-background',
  dark: 'text-foreground bg-background', // Auto-switches via CSS variables
  toggle: 'text-muted-foreground hover:text-foreground',
  active: 'text-primary bg-primary/10',
  inactive: 'text-muted-foreground',
  border: 'border-border',
  hover: 'hover:bg-accent hover:text-accent-foreground'
} as const;

// Theme icons and labels
export const themeConfig = {
  light: {
    label: 'Light',
    description: 'Light theme',
    icon: 'Sun'
  },
  dark: {
    label: 'Dark', 
    description: 'Dark theme',
    icon: 'Moon'
  },
  system: {
    label: 'System',
    description: 'Follow system preference',
    icon: 'Monitor'
  }
} as const;

export type ThemeOption = keyof typeof themeConfig;

/**
 * Enhanced theme hook with additional utilities
 */
export function useThemePreference() {
  const { theme, setTheme, systemTheme, resolvedTheme } = useTheme();

  const setThemeWithFeedback = useCallback((newTheme: string) => {
    setTheme(newTheme);
    
    // Optional: Add haptic feedback on mobile
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    // Optional: Track theme preference analytics
    // analytics.track('theme_changed', { theme: newTheme })
  }, [setTheme]);

  const getThemeDisplayName = useCallback(() => {
    if (theme === 'system') {
      return `Auto (${systemTheme === 'dark' ? 'Dark' : 'Light'})`;
    }
    return themeConfig[theme as ThemeOption]?.label || 'Unknown';
  }, [theme, systemTheme]);

  const getNextTheme = useCallback(() => {
    if (theme === 'light') return 'dark';
    if (theme === 'dark') return 'system';
    return 'light';
  }, [theme]);

  const toggleTheme = useCallback(() => {
    setThemeWithFeedback(getNextTheme());
  }, [setThemeWithFeedback, getNextTheme]);

  const isActiveTheme = useCallback((targetTheme: string) => {
    if (targetTheme === 'system') return theme === 'system';
    return resolvedTheme === targetTheme;
  }, [theme, resolvedTheme]);

  return {
    theme,
    setTheme: setThemeWithFeedback,
    systemTheme,
    resolvedTheme,
    displayName: getThemeDisplayName(),
    isSystem: theme === 'system',
    toggleTheme,
    getNextTheme,
    isActiveTheme,
    availableThemes: Object.keys(themeConfig) as ThemeOption[]
  };
}

/**
 * Get theme-aware icon rotation classes
 */
export function getThemeIconClasses(isActive: boolean, theme: 'light' | 'dark') {
  const baseClasses = 'h-4 w-4 transition-all duration-200';
  
  if (theme === 'light') {
    return `${baseClasses} ${isActive ? 'rotate-0 scale-100' : '-rotate-90 scale-0'}`;
  } else {
    return `${baseClasses} ${isActive ? 'rotate-0 scale-100' : 'rotate-90 scale-0'}`;
  }
}

/**
 * Get theme-specific ARIA labels
 */
export function getThemeAriaLabel(currentTheme: string, targetTheme?: string) {
  if (targetTheme) {
    return `Switch to ${themeConfig[targetTheme as ThemeOption]?.label || targetTheme} theme`;
  }
  
  const nextTheme = currentTheme === 'light' ? 'dark' : 'light';
  return `Switch to ${themeConfig[nextTheme as ThemeOption]?.label || nextTheme} theme`;
}

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Get appropriate transition classes based on user preference
 */
export function getTransitionClasses(type: keyof typeof themeTransitions = 'normal') {
  if (prefersReducedMotion()) return '';
  return themeTransitions[type];
}

/**
 * Theme storage utilities
 */
export const themeStorage = {
  key: 'volo-app-theme',
  
  get: (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(themeStorage.key);
  },
  
  set: (theme: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.setItem(themeStorage.key, theme);
  },
  
  remove: (): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(themeStorage.key);
  }
};

/**
 * Detect system theme preference
 */
export function getSystemTheme(): 'light' | 'dark' {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

/**
 * Theme validation utilities
 */
export function isValidTheme(theme: string): theme is ThemeOption {
  return Object.keys(themeConfig).includes(theme);
}

export function sanitizeTheme(theme: string): ThemeOption {
  return isValidTheme(theme) ? theme : 'system';
}
