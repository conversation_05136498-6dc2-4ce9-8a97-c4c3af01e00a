import { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'business.business';
  businessData?: {
    name: string;
    address: string;
    phone?: string;
    website?: string;
    category: string;
    rating?: number;
    reviewCount?: number;
    priceRange?: string;
    hours?: Array<{
      day_of_week: string;
      open_time: string;
      close_time: string;
      is_closed: boolean;
    }>;
  };
}

export function SEOHead({
  title = 'Business Directory - Find Local Businesses',
  description = 'Discover and connect with local businesses in your area. Find reviews, contact information, and services from trusted local providers.',
  keywords = ['business directory', 'local businesses', 'services', 'reviews'],
  image = '/og-image.jpg',
  url = window.location.href,
  type = 'website',
  businessData
}: SEOHeadProps) {
  const siteName = 'Business Directory';
  const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;

  // Generate structured data for business
  const generateBusinessSchema = () => {
    if (!businessData) return null;

    const schema = {
      '@context': 'https://schema.org',
      '@type': 'LocalBusiness',
      name: businessData.name,
      description: description,
      address: {
        '@type': 'PostalAddress',
        streetAddress: businessData.address
      },
      telephone: businessData.phone,
      url: businessData.website,
      category: businessData.category,
      priceRange: businessData.priceRange || '$',
    };

    if (businessData.rating && businessData.reviewCount) {
      schema['aggregateRating'] = {
        '@type': 'AggregateRating',
        ratingValue: businessData.rating,
        reviewCount: businessData.reviewCount,
        bestRating: 5,
        worstRating: 1
      };
    }

    if (businessData.hours && businessData.hours.length > 0) {
      schema['openingHours'] = businessData.hours
        .filter(h => !h.is_closed)
        .map(h => {
          const dayMap = {
            monday: 'Mo',
            tuesday: 'Tu', 
            wednesday: 'We',
            thursday: 'Th',
            friday: 'Fr',
            saturday: 'Sa',
            sunday: 'Su'
          };
          const day = dayMap[h.day_of_week.toLowerCase() as keyof typeof dayMap];
          return `${day} ${h.open_time}-${h.close_time}`;
        });
    }

    return schema;
  };

  const businessSchema = generateBusinessSchema();

  useEffect(() => {
    // Update document title
    document.title = fullTitle;

    // Update meta tags
    const updateMetaTag = (name: string, content: string, property = false) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Basic meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords.join(', '));
    updateMetaTag('robots', 'index, follow');
    updateMetaTag('author', siteName);

    // Open Graph tags
    updateMetaTag('og:title', fullTitle, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', image, true);
    updateMetaTag('og:url', url, true);
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:site_name', siteName, true);

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', fullTitle);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', image);

    // Update canonical link
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', url);

    // Add structured data
    if (businessSchema) {
      let script = document.querySelector('script[data-schema="business"]') as HTMLScriptElement;
      if (!script) {
        script = document.createElement('script');
        script.setAttribute('type', 'application/ld+json');
        script.setAttribute('data-schema', 'business');
        document.head.appendChild(script);
      }
      script.textContent = JSON.stringify(businessSchema);
    }

    // Add breadcrumb schema for business pages
    if (businessData) {
      let breadcrumbScript = document.querySelector('script[data-schema="breadcrumb"]') as HTMLScriptElement;
      if (!breadcrumbScript) {
        breadcrumbScript = document.createElement('script');
        breadcrumbScript.setAttribute('type', 'application/ld+json');
        breadcrumbScript.setAttribute('data-schema', 'breadcrumb');
        document.head.appendChild(breadcrumbScript);
      }
      breadcrumbScript.textContent = JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Home',
            item: window.location.origin
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: 'Categories',
            item: `${window.location.origin}/categories`
          },
          {
            '@type': 'ListItem',
            position: 3,
            name: businessData.category,
            item: `${window.location.origin}/categories/${businessData.category.toLowerCase().replace(/\s+/g, '-')}`
          },
          {
            '@type': 'ListItem',
            position: 4,
            name: businessData.name,
            item: url
          }
        ]
      });
    }
  }, [fullTitle, description, keywords, image, url, type, siteName, businessSchema, businessData]);

  return null;
}
