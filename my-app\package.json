{"name": "volo-app-template", "version": "0.3.0", "description": "Full-stack React + Hono template with Firebase Auth, Neon DB, and Cloudflare deployment", "type": "module", "scripts": {"dev": "node scripts/run-dev.js", "firebase:emulator": "firebase emulators:start --only auth --project demo-project --export-on-exit=./data/firebase-emulator --import=./data/firebase-emulator", "post-setup": "node scripts/post-setup.js", "connect:auth": "npx create-volo-app --connect --auth", "connect:database": "npx create-volo-app --connect --database", "connect:database:neon": "npx create-volo-app --connect --database neon", "connect:database:supabase": "npx create-volo-app --connect --database supabase", "connect:database:custom": "npx create-volo-app --connect --database custom", "connect:deploy": "npx create-volo-app --connect --deploy", "connection:status": "npx create-volo-app --status", "build": "cd ui && pnpm run build", "deploy": "cd server && wrangler deploy && echo 'Frontend deployment: Connect your Git repo to Cloudflare Pages'"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2", "firebase-tools": "^13.0.0", "get-port": "^7.0.0", "postgres": "^3.4.7", "embedded-postgres": "17.5.0-beta.15"}, "keywords": ["react", "hono", "firebase", "neon", "cloudflare", "full-stack", "template"], "template": {"placeholders": {"WORKER_NAME": "string", "FIREBASE_PROJECT_ID": "string", "FIREBASE_API_KEY": "string", "FIREBASE_MESSAGING_SENDER_ID": "string", "FIREBASE_APP_ID": "string", "FIREBASE_MEASUREMENT_ID": "string", "DATABASE_URL": "string"}}, "pnpm": {"onlyBuiltDependencies": ["embedded-postgres", "@embedded-postgres/darwin-arm64", "@embedded-postgres/darwin-x64", "@embedded-postgres/linux-arm64", "@embedded-postgres/linux-x64", "@embedded-postgres/win32-x64"]}}