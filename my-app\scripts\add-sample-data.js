// Simple script to add sample data via API calls
const API_BASE = 'http://localhost:8787/api/v1';

const categories = [
  { name: 'Hotels & Accommodations', slug: 'hotels-accommodations', icon: 'hotel', description: 'Hotels, resorts, inns, and other lodging facilities' },
  { name: 'Restaurants & Dining', slug: 'restaurants-dining', icon: 'utensils', description: 'Restaurants, cafes, bars, and dining establishments' },
  { name: 'Shopping & Retail', slug: 'shopping-retail', icon: 'store', description: 'Supermarkets, department stores, specialty shops, and retail outlets' },
  { name: 'Tours & Activities', slug: 'tours-activities', icon: 'compass', description: 'Tour operators, activity providers, and adventure services' },
  { name: 'Financial Services', slug: 'financial-services', icon: 'briefcase', description: 'Banks, insurance companies, and financial institutions' },
  { name: 'Healthcare & Medical', slug: 'healthcare-medical', icon: 'heart', description: 'Hospitals, clinics, pharmacies, and medical services' },
];

const businesses = [
  {
    name: 'Best Western Plus Belize Biltmore Plaza',
    category: 'Hotels & Accommodations',
    description: 'Premier hotel located on Northern Highway offering luxury accommodations with modern amenities, spa services, and fine dining.',
    address: 'Mile 3, Northern Highway, Belize City, Belize',
    phone: '+************',
    website: 'https://belizebiltmore.com',
    latitude: '17.5162',
    longitude: '-88.1808'
  },
  {
    name: 'Fort George Hotel & Spa',
    category: 'Hotels & Accommodations', 
    description: 'Sophisticated hotel located in the heart of Belize City with a focus on sustainability and elegant accommodations.',
    address: 'Fort George, Belize City, Belize',
    phone: '+************',
    website: 'https://www.fortgeorgebelize.com',
    latitude: '17.4956',
    longitude: '-88.1870'
  },
  {
    name: '501 Restaurant',
    category: 'Restaurants & Dining',
    description: 'Located on the ground floor of The Great House hotel, serving innovative dishes including vegan options.',
    address: 'The Great House, Fort George, Belize City, Belize',
    phone: '+************',
    latitude: '17.4961',
    longitude: '-88.1875'
  },
  {
    name: 'Save-U Supermarket',
    category: 'Shopping & Retail',
    description: 'Modern supermarket offering groceries, liquor, sundries, and household items.',
    address: 'Sancas Plaza, Belize City, Belize',
    phone: '+************',
    latitude: '17.4980',
    longitude: '-88.1820'
  },
  {
    name: 'Brodies Department Store',
    category: 'Shopping & Retail',
    description: 'Modern supermarket, mini-department store and pharmacy. James Brodie & Co. has been serving Belize since 1887.',
    address: 'Goldson Highway, Belize City, Belize',
    phone: '+************',
    website: 'https://www.brodies.bz',
    latitude: '17.5100',
    longitude: '-88.1900'
  },
  {
    name: 'Belize Bank Limited',
    category: 'Financial Services',
    description: 'The largest full-service commercial banking operation in Belize.',
    address: '60 Market Square, Belize City, Belize',
    phone: '+************',
    website: 'https://www.belizebank.com',
    latitude: '17.4945',
    longitude: '-88.1875'
  }
];

console.log('Sample data prepared!');
console.log('Categories:', categories.length);
console.log('Businesses:', businesses.length);
console.log('To use this data, make API calls to your admin endpoints when the server is running.');

// Output the data in a format that can be easily copied
console.log('\n=== CATEGORIES DATA ===');
console.log(JSON.stringify(categories, null, 2));

console.log('\n=== BUSINESSES DATA ===');
console.log(JSON.stringify(businesses, null, 2));