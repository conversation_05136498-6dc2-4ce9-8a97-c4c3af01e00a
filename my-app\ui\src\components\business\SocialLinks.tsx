import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Facebook, Twitter, Instagram, Linkedin, Youtube, ExternalLink } from 'lucide-react';

interface SocialLink {
  platform: string;
  url: string;
}

interface SocialLinksProps {
  socialLinks?: SocialLink[];
  website?: string;
}

const socialIcons: Record<string, React.ComponentType<any>> = {
  facebook: Facebook,
  twitter: Twitter,
  instagram: Instagram,
  linkedin: Linkedin,
  youtube: Youtube,
};

const socialColors: Record<string, string> = {
  facebook: 'hover:text-blue-600',
  twitter: 'hover:text-sky-500',
  instagram: 'hover:text-pink-600',
  linkedin: 'hover:text-blue-700',
  youtube: 'hover:text-red-600',
};

export function SocialLinks({ socialLinks = [], website }: SocialLinksProps) {
  // If no social links or website, don't render the component
  if (!socialLinks.length && !website) {
    return null;
  }

  const handleSocialClick = (url: string) => {
    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = `https://${url}`;
    }
    window.open(formattedUrl, '_blank');
  };

  const handleWebsiteClick = () => {
    if (website) {
      let url = website;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`;
      }
      window.open(url, '_blank');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Connect Online</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Website Link */}
          {website && (
            <Button
              onClick={handleWebsiteClick}
              variant="outline"
              className="w-full justify-start gap-3"
              size="sm"
            >
              <ExternalLink className="w-4 h-4" />
              Visit Website
            </Button>
          )}

          {/* Social Media Links */}
          {socialLinks.length > 0 && (
            <div className="space-y-2">
              {socialLinks.map((link, index) => {
                const IconComponent = socialIcons[link.platform.toLowerCase()] || ExternalLink;
                const colorClass = socialColors[link.platform.toLowerCase()] || 'hover:text-primary';
                
                return (
                  <Button
                    key={index}
                    onClick={() => handleSocialClick(link.url)}
                    variant="ghost"
                    className={`w-full justify-start gap-3 ${colorClass} transition-colors`}
                    size="sm"
                  >
                    <IconComponent className="w-4 h-4" />
                    {link.platform.charAt(0).toUpperCase() + link.platform.slice(1)}
                  </Button>
                );
              })}
            </div>
          )}

          {/* Social Icons Grid (alternative layout for many links) */}
          {socialLinks.length > 4 && (
            <div className="pt-2 border-t">
              <div className="grid grid-cols-4 gap-2">
                {socialLinks.map((link, index) => {
                  const IconComponent = socialIcons[link.platform.toLowerCase()] || ExternalLink;
                  const colorClass = socialColors[link.platform.toLowerCase()] || 'hover:text-primary';
                  
                  return (
                    <Button
                      key={`icon-${index}`}
                      onClick={() => handleSocialClick(link.url)}
                      variant="ghost"
                      size="sm"
                      className={`p-2 h-auto ${colorClass} transition-colors`}
                      title={`Visit our ${link.platform}`}
                    >
                      <IconComponent className="w-5 h-5" />
                    </Button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}