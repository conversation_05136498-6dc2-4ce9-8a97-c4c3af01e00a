# 🎯 **BUSINESS DIRECTORY PLATFORM - IMPLEMENTATION PROMPT**
*Aligned with Volo App Template Stack*

## **PROJECT OVERVIEW**
Create a comprehensive business directory platform with dynamic business listings, interactive mapping, and admin-controlled content management using your existing Volo App Template architecture.

## **CORE FEATURES & REQUIREMENTS**

### **1. Homepage Implementation**
```
REQUIRED COMPONENTS:
- Hero section with prominent search bar
- Category highlights grid (6-8 main categories)
- Featured businesses carousel (3-4 businesses)
- "New Businesses" section (latest 5 additions)
- "Featured Products" carousel (rotating product highlights)
- Dynamic marquee banner (recent additions + special offers)
- Responsive design using your design system tokens
- Follow existing component patterns from ShadCN/UI
```

### **2. Categories System**
```
STRUCTURE:
- Categories page with icon grid layout using design tokens
- Each category: icon, title, description, business count
- Category detail pages with business listings
- Filtering options: location, rating, services, price range
- Sorting: alphabetical, rating, newest, distance
- Map view toggle for geographic visualization
- Use existing UI component patterns
```

### **3. Interactive Mapping (Mapbox GL JS)**
```
IMPLEMENTATION REQUIREMENTS:
- Mapbox GL JS integration: https://docs.mapbox.com/mapbox-gl-js/
- Business markers with custom icons per category
- Popup cards showing: business name, rating, category, quick actions
- Clustering for dense areas
- Search within map bounds
- Filter businesses by category on map
- Mobile-responsive map controls
- Style with your design system CSS custom properties
```

### **4. Business Profile System**
```
DYNAMIC TEMPLATE STRUCTURE:
- Business header: name, logo, hero image, rating
- Contact information: address, phone, email, website, hours
- Services/products section
- Photo gallery
- Customer reviews and ratings
- Location map (embedded Mapbox)
- Social media links
- Call-to-action buttons (call, directions, website)

TEMPLATE REQUIREMENTS:
- Single template for all businesses
- Dynamic field population from Drizzle ORM
- SEO-optimized URLs (/business/[slug])
- Schema markup for local business
- Follow existing component architecture patterns
```

### **5. Database Schema (Neon DB Production)**
```sql
CORE TABLES NEEDED (Drizzle ORM Schema):
- businesses (id, name, slug, category_id, address, coordinates, etc.)
- categories (id, name, slug, icon, description)
- business_images (business_id, image_url, is_primary)
- business_hours (business_id, day, open_time, close_time)
- reviews (business_id, rating, comment, author_name, date)
- business_applications (pending submissions from businesses)
```

### **6. Admin Dashboard**
```
ADMIN CAPABILITIES:
- Business management: add, edit, delete, approve
- Category management
- Review moderation
- Application processing (approve/reject business submissions)
- Analytics dashboard
- Bulk import tools
- Featured business selection
- Use Firebase Auth for admin authentication
```

### **7. Business Application System**
```
WORKFLOW:
1. Business registration form (public-facing)
2. Email notification to admin
3. Admin review interface
4. Approval/rejection with email notifications
5. Automatic business page creation upon approval
6. Template-based page generation
```

## **TECHNICAL SPECIFICATIONS**
*Aligned with Your Volo App Template Stack*

### **Frontend Stack (Your Existing)**
```
USING YOUR STACK:
- React 19 + TypeScript + Vite ✓
- Tailwind CSS + ShadCN/UI ✓
- Your existing design system tokens ✓
- Mapbox GL JS (new integration)
- React Hook Form (business applications)
- Your existing component patterns ✓
```

### **Backend & Database (Your Existing)**
```
USING YOUR STACK:
- Hono API framework ✓
- Drizzle ORM ✓
- Neon PostgreSQL (production) ✓
- Local PostgreSQL (development) ✓
- Firebase Auth (admin authentication) ✓
- Cloudflare Workers deployment ✓
```

### **Key Integrations**
```
REQUIRED:
- Mapbox GL JS for interactive maps
- Email service integration with your existing patterns
- Image upload handling (align with your current approach)
- Search functionality using Drizzle ORM
- Follow your existing API patterns in Hono
```

## **IMPLEMENTATION PHASES**
*Following Your Volo App Template Patterns*

### **Phase 1: Foundation**
- [ ] Extend existing Drizzle schema for business tables
- [ ] Set up Neon DB for production (keep local PostgreSQL for dev)
- [ ] Create business API routes in your Hono server
- [ ] Extend Firebase Auth for admin roles

### **Phase 2: Core Pages**
- [ ] Homepage components using your ShadCN/UI patterns
- [ ] Categories page following your existing routing
- [ ] Business profile template with your design tokens
- [ ] Search functionality using Drizzle ORM patterns

### **Phase 3: Interactive Features**
- [ ] Mapbox integration as new React component
- [ ] Advanced filtering using your existing patterns
- [ ] Business application form with React Hook Form
- [ ] Admin dashboard extending your auth patterns

### **Phase 4: Enhancement**
- [ ] Review system with Drizzle schema
- [ ] Advanced search with your existing API patterns
- [ ] Performance optimization following your guidelines
- [ ] SEO implementation
- [ ] Mobile optimization using your responsive design

## **CRITICAL IMPLEMENTATION NOTES**
*Aligned with Your Architecture*

### **Dynamic Business Pages (React Router)**
```javascript
// Follow your existing routing patterns in App.tsx
// Add business routes to your React Router setup

// In your pages/ directory
export default function BusinessPage() {
  const { slug } = useParams();
  // Use your existing serverComm.ts patterns for API calls
  // Follow your component structure patterns
}
```

### **Admin Business Management**
```
REQUIREMENTS:
- Extend your Firebase Auth context for admin roles
- Use your existing form patterns with ShadCN/UI
- Follow your Drizzle ORM patterns for CRUD operations
- Use your design system tokens for styling
- Integrate with your Hono API routes
```

### **Mapbox Integration**
```javascript
// Create new component following your patterns
// components/map/InteractiveMap.tsx
// Use your CSS custom properties for styling
// Follow your TypeScript patterns for props
// Integrate with your existing API communication patterns
```

### **Database Integration**
```typescript
// Extend your existing schema/ directory
// Follow your Drizzle ORM patterns
// Use your existing database connection patterns
// Maintain your migration approach
```

## **SUCCESS CRITERIA**
- [ ] All homepage sections functional using your design system
- [ ] Categories system works with your existing patterns
- [ ] Interactive map integrates seamlessly with your stack
- [ ] Business pages follow your component architecture
- [ ] Admin functionality extends your Firebase Auth
- [ ] Business application workflow uses your API patterns
- [ ] Mobile-responsive using your design tokens
- [ ] Performance meets your existing standards

## **DRIZZLE ORM SCHEMA IMPLEMENTATION**
*Following Your Existing Patterns*

### **Extend Your Schema Directory**
```typescript
// server/src/schema/business.ts
// Follow your existing Drizzle patterns

import { pgTable, serial, varchar, text, decimal, boolean, timestamp, integer, time } from 'drizzle-orm/pg-core';

export const categories = pgTable('categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  slug: varchar('slug', { length: 100 }).notNull().unique(),
  icon: varchar('icon', { length: 255 }),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow(),
});

export const businesses = pgTable('businesses', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  categoryId: integer('category_id').references(() => categories.id),
  description: text('description'),
  address: text('address').notNull(),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  phone: varchar('phone', { length: 20 }),
  email: varchar('email', { length: 255 }),
  website: varchar('website', { length: 255 }),
  logoUrl: varchar('logo_url', { length: 255 }),
  heroImageUrl: varchar('hero_image_url', { length: 255 }),
  isFeatured: boolean('is_featured').default(false),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const businessHours = pgTable('business_hours', {
  id: serial('id').primaryKey(),
  businessId: integer('business_id').references(() => businesses.id, { onDelete: 'cascade' }),
  dayOfWeek: integer('day_of_week').notNull(), // 0=Sunday, 1=Monday, etc.
  openTime: time('open_time'),
  closeTime: time('close_time'),
  isClosed: boolean('is_closed').default(false),
});

export const businessImages = pgTable('business_images', {
  id: serial('id').primaryKey(),
  businessId: integer('business_id').references(() => businesses.id, { onDelete: 'cascade' }),
  imageUrl: varchar('image_url', { length: 255 }).notNull(),
  altText: varchar('alt_text', { length: 255 }),
  isPrimary: boolean('is_primary').default(false),
  displayOrder: integer('display_order').default(0),
});

export const reviews = pgTable('reviews', {
  id: serial('id').primaryKey(),
  businessId: integer('business_id').references(() => businesses.id, { onDelete: 'cascade' }),
  authorName: varchar('author_name', { length: 100 }).notNull(),
  authorEmail: varchar('author_email', { length: 255 }),
  rating: integer('rating'), // Add check constraint in migration
  comment: text('comment'),
  isApproved: boolean('is_approved').default(false),
  createdAt: timestamp('created_at').defaultNow(),
});

export const businessApplications = pgTable('business_applications', {
  id: serial('id').primaryKey(),
  businessName: varchar('business_name', { length: 255 }).notNull(),
  contactName: varchar('contact_name', { length: 255 }).notNull(),
  contactEmail: varchar('contact_email', { length: 255 }).notNull(),
  contactPhone: varchar('contact_phone', { length: 20 }),
  categoryId: integer('category_id').references(() => categories.id),
  address: text('address').notNull(),
  description: text('description'),
  website: varchar('website', { length: 255 }),
  status: varchar('status', { length: 20 }).default('pending'),
  adminNotes: text('admin_notes'),
  submittedAt: timestamp('submitted_at').defaultNow(),
  reviewedAt: timestamp('reviewed_at'),
});

// Types for TypeScript (follow your existing patterns)
export type Category = typeof categories.$inferSelect;
export type NewCategory = typeof categories.$inferInsert;
export type Business = typeof businesses.$inferSelect;
export type NewBusiness = typeof businesses.$inferInsert;
export type BusinessApplication = typeof businessApplications.$inferSelect;
export type NewBusinessApplication = typeof businessApplications.$inferInsert;
```

## **HONO API ROUTES STRUCTURE**
*Following Your Existing API Patterns*

### **Extend Your api.ts File**
```typescript
// server/src/api.ts
// Add these routes to your existing Hono app

// Public business routes
app.get('/api/businesses', async (c) => {
  // Use your existing Drizzle patterns
  // Follow your existing error handling
  // Apply your existing CORS middleware
});

app.get('/api/businesses/:slug', async (c) => {
  // Single business details
  // Use your existing parameter validation
});

app.get('/api/categories', async (c) => {
  // All categories with business counts
});

app.get('/api/categories/:slug', async (c) => {
  // Businesses in specific category
});

app.get('/api/search', async (c) => {
  // Search businesses with query parameters
});

app.post('/api/applications', async (c) => {
  // Submit business application
  // Use your existing validation patterns
});

app.post('/api/reviews', async (c) => {
  // Submit business review
});

// Admin routes (use your existing authMiddleware)
app.use('/api/admin/*', authMiddleware); // Your existing auth middleware

app.get('/api/admin/businesses', async (c) => {
  // Admin view of all businesses
});

app.post('/api/admin/businesses', async (c) => {
  // Create new business
});

app.put('/api/admin/businesses/:id', async (c) => {
  // Update business
});

app.delete('/api/admin/businesses/:id', async (c) => {
  // Delete business
});

app.get('/api/admin/applications', async (c) => {
  // Get pending applications
});

app.put('/api/admin/applications/:id', async (c) => {
  // Approve/reject application
});
```

## **COMPONENT ARCHITECTURE**
*Following Your Existing ShadCN/UI Patterns*

### **Extend Your UI Components**
```
ui/src/components/
├── homepage/
│   ├── HeroSection.tsx          # Use your design tokens
│   ├── SearchBar.tsx            # Follow your form patterns
│   ├── CategoryHighlights.tsx   # Use your card components
│   ├── FeaturedBusinesses.tsx   # Follow your carousel patterns
│   ├── NewBusinesses.tsx        # Use your existing grid layouts
│   ├── FeaturedProducts.tsx     # Follow your component structure
│   └── MarqueeBanner.tsx        # Use your animation patterns
├── business/
│   ├── BusinessCard.tsx         # Extend your existing card patterns
│   ├── BusinessProfile.tsx      # Use your layout components
│   ├── BusinessHeader.tsx       # Follow your header patterns
│   ├── ContactInfo.tsx          # Use your info display patterns
│   ├── BusinessHours.tsx        # Follow your data display patterns
│   ├── PhotoGallery.tsx         # Use your image components
│   ├── ReviewsSection.tsx       # Follow your content patterns
│   └── BusinessMap.tsx          # New Mapbox integration
├── map/
│   ├── InteractiveMap.tsx       # New component following your patterns
│   ├── BusinessMarker.tsx       # Custom Mapbox markers
│   ├── MarkerPopup.tsx          # Popup cards with your styling
│   ├── MapControls.tsx          # Controls using your button components
│   └── MapFilters.tsx           # Filters using your form components
└── admin/
    ├── BusinessForm.tsx         # Use your form patterns
    ├── ApplicationReview.tsx    # Follow your review patterns
    └── AdminDashboard.tsx       # Use your dashboard layouts
```

### **Integration with Your Existing Patterns**
```typescript
// Follow your existing component structure
// Use your CSS custom properties from design system
// Integrate with your serverComm.ts for API calls
// Use your existing auth context for admin features
// Follow your TypeScript interface patterns
```

## **NEXT STEPS**
*Aligned with Your Development Workflow*

1. **Extend Database Schema**: Add business tables to your existing Drizzle schema
2. **Update Neon DB**: Configure production database (keep local PostgreSQL for dev)
3. **Extend Hono API**: Add business routes to your existing api.ts
4. **Create Components**: Build business components using your ShadCN/UI patterns
5. **Integrate Mapbox**: Add mapping as new component following your architecture
6. **Extend Admin Auth**: Use your Firebase Auth patterns for admin features
7. **Follow Your Deployment**: Use your existing Cloudflare Workers/Pages setup

## **ALIGNMENT CHECKLIST**
- ✅ **Uses React 19 + Vite** (not Next.js)
- ✅ **Extends Hono API** (not Next.js API routes)
- ✅ **Uses Drizzle ORM** (not Prisma)
- ✅ **Uses Firebase Auth** (not NextAuth.js)
- ✅ **Neon DB for production** ✓
- ✅ **Follows your design system** tokens
- ✅ **Uses your component patterns**
- ✅ **Integrates with your existing architecture**

---

**Agent Used**: `@agents-agument/core/prompt-assistant.md` - Prompt Assistant

This corrected implementation prompt now properly aligns with your Volo App Template stack while maintaining the comprehensive business directory requirements and keeping Neon DB for production as requested.
