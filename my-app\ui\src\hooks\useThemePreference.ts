/**
 * Enhanced Theme Preference Hook
 * 
 * Provides advanced theme management with additional features
 */

import { useState, useEffect, useCallback } from 'react';
import { useTheme } from 'next-themes';
import { themeConfig, type ThemeOption } from '@/lib/theme-utils';

interface ThemePreferences {
  autoSwitch: boolean;
  highContrast: boolean;
  reducedMotion: boolean;
  syncAcrossDevices: boolean;
}

interface UseThemePreferenceReturn {
  theme: string | undefined;
  setTheme: (theme: string) => void;
  systemTheme: string | undefined;
  resolvedTheme: string | undefined;
  isLoading: boolean;
  displayName: string;
  isSystem: boolean;
  preferences: ThemePreferences;
  updatePreferences: (prefs: Partial<ThemePreferences>) => void;
  availableThemes: ThemeOption[];
  toggleTheme: () => void;
  isActiveTheme: (targetTheme: string) => boolean;
  getThemeIcon: (themeName: string) => string;
  mounted: boolean;
}

const defaultPreferences: ThemePreferences = {
  autoSwitch: false,
  highContrast: false,
  reducedMotion: false,
  syncAcrossDevices: true
};

const PREFERENCES_KEY = 'volo-theme-preferences';

export function useThemePreference(): UseThemePreferenceReturn {
  const { theme, setTheme, systemTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [preferences, setPreferences] = useState<ThemePreferences>(defaultPreferences);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
    setIsLoading(false);
    
    // Load preferences from localStorage
    try {
      const savedPrefs = localStorage.getItem(PREFERENCES_KEY);
      if (savedPrefs) {
        const parsed = JSON.parse(savedPrefs);
        setPreferences({ ...defaultPreferences, ...parsed });
      }
    } catch (error) {
      console.warn('Failed to load theme preferences:', error);
    }
  }, []);

  // Save preferences to localStorage
  const updatePreferences = useCallback((newPrefs: Partial<ThemePreferences>) => {
    const updatedPrefs = { ...preferences, ...newPrefs };
    setPreferences(updatedPrefs);
    
    try {
      localStorage.setItem(PREFERENCES_KEY, JSON.stringify(updatedPrefs));
    } catch (error) {
      console.warn('Failed to save theme preferences:', error);
    }
  }, [preferences]);

  // Enhanced theme setter with analytics and feedback
  const setThemeWithEnhancements = useCallback((newTheme: string) => {
    setTheme(newTheme);
    
    // Haptic feedback on mobile devices
    if ('vibrate' in navigator && !preferences.reducedMotion) {
      navigator.vibrate(50);
    }
    
    // Optional: Track theme changes for analytics
    if (typeof window !== 'undefined' && 'gtag' in window) {
      // @ts-ignore
      window.gtag('event', 'theme_change', {
        theme: newTheme,
        previous_theme: theme
      });
    }
  }, [setTheme, theme, preferences.reducedMotion]);

  // Get display name for current theme
  const getDisplayName = useCallback(() => {
    if (!mounted) return 'Loading...';
    
    if (theme === 'system') {
      const systemLabel = systemTheme === 'dark' ? 'Dark' : 'Light';
      return `Auto (${systemLabel})`;
    }
    
    return themeConfig[theme as ThemeOption]?.label || 'Unknown';
  }, [theme, systemTheme, mounted]);

  // Toggle between themes in sequence: light -> dark -> system -> light
  const toggleTheme = useCallback(() => {
    if (!mounted) return;
    
    let nextTheme: string;
    switch (theme) {
      case 'light':
        nextTheme = 'dark';
        break;
      case 'dark':
        nextTheme = 'system';
        break;
      case 'system':
      default:
        nextTheme = 'light';
        break;
    }
    
    setThemeWithEnhancements(nextTheme);
  }, [theme, mounted, setThemeWithEnhancements]);

  // Check if a theme is currently active
  const isActiveTheme = useCallback((targetTheme: string) => {
    if (!mounted) return false;
    
    if (targetTheme === 'system') {
      return theme === 'system';
    }
    
    return resolvedTheme === targetTheme;
  }, [theme, resolvedTheme, mounted]);

  // Get icon name for a theme
  const getThemeIcon = useCallback((themeName: string) => {
    return themeConfig[themeName as ThemeOption]?.icon || 'Monitor';
  }, []);

  // Auto-switch based on time (if enabled)
  useEffect(() => {
    if (!preferences.autoSwitch || !mounted) return;
    
    const checkTimeBasedTheme = () => {
      const hour = new Date().getHours();
      const isDayTime = hour >= 6 && hour < 18;
      const preferredTheme = isDayTime ? 'light' : 'dark';
      
      if (theme !== 'system' && resolvedTheme !== preferredTheme) {
        setThemeWithEnhancements(preferredTheme);
      }
    };
    
    // Check immediately
    checkTimeBasedTheme();
    
    // Check every hour
    const interval = setInterval(checkTimeBasedTheme, 60 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [preferences.autoSwitch, theme, resolvedTheme, mounted, setThemeWithEnhancements]);

  // Listen for system theme changes
  useEffect(() => {
    if (!mounted) return;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      // Only react if we're in system mode
      if (theme === 'system') {
        // Force a re-render to update display name
        setIsLoading(false);
      }
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme, mounted]);

  // Apply reduced motion preference
  useEffect(() => {
    if (!mounted) return;
    
    const root = document.documentElement;
    if (preferences.reducedMotion) {
      root.style.setProperty('--transition-duration', '0ms');
    } else {
      root.style.removeProperty('--transition-duration');
    }
  }, [preferences.reducedMotion, mounted]);

  return {
    theme,
    setTheme: setThemeWithEnhancements,
    systemTheme,
    resolvedTheme,
    isLoading,
    displayName: getDisplayName(),
    isSystem: theme === 'system',
    preferences,
    updatePreferences,
    availableThemes: Object.keys(themeConfig) as ThemeOption[],
    toggleTheme,
    isActiveTheme,
    getThemeIcon,
    mounted
  };
}
