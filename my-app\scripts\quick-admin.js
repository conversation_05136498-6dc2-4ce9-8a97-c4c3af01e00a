const { readFileSync, writeFileSync, existsSync } = require('fs');
const { join } = require('path');

// Find the latest Firebase export directory
function findLatestFirebaseExport() {
  const dataDir = join(__dirname, '..', 'data', 'firebase-emulator');
  return dataDir;
}

// Make user admin
function makeUserAdmin(email) {
  try {
    const exportDir = findLatestFirebaseExport();
    const accountsPath = join(exportDir, 'auth_export', 'accounts.json');
    
    if (!existsSync(accountsPath)) {
      console.log('❌ No Firebase accounts found. Please sign in first at http://localhost:5601/admin');
      return;
    }
    
    // Read current accounts
    const accountsData = JSON.parse(readFileSync(accountsPath, 'utf8'));
    
    // Find user by email
    const user = accountsData.users.find(u => u.email === email);
    
    if (!user) {
      console.log(`❌ User with email ${email} not found.`);
      console.log('Available users:');
      accountsData.users.forEach(u => console.log(`  - ${u.email}`));
      console.log('\nPlease sign in first at http://localhost:5601/admin');
      return;
    }
    
    console.log(`✅ Found user: ${user.email}`);
    
    // Add custom claims for admin
    if (!user.customClaims) {
      user.customClaims = {};
    }
    
    user.customClaims.admin = true;
    user.customClaims.roles = ['admin'];
    user.customClaims.permissions = ['admin.access', 'admin.businesses', 'admin.applications', 'admin.reviews'];
    
    // Write back to file
    writeFileSync(accountsPath, JSON.stringify(accountsData, null, 2));
    
    console.log(`🎉 Successfully made ${email} an admin!`);
    console.log('🔄 Please refresh your browser to get admin access.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Get email from command line or use default
const email = process.argv[2] || '<EMAIL>';
console.log(`Making ${email} an admin...`);
makeUserAdmin(email);
