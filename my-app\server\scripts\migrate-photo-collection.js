/**
 * Migration script to add photo collection fields to business_images table
 * Run this script to update your database schema
 */

import 'dotenv/config';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { getDatabase } from '../src/lib/db.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runMigration() {
  try {
    console.log('🔄 Starting photo collection migration...');
    
    const db = await getDatabase();
    
    // Read the migration SQL file
    const migrationPath = join(__dirname, '../src/migrations/add-photo-collection-fields.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');
    
    // Split by semicolon and execute each statement
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        await db.execute(statement);
      }
    }
    
    console.log('✅ Photo collection migration completed successfully!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Add your Google Places API key to .env file:');
    console.log('   GOOGLE_PLACES_API_KEY=your_api_key_here');
    console.log('2. Restart your server to load the new environment variable');
    console.log('3. Create a new business to test automatic photo collection');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
