import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Star, MapPin, Share2, Heart } from 'lucide-react';
import { Link } from 'react-router-dom';

interface BusinessHeaderProps {
  business: {
    id: number;
    name: string;
    short_description: string;
    address: string;
    hero_image_url: string;
    logo_url: string;
    is_featured: boolean;
    average_rating: string;
    total_reviews: number;
    category: {
      name: string;
      slug: string;
    };
  };
}

export function BusinessHeader({ business }: BusinessHeaderProps) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: business.name,
          text: business.short_description,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  return (
    <div className="relative">
      {/* Hero Image */}
      <div className="h-64 md:h-80 lg:h-96 relative overflow-hidden">
        {business.hero_image_url ? (
          <img
            src={business.hero_image_url}
            alt={business.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
            <span className="text-6xl font-bold text-muted-foreground">
              {business.name.charAt(0)}
            </span>
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20"></div>
        
        {/* Featured Badge */}
        {business.is_featured && (
          <Badge className="absolute top-6 left-6 bg-primary text-primary-foreground">
            Featured Business
          </Badge>
        )}
      </div>
      
      {/* Business Info Overlay */}
      <div className="container mx-auto px-6">
        <div className="relative -mt-16 md:-mt-20">
          <div className="bg-background rounded-lg shadow-lg p-6 md:p-8">
            <div className="flex flex-col md:flex-row md:items-start gap-6">
              {/* Logo */}
              {business.logo_url && (
                <div className="w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden border-4 border-background shadow-lg flex-shrink-0">
                  <img
                    src={business.logo_url}
                    alt={`${business.name} logo`}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              
              {/* Business Details */}
              <div className="flex-1 min-w-0">
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                  <div className="space-y-3">
                    <div>
                      <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">
                        {business.name}
                      </h1>
                      <Link 
                        to={`/categories/${business.category.slug}`}
                        className="text-primary hover:text-primary/80 font-medium"
                      >
                        {business.category.name}
                      </Link>
                    </div>
                    
                    {/* Rating */}
                    {business.total_reviews > 0 && (
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star
                              key={i}
                              className={`w-5 h-5 ${
                                i < Math.floor(parseFloat(business.average_rating))
                                  ? 'fill-yellow-400 text-yellow-400'
                                  : 'text-muted-foreground'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="font-semibold">
                          {parseFloat(business.average_rating).toFixed(1)}
                        </span>
                        <span className="text-muted-foreground">
                          ({business.total_reviews} {business.total_reviews === 1 ? 'review' : 'reviews'})
                        </span>
                      </div>
                    )}
                    
                    {/* Address */}
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="w-4 h-4 flex-shrink-0" />
                      <span>{business.address}</span>
                    </div>
                    
                    {/* Description */}
                    {business.short_description && (
                      <p className="text-muted-foreground">
                        {business.short_description}
                      </p>
                    )}
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex flex-row lg:flex-col gap-3">
                    <Button onClick={handleShare} variant="outline" size="sm">
                      <Share2 className="w-4 h-4 mr-2" />
                      Share
                    </Button>
                    <Button variant="outline" size="sm">
                      <Heart className="w-4 h-4 mr-2" />
                      Save
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
