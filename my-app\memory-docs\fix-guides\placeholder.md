# Fix Guides Directory

This directory contains post-mortem documentation for resolved issues. Each file follows the naming convention: `YYYY-MM-DD-issue-summary.md`

## 📋 Template for New Fix Guides

When creating a new fix guide, use this template:

```markdown
# [Issue Title] - Post-Mortem

**Date**: YYYY-MM-DD  
**Severity**: Critical/High/Medium/Low  
**Duration**: X hours/minutes  
**Affected Systems**: [List components]  

## 🚨 Problem Summary
Brief description of what went wrong.

## 🔍 Root Cause Analysis
Detailed explanation of why the issue occurred.

## 🛠️ Resolution Steps
1. Step-by-step actions taken to fix the issue
2. Include commands, code changes, configuration updates
3. Note any temporary workarounds used

## 🔒 Preventative Measures
- Changes made to prevent recurrence
- Monitoring improvements
- Process updates
- Documentation updates

## 📊 Impact Assessment
- Users affected
- Data integrity impact
- Performance impact
- Business impact

## 📚 Lessons Learned
Key takeaways for future reference.

## 🔗 Related Issues
Links to similar problems or related tickets.
```

## 🎯 Purpose

Fix guides serve multiple purposes:
- **Knowledge preservation**: Capture institutional memory
- **Faster resolution**: Reference for similar future issues
- **Process improvement**: Identify patterns and systemic problems
- **Team learning**: Share debugging techniques and solutions

## 📁 Organization

Files should be organized chronologically and include:
- **Date prefix**: For easy sorting and timeline tracking
- **Descriptive names**: Clear indication of the problem area
- **Consistent format**: Use the template above for all entries

## 🔄 Maintenance

- **Archive old guides**: Move outdated fixes to archive folder annually
- **Update references**: Keep links and commands current
- **Cross-reference**: Link related issues and solutions
- **Review regularly**: Ensure preventative measures are still effective

---

**Created**: 2025-01-08  
**Purpose**: Template and guidelines for issue post-mortems  
**Maintainer**: Documentation Specialist Agent
