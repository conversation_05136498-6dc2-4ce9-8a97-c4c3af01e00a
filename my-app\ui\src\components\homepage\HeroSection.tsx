import { useState } from 'react';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useNavigate } from 'react-router-dom';

interface HeroSectionProps {
  onSearch?: (query: string, location?: string) => void;
}

export function HeroSection({ onSearch }: HeroSectionProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      if (onSearch) {
        onSearch(searchQuery);
      } else {
        // Navigate to search page with query parameters
        const params = new URLSearchParams();
        params.set('q', searchQuery);
        navigate(`/search?${params.toString()}`);
      }
    }
  };

  return (
    <section className="@container">
      <div className="@[480px]:p-4">
        <div
          className="flex min-h-[520px] flex-col gap-8 bg-cover bg-center bg-no-repeat @[480px]:gap-10 @[480px]:rounded-xl items-center justify-center p-6 relative overflow-hidden"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.6) 100%), url("https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1400&h=900&fit=crop&crop=center")`
          }}
        >
          {/* Background overlay for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/40 to-black/60" />

          <div className="flex flex-col gap-4 text-center relative z-10 max-w-4xl mx-auto">
            <h1 className="text-white text-4xl font-bold leading-tight tracking-tight @[480px]:text-6xl @[480px]:font-bold @[480px]:leading-tight drop-shadow-lg">
              Explore Local Businesses Worldwide
            </h1>
            <h2 className="text-white/90 text-lg font-normal leading-relaxed @[480px]:text-xl @[480px]:font-normal @[480px]:leading-relaxed max-w-2xl mx-auto drop-shadow-md">
              Discover top-rated services and businesses in any location. Use the interactive globe to find businesses near you or anywhere in the world.
            </h2>
          </div>

          <form onSubmit={handleSearch} className="w-full max-w-2xl relative z-10">
            <div className="flex items-center bg-white/95 backdrop-blur-sm rounded-full shadow-2xl border border-white/20 overflow-hidden h-14 @[480px]:h-16">
              <div className="flex items-center justify-center pl-6 pr-4">
                <Search className="h-5 w-5 text-gray-500" />
              </div>
              <Input
                placeholder="Search for businesses or services"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 border-0 bg-transparent text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-0 text-base @[480px]:text-lg font-medium px-0"
              />
              <div className="pr-2">
                <Button
                  type="submit"
                  className="h-10 px-6 @[480px]:h-12 @[480px]:px-8 bg-primary hover:bg-primary/90 text-white font-semibold rounded-full shadow-lg transition-all duration-200 hover:shadow-xl"
                >
                  <span className="truncate">Search</span>
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}
