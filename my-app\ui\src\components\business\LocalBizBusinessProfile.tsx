import { LocalBizHeader } from './LocalBizHeader';
import { LocalBizBusinessHero } from './LocalBizBusinessHero';
import { LocalBizContent } from './LocalBizContent';

interface BusinessData {
  id: number;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  address: string;
  latitude?: string;
  longitude?: string;
  phone?: string;
  email?: string;
  website?: string;
  logo_url?: string;
  hero_image_url?: string;
  is_featured: boolean;
  is_active: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: string;
  category?: {
    id: number;
    name: string;
    slug: string;
  };
  hours?: Array<{
    id: number;
    day_of_week: string;
    open_time: string;
    close_time: string;
    is_closed: boolean;
  }>;
  images?: Array<{
    id: number;
    image_url: string;
    alt_text?: string;
    is_primary: boolean;
    display_order: number;
  }>;
  reviews?: Array<{
    id: number;
    rating: number;
    comment: string;
    author_name: string;
    created_at: string;
    is_verified: boolean;
  }>;
}

interface LocalBizBusinessProfileProps {
  business: BusinessData;
}

export function LocalBizBusinessProfile({ business }: LocalBizBusinessProfileProps) {
  return (
    <div 
      className="min-h-screen bg-background" 
      style={{ fontFamily: '"Space Grotesk", "Noto Sans", sans-serif' }}
    >
      {/* LocalBiz Header */}
      <LocalBizHeader />
      
      {/* Business Hero Section */}
      <LocalBizBusinessHero business={business} />
      
      {/* Business Content */}
      <LocalBizContent business={business} />
    </div>
  );
}
