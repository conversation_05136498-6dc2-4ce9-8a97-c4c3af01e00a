/**
 * Theme Settings Component
 * 
 * Advanced theme configuration panel with preferences and customization options
 */

import * as React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Sun, 
  Moon, 
  Monitor, 
  Palette, 
  Settings, 
  Zap, 
  Eye, 
  Clock,
  Smartphone,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useThemePreference } from '@/hooks/useThemePreference';
import { ThemeSelector } from './ThemeSelector';
import { getTransitionClasses } from '@/lib/theme-utils';

interface ThemeSettingsProps {
  className?: string;
  showAdvanced?: boolean;
}

export function ThemeSettings({ 
  className, 
  showAdvanced = true 
}: ThemeSettingsProps) {
  const { 
    theme, 
    resolvedTheme, 
    systemTheme, 
    displayName, 
    preferences, 
    updatePreferences,
    mounted 
  } = useThemePreference();

  // Keyboard shortcut handler
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + T to toggle theme
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        // Toggle between light and dark (skip system for quick toggle)
        const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';
        // Note: We'd need to access setTheme here, but keeping it simple for now
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [resolvedTheme]);

  if (!mounted) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="h4">Theme Settings</CardTitle>
          <CardDescription>Loading theme preferences...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="h-4 bg-muted rounded w-24" />
                  <div className="h-3 bg-muted rounded w-32" />
                </div>
                <div className="h-6 w-11 bg-muted rounded-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getThemeIcon = () => {
    if (theme === 'system') return <Monitor className="h-4 w-4" />;
    return resolvedTheme === 'dark' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />;
  };

  return (
    <Card className={cn(getTransitionClasses('card'), className)}>
      <CardHeader>
        <CardTitle className="h4 flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Theme Settings
        </CardTitle>
        <CardDescription>
          Customize your theme preferences and appearance settings
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current Theme Status */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-3">
            <span className={getTransitionClasses('icon')}>
              {getThemeIcon()}
            </span>
            <div>
              <span className="label font-medium">Current Theme</span>
              <p className="caption">
                {displayName}
                {theme === 'system' && (
                  <Badge variant="outline" className="ml-2 text-xs">
                    Auto
                  </Badge>
                )}
              </p>
            </div>
          </div>
          <Badge variant={resolvedTheme === 'dark' ? 'default' : 'secondary'}>
            {resolvedTheme === 'dark' ? 'Dark' : 'Light'}
          </Badge>
        </div>

        {/* Theme Selection */}
        <div className="space-y-3">
          <Label className="h5">Theme Selection</Label>
          <ThemeSelector 
            variant="cards" 
            showDescriptions={true}
            className="grid-cols-1 sm:grid-cols-3"
          />
        </div>

        <Separator />

        {/* Theme Preferences */}
        <div className="space-y-4">
          <Label className="h5">Preferences</Label>
          
          <div className="space-y-4">
            {/* Auto Switch */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="label flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Auto Switch
                </Label>
                <p className="caption">
                  Automatically switch between light and dark based on time of day
                </p>
              </div>
              <Switch
                checked={preferences.autoSwitch}
                onCheckedChange={(checked) => 
                  updatePreferences({ autoSwitch: checked })
                }
                aria-label="Toggle auto switch"
              />
            </div>

            {/* Reduced Motion */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="label flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Reduce Motion
                </Label>
                <p className="caption">
                  Minimize animations and transitions for better accessibility
                </p>
              </div>
              <Switch
                checked={preferences.reducedMotion}
                onCheckedChange={(checked) => 
                  updatePreferences({ reducedMotion: checked })
                }
                aria-label="Toggle reduced motion"
              />
            </div>

            {/* High Contrast */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="label flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  High Contrast
                </Label>
                <p className="caption">
                  Increase contrast for better visibility
                </p>
              </div>
              <Switch
                checked={preferences.highContrast}
                onCheckedChange={(checked) => 
                  updatePreferences({ highContrast: checked })
                }
                aria-label="Toggle high contrast"
              />
            </div>

            {/* Sync Across Devices */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="label flex items-center gap-2">
                  <Smartphone className="h-4 w-4" />
                  Sync Across Devices
                </Label>
                <p className="caption">
                  Keep theme preferences synchronized across all your devices
                </p>
              </div>
              <Switch
                checked={preferences.syncAcrossDevices}
                onCheckedChange={(checked) => 
                  updatePreferences({ syncAcrossDevices: checked })
                }
                aria-label="Toggle device sync"
              />
            </div>
          </div>
        </div>

        {showAdvanced && (
          <>
            <Separator />

            {/* Advanced Settings */}
            <div className="space-y-4">
              <Label className="h5 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Advanced
              </Label>
              
              <div className="space-y-3">
                {/* System Theme Info */}
                <div className="p-3 border rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="label">System Theme</span>
                    <Badge variant="outline">
                      {systemTheme === 'dark' ? 'Dark' : 'Light'}
                    </Badge>
                  </div>
                  <p className="caption">
                    Your operating system is currently using {systemTheme} mode
                  </p>
                </div>

                {/* Keyboard Shortcuts */}
                <div className="p-3 border rounded-lg space-y-2">
                  <span className="label">Keyboard Shortcuts</span>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="caption">Toggle Theme</span>
                      <Badge variant="outline" className="font-mono text-xs">
                        Ctrl + Shift + T
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Reset Settings */}
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => {
                    updatePreferences({
                      autoSwitch: false,
                      highContrast: false,
                      reducedMotion: false,
                      syncAcrossDevices: true
                    });
                  }}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset to Defaults
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Compact Theme Settings
 * 
 * A simplified version for smaller spaces
 */
export function ThemeSettingsCompact({ className }: { className?: string }) {
  return (
    <ThemeSettings 
      className={className} 
      showAdvanced={false} 
    />
  );
}
