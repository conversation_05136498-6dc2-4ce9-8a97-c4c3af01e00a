// Public search routes
import { Hono } from 'hono';
import { eq, and, desc, count, sql } from 'drizzle-orm';
import { getDatabase } from '../lib/db';
import * as businessSchema from '../schema/business';
import { LocationService } from '../services/location-service';

const publicSearchRoutes = new Hono();

// Advanced business search
publicSearchRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const q = c.req.query('q'); // search query
    const category = c.req.query('category');
    const location = c.req.query('location');
    const rating = c.req.query('rating') ? parseFloat(c.req.query('rating')!) : null;
    const featured = c.req.query('featured') === 'true';
    const sortBy = c.req.query('sortBy') || 'relevance'; // relevance, rating, newest, name
    const offset = (page - 1) * limit;
    const db = await getDatabase();

    let query = db
      .select({
        id: businessSchema.businesses.id,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        short_description: businessSchema.businesses.short_description,
        address: businessSchema.businesses.address,
        phone: businessSchema.businesses.phone,
        email: businessSchema.businesses.email,
        website: businessSchema.businesses.website,
        logo_url: businessSchema.businesses.logo_url,
        hero_image_url: businessSchema.businesses.hero_image_url,
        is_featured: businessSchema.businesses.is_featured,
        average_rating: businessSchema.businesses.average_rating,
        total_reviews: businessSchema.businesses.total_reviews,
        created_at: businessSchema.businesses.created_at,
        category: {
          id: businessSchema.categories.id,
          name: businessSchema.categories.name,
          slug: businessSchema.categories.slug,
        },
        // Add search relevance score when using full-text search
        ...(q && {
          search_rank: sql<number>`
            ts_rank_cd(
              to_tsvector('english', ${businessSchema.businesses.name} || ' ' || 
                COALESCE(${businessSchema.businesses.description}, '') || ' ' || 
                COALESCE(${businessSchema.businesses.short_description}, '')),
              to_tsquery('english', ${q.split(' ').map(term => `${term}:*`).join(' & ')})
            )
          `
        })
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(eq(businessSchema.businesses.is_active, true));

    // Build where conditions
    const whereConditions = [eq(businessSchema.businesses.is_active, true)];

    // Full-text search
    if (q) {
      const searchTerms = q.split(' ').map(term => `${term}:*`).join(' & ');
      whereConditions.push(
        sql`to_tsvector('english', ${businessSchema.businesses.name} || ' ' || 
            COALESCE(${businessSchema.businesses.description}, '') || ' ' || 
            COALESCE(${businessSchema.businesses.short_description}, '')) 
            @@ to_tsquery('english', ${searchTerms})`
      );
    }

    // Category filter
    if (category) {
      whereConditions.push(eq(businessSchema.businesses.category_id, parseInt(category)));
    }

    // Location filter (basic address search - can be enhanced with geocoding)
    if (location) {
      whereConditions.push(
        sql`${businessSchema.businesses.address} ILIKE ${`%${location}%`}`
      );
    }

    // Rating filter
    if (rating) {
      whereConditions.push(
        sql`${businessSchema.businesses.average_rating} >= ${rating}`
      );
    }

    // Featured filter
    if (featured) {
      whereConditions.push(eq(businessSchema.businesses.is_featured, true));
    }

    // Apply all conditions
    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    // Apply sorting
    switch (sortBy) {
      case 'rating':
        query = query.orderBy(desc(businessSchema.businesses.average_rating), desc(businessSchema.businesses.total_reviews));
        break;
      case 'newest':
        query = query.orderBy(desc(businessSchema.businesses.created_at));
        break;
      case 'name':
        query = query.orderBy(businessSchema.businesses.name);
        break;
      case 'relevance':
      default:
        if (q) {
          query = query.orderBy(
            sql`ts_rank_cd(
              to_tsvector('english', ${businessSchema.businesses.name} || ' ' || 
                COALESCE(${businessSchema.businesses.description}, '') || ' ' || 
                COALESCE(${businessSchema.businesses.short_description}, '')),
              to_tsquery('english', ${q.split(' ').map(term => `${term}:*`).join(' & ')})
            ) DESC`,
            desc(businessSchema.businesses.is_featured),
            desc(businessSchema.businesses.average_rating)
          );
        } else {
          query = query.orderBy(desc(businessSchema.businesses.is_featured), desc(businessSchema.businesses.created_at));
        }
        break;
    }

    const businesses = await query.limit(limit).offset(offset);

    // Get total count with same filters
    let countQuery = db.select({ total: count() }).from(businessSchema.businesses);
    if (whereConditions.length > 0) {
      countQuery = countQuery.where(and(...whereConditions));
    }

    const [{ total }] = await countQuery;

    return c.json({
      success: true,
      data: {
        businesses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        search: {
          query: q,
          category,
          location,
          rating,
          featured,
          sortBy
        }
      }
    });
  } catch (error) {
    console.error('Error performing search:', error);
    return c.json({ 
      success: false,
      error: 'Search failed' 
    }, 500);
  }
});

// Search suggestions/autocomplete
publicSearchRoutes.get('/suggestions', async (c) => {
  try {
    const q = c.req.query('q');
    const limit = parseInt(c.req.query('limit') || '5');
    
    if (!q || q.length < 2) {
      return c.json({
        success: true,
        data: { suggestions: [] }
      });
    }

    const db = await getDatabase();

    // Get business name suggestions
    const businessSuggestions = await db
      .select({
        type: sql<string>`'business'`,
        name: businessSchema.businesses.name,
        slug: businessSchema.businesses.slug,
        category_name: businessSchema.categories.name
      })
      .from(businessSchema.businesses)
      .leftJoin(businessSchema.categories, eq(businessSchema.businesses.category_id, businessSchema.categories.id))
      .where(and(
        eq(businessSchema.businesses.is_active, true),
        sql`${businessSchema.businesses.name} ILIKE ${`%${q}%`}`
      ))
      .limit(limit);

    // Get category suggestions
    const categorySuggestions = await db
      .select({
        type: sql<string>`'category'`,
        name: businessSchema.categories.name,
        slug: businessSchema.categories.slug,
        business_count: sql<number>`count(${businessSchema.businesses.id})`
      })
      .from(businessSchema.categories)
      .leftJoin(businessSchema.businesses, and(
        eq(businessSchema.categories.id, businessSchema.businesses.category_id),
        eq(businessSchema.businesses.is_active, true)
      ))
      .where(and(
        eq(businessSchema.categories.is_active, true),
        sql`${businessSchema.categories.name} ILIKE ${`%${q}%`}`
      ))
      .groupBy(businessSchema.categories.id, businessSchema.categories.name, businessSchema.categories.slug)
      .limit(limit);

    const suggestions = [
      ...businessSuggestions.map(s => ({ ...s, category_name: s.category_name })),
      ...categorySuggestions.map(s => ({ ...s, category_name: null }))
    ].slice(0, limit);

    return c.json({
      success: true,
      data: { suggestions }
    });
  } catch (error) {
    console.error('Error getting search suggestions:', error);
    return c.json({ 
      success: false,
      error: 'Failed to get suggestions' 
    }, 500);
  }
});

// Location-based search
publicSearchRoutes.get('/location', async (c) => {
  try {
    const latitude = parseFloat(c.req.query('latitude') || '0');
    const longitude = parseFloat(c.req.query('longitude') || '0');
    const radius = parseInt(c.req.query('radius') || '50');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const category = c.req.query('category');
    const search = c.req.query('search');
    const rating = c.req.query('rating') ? parseFloat(c.req.query('rating')!) : undefined;

    if (!latitude || !longitude) {
      return c.json({
        success: false,
        error: 'Latitude and longitude are required'
      }, 400);
    }

    const result = await LocationService.searchBusinessesByLocation({
      latitude,
      longitude,
      radius,
      page,
      limit,
      category,
      search,
      rating
    });

    return c.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error performing location search:', error);
    return c.json({ 
      success: false,
      error: 'Location search failed' 
    }, 500);
  }
});

// Get nearby businesses
publicSearchRoutes.get('/nearby/:businessId', async (c) => {
  try {
    const businessId = parseInt(c.req.param('businessId'));
    const radius = parseInt(c.req.query('radius') || '10');
    const limit = parseInt(c.req.query('limit') || '5');

    const nearbyBusinesses = await LocationService.getNearbyBusinesses(businessId, radius, limit);

    return c.json({
      success: true,
      data: {
        nearby_businesses: nearbyBusinesses
      }
    });
  } catch (error) {
    console.error('Error fetching nearby businesses:', error);
    return c.json({ 
      success: false,
      error: 'Failed to fetch nearby businesses' 
    }, 500);
  }
});

// Get location suggestions
publicSearchRoutes.get('/locations', async (c) => {
  try {
    const q = c.req.query('q');
    const limit = parseInt(c.req.query('limit') || '5');
    
    if (!q || q.length < 2) {
      return c.json({
        success: true,
        data: { locations: [] }
      });
    }

    const locations = await LocationService.getLocationSuggestions(q, limit);

    return c.json({
      success: true,
      data: { locations }
    });
  } catch (error) {
    console.error('Error getting location suggestions:', error);
    return c.json({ 
      success: false,
      error: 'Failed to get location suggestions' 
    }, 500);
  }
});

// Get business density for heatmap
publicSearchRoutes.get('/density', async (c) => {
  try {
    const neLat = parseFloat(c.req.query('neLat') || '0');
    const neLon = parseFloat(c.req.query('neLon') || '0');
    const swLat = parseFloat(c.req.query('swLat') || '0');
    const swLon = parseFloat(c.req.query('swLon') || '0');
    const gridSize = parseInt(c.req.query('gridSize') || '10');

    if (!neLat || !neLon || !swLat || !swLon) {
      return c.json({
        success: false,
        error: 'Bounding box coordinates are required (neLat, neLon, swLat, swLon)'
      }, 400);
    }

    const density = await LocationService.getBusinessDensity({
      northEast: { latitude: neLat, longitude: neLon },
      southWest: { latitude: swLat, longitude: swLon }
    }, gridSize);

    return c.json({
      success: true,
      data: { density }
    });
  } catch (error) {
    console.error('Error getting business density:', error);
    return c.json({ 
      success: false,
      error: 'Failed to get business density' 
    }, 500);
  }
});

export default publicSearchRoutes;