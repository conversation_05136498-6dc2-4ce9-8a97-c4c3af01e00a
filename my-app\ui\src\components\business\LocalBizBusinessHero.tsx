import { Star, MapPin, Clock, Phone, Mail, Globe } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface BusinessData {
  id: number;
  name: string;
  description?: string;
  address: string;
  phone?: string;
  email?: string;
  website?: string;
  logo_url?: string;
  average_rating: string;
  total_reviews: number;
  is_featured: boolean;
  category?: {
    name: string;
  };
  hours?: Array<{
    day_of_week: string;
    open_time: string;
    close_time: string;
    is_closed: boolean;
  }>;
}

interface LocalBizBusinessHeroProps {
  business: BusinessData;
}

const RatingStars = ({ rating, showCount = false, count = 0 }: { rating: number; showCount?: boolean; count?: number }) => {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? 'fill-current text-yellow-400' : 'text-gray-300'
          }`}
        />
      ))}
      {showCount && (
        <span className="text-sm text-muted-foreground ml-1">
          ({count} review{count !== 1 ? 's' : ''})
        </span>
      )}
    </div>
  );
};

const getCurrentStatus = (hours?: BusinessData['hours']) => {
  if (!hours || hours.length === 0) {
    return { isOpen: false, status: 'Hours not available' };
  }

  const now = new Date();
  const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

  const todayHours = hours.find(h => h.day_of_week.toLowerCase() === currentDay);
  
  if (!todayHours || todayHours.is_closed) {
    return { isOpen: false, status: 'Closed today' };
  }

  const isOpen = currentTime >= todayHours.open_time && currentTime <= todayHours.close_time;
  const status = isOpen 
    ? `Open until ${todayHours.close_time}` 
    : `Closed • Opens at ${todayHours.open_time}`;

  return { isOpen, status };
};

export function LocalBizBusinessHero({ business }: LocalBizBusinessHeroProps) {
  const { isOpen, status } = getCurrentStatus(business.hours);
  const rating = parseFloat(business.average_rating);

  return (
    <div className="px-10 py-6">
      <Card className="rounded-xl border-border">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            {/* Business Logo */}
            <div className="w-20 h-20 rounded-xl overflow-hidden bg-muted flex-shrink-0">
              {business.logo_url ? (
                <img
                  src={business.logo_url}
                  alt={`${business.name} logo`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-2xl font-bold text-muted-foreground">
                  {business.name.charAt(0)}
                </div>
              )}
            </div>
            
            {/* Business Info */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h1 className="text-2xl font-bold text-foreground mb-1">{business.name}</h1>
                  {business.category && (
                    <p className="text-sm text-muted-foreground">{business.category.name}</p>
                  )}
                </div>
                {business.is_featured && (
                  <Badge variant="default" className="ml-4">
                    Featured
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-4 mb-2">
                <RatingStars rating={Math.floor(rating)} showCount count={business.total_reviews} />
                <span className="text-sm font-medium">{rating.toFixed(1)}</span>
              </div>
              
              <div className="flex items-center gap-2 mb-4">
                <Badge variant={isOpen ? "default" : "secondary"} className="text-xs">
                  <Clock className="w-3 h-3 mr-1" />
                  {status}
                </Badge>
                {business.address && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="w-3 h-3 mr-1" />
                    {business.address}
                  </div>
                )}
              </div>
              
              {/* Quick Contact Actions */}
              <div className="flex items-center gap-2">
                {business.phone && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={`tel:${business.phone}`}>
                      <Phone className="w-3 h-3 mr-1" />
                      Call
                    </a>
                  </Button>
                )}
                {business.email && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={`mailto:${business.email}`}>
                      <Mail className="w-3 h-3 mr-1" />
                      Email
                    </a>
                  </Button>
                )}
                {business.website && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={business.website.startsWith('http') ? business.website : `https://${business.website}`} target="_blank" rel="noopener noreferrer">
                      <Globe className="w-3 h-3 mr-1" />
                      Website
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
