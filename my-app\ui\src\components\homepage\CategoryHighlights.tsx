import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { api } from '@/lib/serverComm';
import { 
  Store, 
  Utensils, 
  Car, 
  Heart, 
  Briefcase, 
  Home,
  Scissors,
  Dumbbell,
  GraduationCap,
  Wrench
} from 'lucide-react';

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
  business_count: number;
}

const iconMap: Record<string, React.ComponentType<any>> = {
  store: Store,
  utensils: Utensils,
  car: Car,
  heart: Heart,
  briefcase: Briefcase,
  home: Home,
  scissors: Scissors,
  dumbbell: Dumbbell,
  'graduation-cap': GraduationCap,
  wrench: Wrench,
};

export function CategoryHighlights() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCategories() {
      try {
        const response = await api.getCategories();
        
        // Defensive programming: handle different response formats
        let categoriesData: Category[] = [];
        if (response && response.categories && Array.isArray(response.categories)) {
          categoriesData = response.categories;
        } else if (response && Array.isArray(response)) {
          // Handle case where response is directly an array
          categoriesData = response;
        } else {
          console.warn('Unexpected categories response format:', response);
          categoriesData = [];
        }
        
        // Show top 8 categories with most businesses
        const sortedCategories = categoriesData
          .sort((a: Category, b: Category) => (b.business_count || 0) - (a.business_count || 0))
          .slice(0, 8);
        setCategories(sortedCategories);
      } catch (err) {
        setError('Failed to load categories');
        console.error('Error fetching categories:', err);
        setCategories([]); // Ensure we have an empty array
      } finally {
        setLoading(false);
      }
    }

    fetchCategories();
  }, []);

  if (loading) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Browse Categories</h2>
            <p className="text-muted-foreground">Explore businesses by category</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-muted rounded-full mx-auto mb-4"></div>
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-3 bg-muted rounded w-16 mx-auto"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <p className="text-muted-foreground">{error}</p>
        </div>
      </section>
    );
  }

  // Category data with images matching the mockup - used as fallback
  const categoryImageMap: Record<string, string> = {
    "restaurants": "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=300&h=300&fit=crop",
    "home services": "https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop",
    "beauty & spas": "https://images.unsplash.com/photo-**********-138dadb4c035?w=300&h=300&fit=crop",
    "automotive": "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=300&h=300&fit=crop",
    "health & medical": "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=300&fit=crop",
    "professional services": "https://images.unsplash.com/photo-1497366216548-37526070297c?w=300&h=300&fit=crop"
  };

  const fallbackCategories = [
    { name: "Restaurants", slug: "restaurants" },
    { name: "Home Services", slug: "home-services" },
    { name: "Beauty & Spas", slug: "beauty-spas" },
    { name: "Automotive", slug: "automotive" },
    { name: "Health & Medical", slug: "health-medical" },
    { name: "Professional Services", slug: "professional-services" }
  ];

  if (loading) {
    return (
      <section className="py-12 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Popular Categories</h2>
            <p className="text-muted-foreground text-lg">Explore businesses by category</p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex flex-col items-center gap-3">
                <div className="w-20 h-20 bg-muted rounded-full animate-pulse" />
                <div className="h-4 bg-muted rounded w-16 animate-pulse" />
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-12 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Popular Categories</h2>
            <p className="text-muted-foreground text-lg">Explore businesses by category</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  const displayCategories = Array.isArray(categories) && categories.length > 0
    ? categories.slice(0, 6)
    : fallbackCategories;

  return (
    <section className="py-12 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">Popular Categories</h2>
          <p className="text-muted-foreground text-lg">Explore businesses by category</p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
          {displayCategories.map((category, index) => {
            const categoryKey = category.name.toLowerCase();
            const imageUrl = categoryImageMap[categoryKey] || `https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop`;
            const IconComponent = 'id' in category ? iconMap[category.icon] || Store : Store;

            return (
              <Link
                key={'id' in category ? category.id : index}
                to={`/categories/${category.slug}`}
                className="flex flex-col items-center gap-3 group"
              >
                <div className="relative">
                  <div
                    className="w-20 h-20 bg-center bg-no-repeat bg-cover rounded-full group-hover:scale-110 transition-transform duration-200 shadow-lg border-4 border-white dark:border-gray-800"
                    style={{
                      backgroundImage: `url("${imageUrl}")`
                    }}
                  >
                    {/* Overlay with icon for API categories */}
                    {'id' in category && (
                      <div className="w-full h-full bg-black/30 rounded-full flex items-center justify-center">
                        <IconComponent className="w-6 h-6 text-white drop-shadow-lg" />
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-foreground text-sm font-medium leading-normal group-hover:text-primary transition-colors text-center">
                  {category.name}
                </p>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  );
}
