import { <PERSON>, Sun } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useThemePreference } from "@/hooks/useThemePreference";
import { getTransitionClasses, getThemeAriaLabel } from "@/lib/theme-utils";

interface ModeToggleProps {
  className?: string;
  size?: "sm" | "icon" | "lg";
  variant?: "default" | "outline" | "ghost" | "secondary";
}

export function ModeToggle({
  className,
  size = "icon",
  variant = "ghost",
  ...props
}: ModeToggleProps) {
  const { theme, setTheme, resolvedTheme, mounted } = useThemePreference();

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn("h-8 w-8", className)}
        disabled
        {...props}
      >
        <Sun className="h-4 w-4" />
        <span className="sr-only">Loading theme toggle</span>
      </Button>
    );
  }

  const handleToggle = () => {
    setTheme(resolvedTheme === "light" ? "dark" : "light");
  };

  const ariaLabel = getThemeAriaLabel(resolvedTheme || "light");

  return (
    <Button
      variant={variant}
      size={size}
      className={cn(
        "h-8 w-8 relative",
        getTransitionClasses('button'),
        className
      )}
      onClick={handleToggle}
      aria-label={ariaLabel}
      title={ariaLabel}
      {...props}
    >
      <Sun className={cn(
        "h-4 w-4 absolute transition-all duration-300 ease-in-out",
        resolvedTheme === "light"
          ? "rotate-0 scale-100 opacity-100"
          : "-rotate-90 scale-0 opacity-0"
      )} />
      <Moon className={cn(
        "h-4 w-4 absolute transition-all duration-300 ease-in-out",
        resolvedTheme === "dark"
          ? "rotate-0 scale-100 opacity-100"
          : "rotate-90 scale-0 opacity-0"
      )} />
      <span className="sr-only">{ariaLabel}</span>
    </Button>
  );
}