{"feature-business-directory-platform-implementation": {"tasks": [{"id": "phase-1-foundation", "title": "Phase 1: Foundation Setup", "description": "Set up database schema, API routes, and authentication foundation", "status": "not_started", "priority": "high", "estimatedHours": 16, "dependencies": [], "subtasks": [{"id": "database-schema-extension", "title": "Extend Drizzle Schema for Business Tables", "description": "Create business.ts schema file with categories, businesses, business_hours, business_images, reviews, and business_applications tables following existing Drizzle patterns", "status": "not_started", "estimatedHours": 4, "files": ["server/src/schema/business.ts"], "acceptance_criteria": ["All business-related tables defined with proper types", "Foreign key relationships established", "TypeScript interfaces exported", "Follows existing schema patterns"]}, {"id": "neon-db-configuration", "title": "Configure Neon DB for Production", "description": "Set up production database connection and run migrations for business tables", "status": "not_started", "estimatedHours": 2, "files": ["server/src/lib/db.ts", "server/drizzle.config.ts"], "acceptance_criteria": ["Neon DB connection configured", "Migrations run successfully", "Local PostgreSQL still works for development", "Environment variables properly set"]}, {"id": "core-api-routes", "title": "Create Core Business API Routes", "description": "Implement public and admin API endpoints for businesses, categories, search, and applications in Hono server", "status": "not_started", "estimatedHours": 6, "files": ["server/src/api.ts"], "acceptance_criteria": ["All public endpoints implemented", "Admin endpoints with auth middleware", "Proper error handling and validation", "Follows existing API patterns"]}, {"id": "firebase-auth-extension", "title": "Extend Firebase Auth for Admin Roles", "description": "Add admin role management through Firebase custom claims and update auth middleware", "status": "not_started", "estimatedHours": 4, "files": ["server/src/middleware/auth.ts", "ui/src/lib/auth-context.tsx"], "acceptance_criteria": ["Admin custom claims implemented", "Auth middleware supports admin routes", "Frontend auth context updated", "Role-based access control working"]}]}, {"id": "phase-2-core-pages", "title": "Phase 2: Core Pages Implementation", "description": "Build homepage, categories, business profiles, and search functionality", "status": "not_started", "priority": "high", "estimatedHours": 20, "dependencies": ["phase-1-foundation"], "subtasks": [{"id": "homepage-components", "title": "Homepage Components Using ShadCN/UI Patterns", "description": "Create hero section, category highlights, featured businesses, new businesses, and marquee banner components", "status": "not_started", "estimatedHours": 8, "files": ["ui/src/components/homepage/HeroSection.tsx", "ui/src/components/homepage/CategoryHighlights.tsx", "ui/src/components/homepage/FeaturedBusinesses.tsx", "ui/src/components/homepage/NewBusinesses.tsx", "ui/src/components/homepage/MarqueeBanner.tsx", "ui/src/pages/HomePage.tsx"], "acceptance_criteria": ["All homepage sections implemented", "Uses design system tokens", "Responsive design working", "Follows ShadCN/UI patterns", "Theme compatibility maintained"]}, {"id": "categories-page", "title": "Categories Page with Existing Routing", "description": "Implement categories grid page and category detail pages with business listings", "status": "not_started", "estimatedHours": 6, "files": ["ui/src/pages/CategoriesPage.tsx", "ui/src/pages/CategoryDetailPage.tsx", "ui/src/components/business/BusinessCard.tsx"], "acceptance_criteria": ["Categories grid layout implemented", "Category detail pages working", "Business cards follow design patterns", "Routing integrated with React Router"]}, {"id": "business-profile-template", "title": "Business Profile Template with Design Tokens", "description": "Create dynamic business profile page with all required sections using design system", "status": "not_started", "estimatedHours": 6, "files": ["ui/src/pages/BusinessProfilePage.tsx", "ui/src/components/business/BusinessProfile.tsx", "ui/src/components/business/BusinessHeader.tsx", "ui/src/components/business/ContactInfo.tsx", "ui/src/components/business/BusinessHours.tsx", "ui/src/components/business/PhotoGallery.tsx", "ui/src/components/business/ReviewsSection.tsx"], "acceptance_criteria": ["Single template for all business types", "Dynamic field population working", "SEO-optimized URLs implemented", "All profile sections functional", "Design tokens used throughout"]}]}, {"id": "phase-3-interactive-features", "title": "Phase 3: Interactive Features", "description": "Implement Mapbox integration, filtering, business applications, and admin workflow", "status": "not_started", "priority": "medium", "estimatedHours": 24, "dependencies": ["phase-2-core-pages"], "subtasks": [{"id": "mapbox-integration", "title": "Mapbox Integration as React Component", "description": "Implement interactive map with business markers, clustering, and mobile-responsive controls", "status": "not_started", "estimatedHours": 8, "files": ["ui/src/components/map/InteractiveMap.tsx", "ui/src/components/map/BusinessMarker.tsx", "ui/src/components/map/MarkerPopup.tsx", "ui/src/components/map/MapControls.tsx", "ui/src/components/map/MapFilters.tsx", "ui/src/components/business/BusinessMap.tsx"], "acceptance_criteria": ["Mapbox GL JS properly integrated", "Custom business markers working", "Clustering for dense areas", "Mobile-responsive controls", "Design system styling applied"]}, {"id": "advanced-filtering-system", "title": "Advanced Filtering System", "description": "Implement search functionality with filters for location, rating, services, and price range", "status": "not_started", "estimatedHours": 6, "files": ["ui/src/components/search/SearchBar.tsx", "ui/src/components/search/FilterPanel.tsx", "ui/src/components/search/SearchResults.tsx", "ui/src/pages/SearchPage.tsx"], "acceptance_criteria": ["Advanced search with autocomplete", "Geographic radius filtering", "Category-based filtering", "Real-time results with debouncing", "Sorting capabilities implemented"]}, {"id": "business-application-form", "title": "Business Application Form with Validation", "description": "Create multi-step business registration form using React Hook Form", "status": "not_started", "estimatedHours": 6, "files": ["ui/src/pages/BusinessApplicationPage.tsx", "ui/src/components/forms/BusinessApplicationForm.tsx", "ui/src/components/forms/ApplicationSteps.tsx"], "acceptance_criteria": ["Multi-step form implemented", "Validation using existing patterns", "File upload for business images", "Location geocoding integration", "Terms and conditions acceptance"]}, {"id": "admin-review-workflow", "title": "Admin Review Workflow", "description": "Build admin interface for reviewing and approving business applications", "status": "not_started", "estimatedHours": 4, "files": ["ui/src/pages/admin/ApplicationReviewPage.tsx", "ui/src/components/admin/ApplicationQueue.tsx", "ui/src/components/admin/ApplicationDetails.tsx"], "acceptance_criteria": ["Application queue management", "Business information verification", "Bulk approval/rejection capabilities", "Application status tracking"]}]}, {"id": "phase-4-enhancement", "title": "Phase 4: Enhancement & Polish", "description": "Add reviews, advanced search, performance optimization, SEO, and testing", "status": "not_started", "priority": "medium", "estimatedHours": 20, "dependencies": ["phase-3-interactive-features"], "subtasks": [{"id": "review-rating-system", "title": "Review and Rating System", "description": "Implement customer review submission and display system with moderation", "status": "not_started", "estimatedHours": 6, "files": ["ui/src/components/reviews/ReviewForm.tsx", "ui/src/components/reviews/ReviewList.tsx", "ui/src/components/reviews/RatingDisplay.tsx", "ui/src/pages/admin/ReviewModerationPage.tsx"], "acceptance_criteria": ["Review submission form working", "Rating display components", "Admin moderation interface", "Spam detection and filtering"]}, {"id": "performance-optimization", "title": "Performance Optimization", "description": "Optimize loading times, implement lazy loading, and improve mobile performance", "status": "not_started", "estimatedHours": 4, "files": ["ui/src/components/", "server/src/api.ts"], "acceptance_criteria": ["Page load time < 3 seconds", "Lazy loading for map components", "Image optimization implemented", "Bundle size optimized"]}, {"id": "seo-implementation", "title": "SEO Implementation", "description": "Add meta tags, schema markup, and SEO-optimized URLs for business pages", "status": "not_started", "estimatedHours": 4, "files": ["ui/src/components/seo/MetaTags.tsx", "ui/src/components/seo/SchemaMarkup.tsx"], "acceptance_criteria": ["Schema markup for local business", "SEO-optimized URLs working", "Meta tags for all pages", "Sitemap generation"]}, {"id": "mobile-optimization", "title": "Mobile Optimization", "description": "Ensure all components work perfectly on mobile devices with touch interactions", "status": "not_started", "estimatedHours": 3, "files": ["ui/src/components/", "ui/src/styles/"], "acceptance_criteria": ["Touch-friendly interface elements", "Optimized map interactions for mobile", "Responsive design verified", "Mobile performance optimized"]}, {"id": "testing-quality-assurance", "title": "Testing and Quality Assurance", "description": "Write tests for components and API endpoints, perform cross-browser testing", "status": "not_started", "estimatedHours": 3, "files": ["ui/src/components/__tests__/", "server/src/__tests__/"], "acceptance_criteria": ["Component tests written", "API endpoint tests implemented", "Cross-browser compatibility verified", "Accessibility compliance tested"]}]}, {"id": "admin-dashboard-complete", "title": "Complete Admin Dashboard", "description": "Build comprehensive admin interface for business and content management", "status": "not_started", "priority": "medium", "estimatedHours": 16, "dependencies": ["phase-2-core-pages"], "subtasks": [{"id": "admin-dashboard-overview", "title": "Admin Dashboard Overview", "description": "Create main admin dashboard with statistics, analytics, and quick actions", "status": "not_started", "estimatedHours": 4, "files": ["ui/src/pages/admin/AdminDashboard.tsx", "ui/src/components/admin/DashboardStats.tsx", "ui/src/components/admin/QuickActions.tsx"], "acceptance_criteria": ["Business statistics displayed", "Recent applications queue", "System health monitoring", "Quick action buttons functional"]}, {"id": "business-management-interface", "title": "Business Management Interface", "description": "Build CRUD interface for managing businesses with bulk operations", "status": "not_started", "estimatedHours": 6, "files": ["ui/src/pages/admin/BusinessManagementPage.tsx", "ui/src/components/admin/BusinessForm.tsx", "ui/src/components/admin/BusinessList.tsx", "ui/src/components/admin/BulkActions.tsx"], "acceptance_criteria": ["CRUD operations for businesses", "Bulk import/export capabilities", "Featured business selection", "Image management system"]}, {"id": "category-management", "title": "Category Management", "description": "Create interface for managing business categories and their properties", "status": "not_started", "estimatedHours": 3, "files": ["ui/src/pages/admin/CategoryManagementPage.tsx", "ui/src/components/admin/CategoryForm.tsx"], "acceptance_criteria": ["Category CRUD operations", "Icon management for categories", "Category hierarchy support", "Business count tracking"]}, {"id": "email-notification-system", "title": "Email Notification System", "description": "Implement email notifications for application workflow and admin communications", "status": "not_started", "estimatedHours": 3, "files": ["server/src/lib/email.ts", "server/src/templates/email-templates.ts"], "acceptance_criteria": ["Email service integration", "Application approval/rejection emails", "Admin notification emails", "Email templates implemented"]}]}]}}