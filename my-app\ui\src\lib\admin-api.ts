import { getAuth } from 'firebase/auth';
import { app } from './firebase';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8787';

class APIError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'APIError';
  }
}

async function getAuthToken(): Promise<string | null> {
  const auth = getAuth(app);
  const user = auth.currentUser;
  if (!user) {
    return null;
  }
  return user.getIdToken();
}

export async function fetchWithAuth(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const token = await getAuthToken();
  const headers = new Headers(options.headers);

  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    throw new APIError(
      response.status,
      `API request failed: ${response.statusText}`
    );
  }

  return response;
}

// ===== ADMIN DASHBOARD API =====
export async function getAdminDashboardStats() {
  const response = await fetchWithAuth('/api/v1/admin/dashboard/stats');
  return response.json();
}

export async function getAdminBusinessTrends(period: string = '30d') {
  const response = await fetchWithAuth(`/api/v1/admin/dashboard/trends?period=${period}`);
  return response.json();
}

export async function getAdminCategoryPerformance() {
  const response = await fetchWithAuth('/api/v1/admin/dashboard/categories');
  return response.json();
}

export async function getAdminApplicationAnalytics() {
  const response = await fetchWithAuth('/api/v1/admin/dashboard/applications');
  return response.json();
}

// ===== ADMIN BUSINESS MANAGEMENT API =====
export async function getAdminBusinesses(params?: {
  page?: number;
  limit?: number;
  status?: string;
  category?: string;
  search?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}) {
  const searchParams = new URLSearchParams();
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.status) searchParams.set('status', params.status);
  if (params?.category) searchParams.set('category', params.category);
  if (params?.search) searchParams.set('search', params.search);
  if (params?.sort) searchParams.set('sort', params.sort);
  if (params?.order) searchParams.set('order', params.order);

  const response = await fetchWithAuth(`/api/v1/admin/businesses?${searchParams}`);
  return response.json();
}

export async function getAdminBusiness(id: number) {
  const response = await fetchWithAuth(`/api/v1/admin/businesses/${id}`);
  return response.json();
}

export async function updateAdminBusiness(id: number, data: any) {
  const response = await fetchWithAuth(`/api/v1/admin/businesses/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function deleteAdminBusiness(id: number) {
  const response = await fetchWithAuth(`/api/v1/admin/businesses/${id}`, {
    method: 'DELETE',
  });
  return response.json();
}

export async function toggleBusinessFeatured(id: number) {
  const response = await fetchWithAuth(`/api/v1/admin/businesses/${id}/featured`, {
    method: 'PATCH',
  });
  return response.json();
}

export async function toggleBusinessStatus(id: number) {
  const response = await fetchWithAuth(`/api/v1/admin/businesses/${id}/status`, {
    method: 'PATCH',
  });
  return response.json();
}

// ===== ADMIN CATEGORY MANAGEMENT API =====
export async function getAdminCategories(params?: {
  page?: number;
  limit?: number;
  search?: string;
}) {
  const searchParams = new URLSearchParams();
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.search) searchParams.set('search', params.search);

  const response = await fetchWithAuth(`/api/v1/admin/categories?${searchParams}`);
  return response.json();
}

export async function createAdminCategory(data: {
  name: string;
  slug: string;
  description?: string;
  icon?: string;
}) {
  const response = await fetchWithAuth('/api/v1/admin/categories', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function updateAdminCategory(id: number, data: any) {
  const response = await fetchWithAuth(`/api/v1/admin/categories/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function deleteAdminCategory(id: number) {
  const response = await fetchWithAuth(`/api/v1/admin/categories/${id}`, {
    method: 'DELETE',
  });
  return response.json();
}

// ===== ADMIN REVIEW MANAGEMENT API =====
export async function getAdminReviews(params?: {
  page?: number;
  limit?: number;
  status?: string;
  business_id?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}) {
  const searchParams = new URLSearchParams();
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.status) searchParams.set('status', params.status);
  if (params?.business_id) searchParams.set('business_id', params.business_id.toString());
  if (params?.sort) searchParams.set('sort', params.sort);
  if (params?.order) searchParams.set('order', params.order);

  const response = await fetchWithAuth(`/api/v1/admin/reviews?${searchParams}`);
  return response.json();
}

export async function moderateReview(id: number, action: 'approve' | 'reject', reason?: string) {
  const response = await fetchWithAuth(`/api/v1/admin/reviews/${id}/moderate`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ action, reason }),
  });
  return response.json();
}

export async function deleteAdminReview(id: number) {
  const response = await fetchWithAuth(`/api/v1/admin/reviews/${id}`, {
    method: 'DELETE',
  });
  return response.json();
}

// ===== ADMIN USER MANAGEMENT API =====
export async function getAdminUsers(params?: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
}) {
  const searchParams = new URLSearchParams();
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.search) searchParams.set('search', params.search);
  if (params?.role) searchParams.set('role', params.role);

  const response = await fetchWithAuth(`/api/v1/admin/users?${searchParams}`);
  return response.json();
}

export async function toggleUserAdminStatus(id: string) {
  const response = await fetchWithAuth(`/api/v1/admin/users/${id}/admin`, {
    method: 'PATCH',
  });
  return response.json();
}

export async function deleteAdminUser(id: string) {
  const response = await fetchWithAuth(`/api/v1/admin/users/${id}`, {
    method: 'DELETE',
  });
  return response.json();
}

// ===== ADMIN APPLICATION MANAGEMENT API =====
export async function getAdminApplications(params?: {
  page?: number;
  limit?: number;
  status?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}) {
  const searchParams = new URLSearchParams();
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.status) searchParams.set('status', params.status);
  if (params?.sort) searchParams.set('sort', params.sort);
  if (params?.order) searchParams.set('order', params.order);

  const response = await fetchWithAuth(`/api/v1/admin/applications?${searchParams}`);
  return response.json();
}

export async function reviewApplication(id: number, action: 'approve' | 'reject', notes?: string) {
  const response = await fetchWithAuth(`/api/v1/admin/applications/${id}/review`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ action, notes }),
  });
  return response.json();
}

export async function getAdminApplication(id: number) {
  const response = await fetchWithAuth(`/api/v1/admin/applications/${id}`);
  return response.json();
}

// Photo Collection API functions
export async function collectBusinessPhotos(businessId: number) {
  const response = await fetchWithAuth(`/api/v1/admin/photos/businesses/${businessId}/collect-photos`, {
    method: 'POST',
  });
  return response.json();
}

export async function getPendingPhotos(businessId?: number) {
  const url = businessId
    ? `/api/v1/admin/photos/pending?business_id=${businessId}`
    : '/api/v1/admin/photos/pending';
  const response = await fetchWithAuth(url);
  return response.json();
}

export async function approvePhoto(photoId: number) {
  const response = await fetchWithAuth(`/api/v1/admin/photos/${photoId}/approve`, {
    method: 'PUT',
  });
  return response.json();
}

export async function rejectPhoto(photoId: number) {
  const response = await fetchWithAuth(`/api/v1/admin/photos/${photoId}/reject`, {
    method: 'DELETE',
  });
  return response.json();
}

export async function setPrimaryPhoto(photoId: number) {
  const response = await fetchWithAuth(`/api/v1/admin/photos/${photoId}/set-primary`, {
    method: 'PUT',
  });
  return response.json();
}

export async function bulkApprovePhotos(businessId: number) {
  const response = await fetchWithAuth(`/api/v1/admin/businesses/${businessId}/photos/approve-all`, {
    method: 'PUT',
  });
  return response.json();
}

export async function addManualPhoto(businessId: number, data: { image_url: string; alt_text?: string; display_order?: number }) {
  const response = await fetchWithAuth(`/api/v1/admin/businesses/${businessId}/photos/manual`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  return response.json();
}

export const adminApi = {
  // Dashboard
  getAdminDashboardStats,
  getAdminBusinessTrends,
  getAdminCategoryPerformance,
  getAdminApplicationAnalytics,

  // Businesses
  getAdminBusinesses,
  getAdminBusiness,
  updateAdminBusiness,
  deleteAdminBusiness,
  toggleBusinessFeatured,
  toggleBusinessStatus,

  // Categories
  getAdminCategories,
  createAdminCategory,
  updateAdminCategory,
  deleteAdminCategory,

  // Reviews
  getAdminReviews,
  moderateReview,
  deleteAdminReview,

  // Users
  getAdminUsers,
  toggleUserAdminStatus,
  deleteAdminUser,

  // Applications
  getAdminApplications,
  reviewApplication,
  getAdminApplication,

  // Photo Collection
  collectBusinessPhotos,
  getPendingPhotos,
  approvePhoto,
  rejectPhoto,
  setPrimaryPhoto,
  bulkApprovePhotos,
  addManualPhoto,
};
