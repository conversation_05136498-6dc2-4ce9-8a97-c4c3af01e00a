# Database Schema Reference

## 🗄️ Schema Overview

The application uses PostgreSQL with Dr<PERSON>zle ORM and a custom `app` schema for organization.

```sql
-- Main application schema
CREATE SCHEMA IF NOT EXISTS app;
```

## 📊 Core Tables

### Users Table
Stores user accounts and authentication information.

```sql
CREATE TABLE app.users (
  id TEXT PRIMARY KEY,                    -- Firebase UID
  email TEXT UNIQUE NOT NULL,             -- User email address
  display_name TEXT,                      -- User's display name
  photo_url TEXT,                         -- Profile photo URL
  is_admin BOOLEAN DEFAULT FALSE NOT NULL, -- Admin role flag
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);
```

**Indexes:**
```sql
CREATE UNIQUE INDEX idx_users_email ON app.users(email);
CREATE INDEX idx_users_admin ON app.users(is_admin);
```

### Categories Table
Business category definitions.

```sql
CREATE TABLE app.categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,             -- Category display name
  slug VARCHAR(100) UNIQUE NOT NULL,      -- URL-friendly identifier
  icon VARCHAR(255),                      -- Icon identifier (e.g., "utensils")
  description TEXT,                       -- Category description
  is_active BOOLEAN DEFAULT TRUE NOT NULL, -- Active status
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);
```

**Indexes:**
```sql
CREATE UNIQUE INDEX idx_categories_slug ON app.categories(slug);
CREATE INDEX idx_categories_active ON app.categories(is_active);
```

### Businesses Table
Main business directory listings.

```sql
CREATE TABLE app.businesses (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,             -- Business name
  slug VARCHAR(255) UNIQUE NOT NULL,      -- URL-friendly identifier
  category_id INTEGER REFERENCES app.categories(id) NOT NULL,
  description TEXT,                       -- Full business description
  short_description VARCHAR(500),         -- Brief description for cards
  address TEXT NOT NULL,                  -- Full address
  latitude DECIMAL(10,8),                 -- GPS coordinates
  longitude DECIMAL(11,8),                -- GPS coordinates
  phone VARCHAR(20),                      -- Contact phone
  email VARCHAR(255),                     -- Contact email
  website VARCHAR(255),                   -- Business website
  logo_url VARCHAR(255),                  -- Logo image URL
  hero_image_url VARCHAR(255),            -- Hero/banner image URL
  is_featured BOOLEAN DEFAULT FALSE NOT NULL, -- Featured status
  is_active BOOLEAN DEFAULT TRUE NOT NULL,    -- Active status
  average_rating DECIMAL(3,2) DEFAULT 0.00,  -- Calculated average rating
  total_reviews INTEGER DEFAULT 0 NOT NULL,  -- Review count
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);
```

**Indexes:**
```sql
CREATE UNIQUE INDEX idx_businesses_slug ON app.businesses(slug);
CREATE INDEX idx_businesses_category ON app.businesses(category_id);
CREATE INDEX idx_businesses_featured ON app.businesses(is_featured, is_active);
CREATE INDEX idx_businesses_location ON app.businesses(latitude, longitude);
CREATE INDEX idx_businesses_rating ON app.businesses(average_rating DESC);
```

### Business Photos Table
Additional photos for businesses.

```sql
CREATE TABLE app.business_photos (
  id SERIAL PRIMARY KEY,
  business_id INTEGER REFERENCES app.businesses(id) ON DELETE CASCADE NOT NULL,
  url VARCHAR(255) NOT NULL,              -- Photo URL
  caption TEXT,                           -- Photo caption/description
  display_order INTEGER DEFAULT 0,       -- Sort order
  is_approved BOOLEAN DEFAULT FALSE NOT NULL, -- Moderation status
  created_at TIMESTAMP DEFAULT NOW() NOT NULL
);
```

**Indexes:**
```sql
CREATE INDEX idx_business_photos_business ON app.business_photos(business_id);
CREATE INDEX idx_business_photos_approved ON app.business_photos(is_approved);
```

### Reviews Table
Customer reviews and ratings.

```sql
CREATE TABLE app.reviews (
  id SERIAL PRIMARY KEY,
  business_id INTEGER REFERENCES app.businesses(id) ON DELETE CASCADE NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5), -- 1-5 stars
  comment TEXT,                           -- Review text
  author_name VARCHAR(100) NOT NULL,      -- Reviewer name
  author_email VARCHAR(255),              -- Reviewer email (optional)
  is_verified BOOLEAN DEFAULT FALSE NOT NULL, -- Verified purchase/visit
  is_approved BOOLEAN DEFAULT FALSE NOT NULL, -- Moderation status
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);
```

**Indexes:**
```sql
CREATE INDEX idx_reviews_business ON app.reviews(business_id);
CREATE INDEX idx_reviews_approved ON app.reviews(is_approved);
CREATE INDEX idx_reviews_rating ON app.reviews(rating);
```

### Applications Table
Business application submissions.

```sql
CREATE TABLE app.applications (
  id SERIAL PRIMARY KEY,
  business_name VARCHAR(255) NOT NULL,    -- Proposed business name
  category_id INTEGER REFERENCES app.categories(id) NOT NULL,
  description TEXT,                       -- Business description
  address TEXT NOT NULL,                  -- Business address
  phone VARCHAR(20),                      -- Contact phone
  email VARCHAR(255),                     -- Contact email
  website VARCHAR(255),                   -- Business website
  owner_name VARCHAR(255) NOT NULL,       -- Owner/contact name
  owner_email VARCHAR(255) NOT NULL,      -- Owner email
  status VARCHAR(20) DEFAULT 'pending' NOT NULL, -- pending/approved/rejected
  admin_notes TEXT,                       -- Admin review notes
  reviewed_by TEXT REFERENCES app.users(id), -- Admin who reviewed
  reviewed_at TIMESTAMP,                  -- Review timestamp
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);
```

**Indexes:**
```sql
CREATE INDEX idx_applications_status ON app.applications(status);
CREATE INDEX idx_applications_category ON app.applications(category_id);
CREATE INDEX idx_applications_created ON app.applications(created_at DESC);
```

## 🔗 Relationships

### Entity Relationship Diagram
```
users (1) ----< reviews (many)
categories (1) ----< businesses (many)
categories (1) ----< applications (many)
businesses (1) ----< reviews (many)
businesses (1) ----< business_photos (many)
users (1) ----< applications (many) [reviewed_by]
```

## 🎯 Data Constraints

### Business Rules
- **Unique slugs**: All businesses and categories must have unique slugs
- **Valid ratings**: Reviews must have ratings between 1-5
- **Required fields**: Name, address, and category are required for businesses
- **Email validation**: All email fields should be validated at application level
- **Phone format**: Phone numbers should follow consistent formatting

### Referential Integrity
- **Cascade deletes**: Business photos and reviews are deleted when business is deleted
- **Foreign key constraints**: All references maintain data integrity
- **Soft deletes**: Businesses use `is_active` flag instead of hard deletes

## 🔧 Maintenance Queries

### Update Business Rating
```sql
UPDATE app.businesses 
SET 
  average_rating = (
    SELECT AVG(rating)::DECIMAL(3,2) 
    FROM app.reviews 
    WHERE business_id = businesses.id AND is_approved = true
  ),
  total_reviews = (
    SELECT COUNT(*) 
    FROM app.reviews 
    WHERE business_id = businesses.id AND is_approved = true
  )
WHERE id = ?;
```

### Clean Up Orphaned Photos
```sql
DELETE FROM app.business_photos 
WHERE business_id NOT IN (SELECT id FROM app.businesses);
```

### Archive Old Applications
```sql
UPDATE app.applications 
SET status = 'archived' 
WHERE status IN ('approved', 'rejected') 
  AND reviewed_at < NOW() - INTERVAL '1 year';
```

---

**Created**: 2025-01-08  
**Last Updated**: 2025-01-08  
**Schema Version**: 1.0  
**Maintainer**: Documentation Specialist Agent
