#!/usr/bin/env node

/**
 * Test Admin Access Script
 * 
 * This script tests admin <NAME_EMAIL> by:
 * 1. Simulating login to get a token
 * 2. Testing admin status endpoint
 * 3. Testing admin-only endpoints
 * 
 * Usage: node scripts/test-admin-access.js
 */

import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Mock Firebase <NAME_EMAIL>
function createMockAdminToken() {
  // In a real scenario, this would be a proper Firebase JWT token
  // For testing with emulator, we'll simulate the token verification
  const mockToken = {
    uid: 'adminTestUser123',
    email: '<EMAIL>',
    admin: true,
    roles: ['admin'],
    permissions: ['admin.access', 'admin.businesses', 'admin.applications', 'admin.reviews']
  };
  
  // Base64 encode the mock token (simplified for testing)
  return Buffer.from(JSON.stringify(mockToken)).toString('base64');
}

// Test admin status endpoint
async function testAdminStatus() {
  try {
    const token = createMockAdminToken();
    const apiUrl = process.env.API_URL || 'http://localhost:5500';
    
    console.log('🧪 Testing admin status endpoint...');
    console.log(`📍 API URL: ${apiUrl}/api/v1/protected/admin-status`);
    console.log(`🔑 Using mock token for: <EMAIL>`);
    
    const response = await fetch(`${apiUrl}/api/v1/protected/admin-status`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`📊 Response Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Admin status response:', JSON.stringify(data, null, 2));
      return data;
    } else {
      const errorText = await response.text();
      console.log('❌ Admin status failed:', errorText);
      return null;
    }
  } catch (error) {
    console.error('❌ Error testing admin status:', error.message);
    return null;
  }
}

// Test admin-only endpoints
async function testAdminEndpoints() {
  try {
    const token = createMockAdminToken();
    const apiUrl = process.env.API_URL || 'http://localhost:5500';
    
    const adminEndpoints = [
      '/api/v1/admin/users',
      '/api/v1/admin/businesses',
      '/api/v1/admin/applications',
      '/api/v1/admin/stats'
    ];
    
    console.log('\n🧪 Testing admin-only endpoints...');
    
    for (const endpoint of adminEndpoints) {
      try {
        console.log(`\n📍 Testing: ${apiUrl}${endpoint}`);
        
        const response = await fetch(`${apiUrl}${endpoint}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`📊 Response Status: ${response.status}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Success: ${endpoint}`);
          console.log(`📄 Response preview: ${JSON.stringify(data).substring(0, 100)}...`);
        } else {
          const errorText = await response.text();
          console.log(`❌ Failed: ${endpoint} - ${errorText}`);
        }
      } catch (error) {
        console.log(`❌ Error testing ${endpoint}:`, error.message);
      }
    }
  } catch (error) {
    console.error('❌ Error testing admin endpoints:', error.message);
  }
}

// Check Firebase emulator accounts
function checkFirebaseAccounts() {
  try {
    const accountsPath = join(projectRoot, 'data', 'firebase-emulator', 'auth_export', 'accounts.json');
    console.log('🔍 Checking Firebase emulator accounts...');
    console.log(`📁 Accounts file: ${accountsPath}`);
    
    const accountsData = JSON.parse(readFileSync(accountsPath, 'utf8'));
    const adminUser = accountsData.users.find(u => u.email === '<EMAIL>');
    
    if (adminUser) {
      console.log('✅ <EMAIL> found in Firebase emulator');
      console.log('🔑 Custom claims:', JSON.stringify(adminUser.customClaims || {}, null, 2));
      return true;
    } else {
      console.log('❌ <EMAIL> not found in Firebase emulator');
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking Firebase accounts:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Admin Access <NAME_EMAIL>\n');
  console.log('=' .repeat(60));
  
  // Check Firebase accounts first
  const accountExists = checkFirebaseAccounts();
  
  if (!accountExists) {
    console.log('\n❌ Cannot proceed with tests - <EMAIL> not found in Firebase emulator');
    process.exit(1);
  }
  
  console.log('\n' + '=' .repeat(60));
  
  // Test admin status
  const adminStatus = await testAdminStatus();
  
  if (!adminStatus) {
    console.log('\n❌ Admin status test failed - check if server is running');
    console.log('💡 Start the server with: npm run dev');
    process.exit(1);
  }
  
  // Test admin endpoints
  await testAdminEndpoints();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🎉 Admin access tests completed!');
  console.log('\n📋 Summary:');
  console.log('✅ <EMAIL> has admin custom claims in Firebase emulator');
  console.log('✅ Admin status endpoint accessible');
  console.log('📝 Check individual endpoint results above');
  console.log('\n💡 Next steps:');
  console.log('1. <NAME_EMAIL> in the UI');
  console.log('2. Verify admin dashboard is accessible');
  console.log('3. Test admin features like business management');
}

// Run the tests
runTests().catch(console.error);
