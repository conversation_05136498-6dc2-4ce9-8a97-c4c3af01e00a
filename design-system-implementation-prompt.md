# Design System Implementation Prompt

## 🎯 Objective
Update the Volo App Template (shop-bze) to fully implement and utilize the existing design system defined in `/my-app/design/desgn-system.md`. Ensure consistent theming, typography, spacing, and component styling across the entire application.

## 📋 Current State Analysis

### ✅ What's Already Working
- **ShadCN/UI Foundation**: Components.json configured with Tailwind CSS v4 and ShadCN components
- **Design System File**: Comprehensive design tokens defined in `/my-app/design/desgn-system.md`
- **Theme Provider**: Dark/light theme switching implemented
- **CSS Variables**: Basic color tokens already in `/my-app/ui/src/index.css`
- **Component Library**: Extensive UI components in `/my-app/ui/src/components/ui/`

### ⚠️ Gaps to Address
1. **Color Token Mismatch**: Current `index.css` uses OKLCH values, design system uses HEX
2. **Missing Typography System**: Font families and size tokens not implemented
3. **Incomplete Spacing System**: Design system spacing tokens not utilized
4. **Shadow System**: Design system shadows not integrated
5. **Component Inconsistency**: UI components may not follow design system patterns

## 🔧 Implementation Tasks

### Phase 1: Core Design Token Integration

#### 1.1 Update CSS Variables in `index.css`
- Replace current color tokens with design system HEX values
- Add typography tokens (font families, sizes, line heights)
- Implement spacing system variables
- Add shadow system variables
- Ensure both light and dark themes are properly configured

#### 1.2 Configure Tailwind CSS
- Update `tailwind.config.js` to use design system tokens
- Configure custom font families (DM Serif Display, Roboto Mono)
- Set up spacing scale using design system values
- Configure shadow utilities

### Phase 2: Typography Implementation

#### 2.1 Font Integration
- Add Google Fonts imports for DM Serif Display and Roboto Mono
- Configure font-family utilities in Tailwind
- Create typography utility classes

#### 2.2 Text Scale System
- Implement responsive typography scale
- Create semantic heading classes (h1-h5)
- Add body text and caption utilities

### Phase 3: Component System Audit & Update

#### 3.1 UI Component Review
Audit and update these components to use design system tokens:
- `/my-app/ui/src/components/ui/button.tsx`
- `/my-app/ui/src/components/ui/input.tsx`
- `/my-app/ui/src/components/ui/card.tsx`
- `/my-app/ui/src/components/ui/badge.tsx`
- All other UI components in the `/ui` folder

#### 3.2 Application Component Updates
Update application-specific components to use design system:
- Navigation components (`navbar.tsx`, `appSidebar.tsx`)
- Business components (BusinessCard, BusinessProfile, etc.)
- Homepage components (HeroSection, CategoryHighlights, etc.)
- Search components (SearchBar, FilterPanel, etc.)

### Phase 4: Layout & Spacing Consistency

#### 4.1 Spacing System Implementation
- Replace hardcoded spacing with design system tokens
- Ensure consistent padding/margin across components
- Update container and layout spacing

#### 4.2 Grid & Layout Updates
- Implement consistent grid systems
- Update responsive breakpoints if needed
- Ensure proper spacing rhythm (8pt grid)

## 🎨 Design System Reference

### Color Palette
```css
/* Primary Brand Colors */
--primary: #6e56cf (Light) / #a48fff (Dark)
--secondary: #e4dfff (Light) / #2d2b55 (Dark)
--accent: #d8e6ff (Light) / #303060 (Dark)

/* Semantic Colors */
--destructive: #ff5470
--background: #f5f5ff (Light) / #0f0f1a (Dark)
--foreground: #2a2a4a (Light) / #e2e2f5 (Dark)
```

### Typography Scale
```css
--font-xs: 12px/16px     /* Labels/Captions */
--font-sm: 14px/20px     /* UI Labels */
--font-base: 16px/24px   /* Body Text */
--font-lg: 20px/32px     /* H5 */
--font-xl: 24px/40px     /* H4 */
--font-2xl: 32px/48px    /* H3 */
--font-3xl: 40px/56px    /* H2 */
--font-4xl: 48px/64px    /* H1/Hero */
```

### Spacing System (8pt Grid)
```css
--space-1: 8px   --space-6: 48px
--space-2: 16px  --space-7: 56px
--space-3: 24px  --space-8: 64px
--space-4: 32px  --space-9: 72px
--space-5: 40px  --space-10: 80px
```

## 🔍 Quality Assurance Checklist

### Visual Consistency
- [ ] All components use design system colors
- [ ] Typography follows established scale
- [ ] Spacing follows 8pt grid system
- [ ] Shadows are consistent across components

### Technical Implementation
- [ ] CSS variables properly defined and used
- [ ] Tailwind config reflects design system
- [ ] No hardcoded colors/spacing in components
- [ ] Dark/light themes work correctly

### Component Standards
- [ ] All UI components follow design system patterns
- [ ] Consistent hover/focus states
- [ ] Proper accessibility contrast ratios
- [ ] Responsive behavior maintained

## 📁 Files to Modify

### Core Configuration
- `/my-app/ui/src/index.css` - Update CSS variables
- `/my-app/ui/tailwind.config.js` - Configure design tokens
- `/my-app/ui/src/App.tsx` - Ensure theme provider setup

### UI Components (Priority Order)
1. `/my-app/ui/src/components/ui/button.tsx`
2. `/my-app/ui/src/components/ui/input.tsx`
3. `/my-app/ui/src/components/ui/card.tsx`
4. `/my-app/ui/src/components/ui/badge.tsx`
5. All remaining `/ui` components

### Application Components
- Navigation: `navbar.tsx`, `appSidebar.tsx`
- Business: All components in `/business` folder
- Homepage: All components in `/homepage` folder
- Search: All components in `/search` folder

## 🚀 Expected Outcomes

### User Experience
- Consistent visual language across the application
- Improved readability with proper typography
- Better accessibility with proper contrast ratios
- Smooth dark/light theme transitions

### Developer Experience
- Clear design token system for future development
- Consistent component patterns
- Easier maintenance and updates
- Better design-development handoff

## 📝 Implementation Notes

### Approach
1. Start with core CSS variable updates
2. Update Tailwind configuration
3. Systematically update components (UI first, then application)
4. Test theme switching functionality
5. Validate responsive behavior

### Considerations
- Maintain existing functionality while updating styling
- Ensure backward compatibility with current component APIs
- Test all interactive states (hover, focus, active)
- Validate accessibility standards are maintained

### Testing Strategy
- Visual regression testing for component changes
- Theme switching validation
- Responsive design verification
- Accessibility audit post-implementation

---

**Priority**: High
**Estimated Effort**: 2-3 development sessions
**Dependencies**: None (all design tokens already defined)
**Risk Level**: Low (styling updates only, no functional changes)
